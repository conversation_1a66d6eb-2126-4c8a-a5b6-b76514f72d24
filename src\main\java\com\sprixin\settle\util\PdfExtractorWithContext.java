package com.sprixin.settle.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import technology.tabula.*;
import technology.tabula.detectors.DetectionAlgorithm;
import technology.tabula.detectors.NurminenDetectionAlgorithm;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class PdfExtractorWithContext {

   

    /**
     * 一个简单的包装类，用于同时存储表格的上下文信息和其数据。
     */
    public static class TableWithContext {
        private final String contextText;
        private final List<List<String>> tableData;

        public TableWithContext(String contextText, List<List<String>> tableData) {
            this.contextText = contextText;
            this.tableData = tableData;
        }

        public String getContextText() {
            return contextText;
        }

        public List<List<String>> getTableData() {
            return tableData;
        }
    }

    public static String  getPdfValue(PDDocument document) {
       
        List<TableWithContext> allTablesWithContext = new ArrayList<>();

        try  {
            ObjectExtractor oe = new ObjectExtractor(document);
            PageIterator pages = oe.extract();
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();
            DetectionAlgorithm detector = new NurminenDetectionAlgorithm();

            System.out.println("开始处理PDF文件 (提取表格及其上下文)...");

            while (pages.hasNext()) {
                Page page = pages.next();
                System.out.println("正在处理第 " + page.getPageNumber() + " 页...");

                // 1. 检测页面上的所有表格区域
                List<Rectangle> tableAreas = detector.detect(page);

                if (tableAreas.isEmpty()) {
                    System.out.println("  - 在第 " + page.getPageNumber() + " 页未检测到表格。");
                    continue;
                }

                // 2. 关键步骤：按垂直位置（从上到下）对表格区域进行排序
                tableAreas.sort(Comparator.comparing(Rectangle::getTop));
                System.out.println("  - 在第 " + page.getPageNumber() + " 页检测并排序了 " + tableAreas.size() + " 个表格区域。");

                float lastY = 0; // 追踪当前处理到的页面垂直位置

                // 3. 遍历排序后的表格区域，提取上下文和表格
                for (Rectangle tableArea : tableAreas) {
                    // 3.1 定义当前表格上方的上下文区域
                    Rectangle contextArea = new Rectangle(lastY, 0, (float) page.getWidth(), tableArea.getTop() - lastY);

                    // 3.2 提取上下文文本
                    String contextText = extractTextFromArea(page, contextArea);

                    // 3.3 提取表格数据
                    Page tableRegion = page.getArea(tableArea);
                    List<Table> structuralTables = sea.extract(tableRegion);
                    List<List<String>> tableData = new ArrayList<>();
                    if (!structuralTables.isEmpty()) {
                        tableData = extractTextFromTableStructure(tableRegion, structuralTables.get(0));
                    }

                    // 3.4 将上下文和表格数据存入包装类
                    allTablesWithContext.add(new TableWithContext(contextText, tableData));

                    // 3.5 更新下一个上下文区域的起始位置
                    lastY = tableArea.getBottom();
                }
            }

            System.out.println("所有页面处理完毕，共提取到 " + allTablesWithContext.size() + " 个带上下文的表格。");

            if (!allTablesWithContext.isEmpty()) {
                return  writeCsvWithContext(allTablesWithContext);
                
            } else {
                System.out.println("未能在PDF中提取到任何内容。");
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static List<List<String>> extractTextFromTableStructure(Page tablePage, Table structuralTable) {
        // ... (此方法与上一版本完全相同)
        List<List<String>> tableData = new ArrayList<>();
        for (List<RectangularTextContainer> row : structuralTable.getRows()) {
            List<String> rowData = new ArrayList<>();
            for (RectangularTextContainer cell : row) {
                Rectangle cellBounds = new Rectangle(cell.getTop(), cell.getLeft(), (float) cell.getWidth(), (float) cell.getHeight());
                List<TextElement> textElements = tablePage.getText(cellBounds);
                String cellText = textElements.stream().map(TextElement::getText).collect(Collectors.joining());
                rowData.add(cellText);
            }
            tableData.add(rowData);
        }
        return tableData;
    }

    /**
     * 从页面的指定矩形区域提取所有文本，并清理格式。
     */
    private static String extractTextFromArea(Page page, Rectangle area) {
        if (area.getHeight() <= 0 || area.getWidth() <= 0) {
            return "";
        }
        List<TextElement> textElements = page.getText(area);
        // 将文本元素连接起来，并清理：去除首尾空白，将多个连续的空白/换行符合并为一个空格
        return textElements.stream()
                .map(TextElement::getText)
                .collect(Collectors.joining(""))
                .replaceAll("\\s+", " ")
                .trim();
    }

    /**
     * 将包含上下文的表格数据写入CSV文件。
     */
    private static String writeCsvWithContext(List<TableWithContext> allTables) throws IOException {
        try (StringWriter sw = new StringWriter(); BufferedWriter writer = new BufferedWriter(sw)) {

            for (TableWithContext item : allTables) {
                // 写入上下文文本，如果存在的话
                String context = item.getContextText();
                if (context != null && !context.isEmpty()) {
                    // 将上下文作为单独一行写入，放在第一个单元格
                    writer.write(escapeCsv("__text__"));
                    writer.write(",");
                    writer.write(escapeCsv(context));
                    writer.newLine();
                }

                // 写入表格数据
                for (List<String> row : item.getTableData()) {
                    for (int i = 0; i < row.size(); i++) {
                        writer.write(escapeCsv(row.get(i)));
                        if (i < row.size() - 1) {
                            writer.write(",");
                        }
                    }
                    writer.newLine();
                }
                // 在每个“上下文+表格”块之后添加一个空行，以提高可读性
                writer.newLine();
            }
            return sw.toString();
        }
    }

    private static String escapeCsv(String text) {
        // ... (此方法与上一版本完全相同)
        if (text == null) {
            return "";
        }
        if (text.contains(",") || text.contains("\"") || text.contains("\n") || text.contains("\r")) {
            text = text.replace("\"", "\"\"");
            return "\"" + text + "\"";
        }
        return text;
    }
}