package com.sprixin.settle.util;
import org.apache.pdfbox.pdmodel.PDDocument;
import technology.tabula.*;
import technology.tabula.detectors.DetectionAlgorithm;
import technology.tabula.detectors.NurminenDetectionAlgorithm;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class PdfExtractorWithContext {

    


    /**
     * 包装类，允许表格数据为null，以处理文档末尾的遗留文本。
     */
    public static class TableWithContext {
        private final String contextText;
        private final List<List<String>> tableData; // Can be null

        public TableWithContext(String contextText, List<List<String>> tableData) {
            this.contextText = contextText;
            this.tableData = tableData;
        }

        public String getContextText() {
            return contextText;
        }

        public List<List<String>> getTableData() {
            return tableData;
        }
    }

    public static String getCsv(PDDocument document) {
        List<TableWithContext> allItems = new ArrayList<>();

        // 1. 关键：创建在页面之间传递状态的上下文缓冲区
        StringBuilder pendingContext = new StringBuilder();

        try {
            ObjectExtractor oe = new ObjectExtractor(document);
            PageIterator pages = oe.extract();
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();
            DetectionAlgorithm detector = new NurminenDetectionAlgorithm();

            System.out.println("开始处理PDF文件 (带跨页上下文处理)...");

            while (pages.hasNext()) {
                Page page = pages.next();
                System.out.println("正在处理第 " + page.getPageNumber() + " 页...");

                // 1. 检测页面上的所有表格区域
                List<Rectangle> tableAreas = detector.detect(page);
                tableAreas.sort(Comparator.comparing(Rectangle::getTop));

                // 2. 如果当前页面没有表格
                if (tableAreas.isEmpty()) {
                    System.out.println("  - 页面无表格，将整页文本加入上下文缓冲区。");
                    String pageText = extractTextFromArea(page, new Rectangle(0, 0, (float) page.getWidth(), (float) page.getHeight()));
                    if (!pageText.isEmpty()) {
                        pendingContext.append(pageText).append(" ");
                    }
                    continue; // 继续处理下一页
                }

                System.out.println("  - 在第 " + page.getPageNumber() + " 页检测并排序了 " + tableAreas.size() + " 个表格区域。");
                float lastY = 0;

                // 3. 遍历页面上的所有表格
                for (Rectangle tableArea : tableAreas) {
                    // 3.1 提取表格上方的文本，并加入缓冲区
                    Rectangle areaAboveTable = new Rectangle(lastY, 0, (float) page.getWidth(), tableArea.getTop() - lastY);
                    String textAbove = extractTextFromArea(page, areaAboveTable);
                    if (!textAbove.isEmpty()) {
                        pendingContext.append("__").append(textAbove).append("__");
                    }

                    // 3.2 提取表格数据
                    Page tableRegion = page.getArea(tableArea);
                    List<Table> structuralTables = sea.extract(tableRegion);
                    List<List<String>> tableData = new ArrayList<>();
                    if (!structuralTables.isEmpty()) {
                        tableData = extractTextFromTableStructure(tableRegion, structuralTables.get(0));
                    }

                    // 3.3 创建条目，清空缓冲区
                    allItems.add(new TableWithContext(pendingContext.toString().trim(), tableData));
                    pendingContext.setLength(0); // 清空缓冲区，为下一个条目做准备

                    // 3.4 更新Y坐标
                    lastY = tableArea.getBottom();
                }

                // 4. 关键：处理最后一个表格之后，到页面末尾的文本
                Rectangle areaBelowLastTable = new Rectangle(lastY, 0, (float) page.getWidth(), (float) (page.getHeight() - lastY));
                String textBelow = extractTextFromArea(page, areaBelowLastTable);
                if (!textBelow.isEmpty()) {
                    System.out.println("  - 发现页末文本，加入缓冲区带到下一页。");
                    pendingContext.append(textBelow).append(" ");
                }
            }

            // 5. 关键：处理所有页面结束后，缓冲区中可能遗留的文本
            if (pendingContext.length() > 0) {
                System.out.println("发现文档末尾的遗留文本。");
                allItems.add(new TableWithContext(pendingContext.toString().trim(), null));
            }

            System.out.println("所有页面处理完毕，共提取到 " + allItems.size() + " 个条目（表格或纯文本块）。");

            if (!allItems.isEmpty()) {
                return writeCsvWithContext(allItems);
            } else {
                System.out.println("未能在PDF中提取到任何内容。");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static List<List<String>> extractTextFromTableStructure(Page tablePage, Table structuralTable) {
        List<List<String>> tableData = new ArrayList<>();
        for (List<RectangularTextContainer> row : structuralTable.getRows()) {
            List<String> rowData = new ArrayList<>();
            for (RectangularTextContainer cell : row) {
                Rectangle cellBounds = new Rectangle(cell.getTop(), cell.getLeft(), (float) cell.getWidth(), (float) cell.getHeight());
                String cellText = tablePage.getText(cellBounds).stream().map(TextElement::getText).collect(Collectors.joining());
                rowData.add(cellText);
            }
            tableData.add(rowData);
        }
        return tableData;
    }

    /**
     * 从页面的指定矩形区域提取所有文本，并智能地处理换行。
     * 在视觉上的换行处插入换行符'\n'，同一行的文本元素用空格分隔。
     *
     * @param page the Page object to extract text from.
     * @param area the Rectangle area to extract text from.
     * @return A single string with original line breaks preserved as '\n'.
     */
    private static String extractTextFromArea(Page page, Rectangle area) {
        if (area.getHeight() <= 0 || area.getWidth() <= 0) {
            return "";
        }

        List<TextElement> textElements = page.getText(area);

        if (textElements.isEmpty()) {
            return "";
        }

        // 按阅读顺序（从上到下，从左到右）对文本元素排序，Tabula的默认排序通常就是这样，但显式排序更保险
        textElements.sort(Comparator.comparing(TextElement::getTop).thenComparing(TextElement::getLeft));

        StringBuilder sb = new StringBuilder();
        TextElement lastElement = null;

        for (TextElement currentElement : textElements) {
            if (lastElement != null) {
                // 检查垂直位置来判断是否换行
                // 如果当前元素的顶部在前一个元素的底部之上，或者Y坐标差异很小，则认为是同一行
                // 这里的 '2.0f' 是一个容差值，可以根据实际PDF的行距进行微调
                if (currentElement.getTop() > lastElement.getBottom() || Math.abs(currentElement.getTop() - lastElement.getTop()) > 2.0f) {
                    sb.append("__"); // 发生了换行，插入换行符
                }
            }
            sb.append(currentElement.getText());
            lastElement = currentElement;
        }

        // 最后清理一下，去除可能因算法产生的多余空白
        return sb.toString().trim().replaceAll(" +", " ");
    }

    /**
     * 更新后的CSV写入方法，能处理表格数据为null的情况。
     *
     * @return String
     */
    private static String writeCsvWithContext(List<TableWithContext> allItems) throws IOException {
        try (StringWriter stringWriter = new StringWriter();
             BufferedWriter writer = new BufferedWriter(stringWriter)) {

            for (TableWithContext item : allItems) {
                String context = item.getContextText();
                if (context != null && !context.isEmpty()) {
                    writer.newLine();
                    writer.write("__text__," + escapeCsv(context)); // 添加标记以区分
                    writer.newLine();
                }

                List<List<String>> tableData = item.getTableData();
                if (tableData != null) {
                    for (List<String> row : tableData) {
                        writer.write(row.stream().map(PdfExtractorWithContext::escapeCsv).collect(Collectors.joining(",")));
                        writer.newLine();
                    }
                }
            }
            writer.flush();
            return stringWriter.toString();
        }
    }

    private static String escapeCsv(String text) {
        if (text == null) {
            return "";
        }
        if (text.contains(",") || text.contains("\"") || text.contains("\n") || text.contains("\r")) {
            text = text.replace("\"", "\"\"");
            return "\"" + text + "\"";
        }
        return text;
    }
}