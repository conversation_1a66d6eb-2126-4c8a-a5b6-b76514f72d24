CREATE TABLE `trade_tj_all_settle_bill` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `subject_id` bigint(20) DEFAULT NULL COMMENT '市场主体ID',
  `month` varchar(7) DEFAULT NULL COMMENT '结算月份（YYYY-MM）',
  `online_electricity` double(38,6) DEFAULT NULL COMMENT '上网电量',
  `settlement_electricity` double(38,6) DEFAULT NULL COMMENT '结算电量',
  `contract_electricity` double(38,6) DEFAULT NULL COMMENT '合同电量',
  `deviation_electricity` double(38,6) DEFAULT NULL COMMENT '偏差电量',
  `total_fee` double(38,6) DEFAULT NULL COMMENT '结算电费',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_bill` (`province_id`,`subject_id`,`month`)
) ENGINE=InnoDB AUTO_INCREMENT=385 DEFAULT CHARSET=utf8 COMMENT='各省结算单主表';

/*Table structure for table `trade_tj_all_settle_bill_detail` */

DROP TABLE IF EXISTS `trade_tj_all_settle_bill_detail`;

CREATE TABLE `trade_tj_all_settle_bill_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `province_id` bigint(20) DEFAULT NULL COMMENT '省份ID',
  `subject_id` bigint(20) DEFAULT NULL COMMENT '市场主体ID',
  `month` varchar(7) DEFAULT NULL COMMENT '结算月份（YYYY-MM）',
  `code` varchar(32) DEFAULT NULL COMMENT '结算科目编码',
  `name` varchar(64) DEFAULT NULL COMMENT '结算科目名称',
  `planned_electricity` double(38,6) DEFAULT NULL COMMENT '交易计划电量',
  `settlement_electricity` double(38,6) DEFAULT NULL COMMENT '结算电量/容量',
  `settlement_price` double(38,6) DEFAULT NULL COMMENT '结算单价/均价',
  `settlement_fee` double(38,6) DEFAULT NULL COMMENT '结算电费',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_detail` (`code`,`province_id`,`subject_id`,`month`)
) ENGINE=InnoDB AUTO_INCREMENT=13659 DEFAULT CHARSET=utf8 COMMENT='各省结算单明细表';

/*Table structure for table `trade_tj_all_settle_item_label` */

DROP TABLE IF EXISTS `trade_tj_all_settle_item_label`;

CREATE TABLE `trade_tj_all_settle_item_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `province_id` bigint(20) NOT NULL,
  `code` varchar(32) NOT NULL,
  `name` varchar(128) NOT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNION_KEY` (`province_id`,`code`)
) ENGINE=InnoDB AUTO_INCREMENT=13302 DEFAULT CHARSET=utf8 COMMENT='分项标签表';

/*Table structure for table `trade_tj_all_settle_subject` */

DROP TABLE IF EXISTS `trade_tj_all_settle_subject`;

CREATE TABLE `trade_tj_all_settle_subject` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '市场主体Id',
  `name` varchar(128) NOT NULL COMMENT '市场主体名称',
  `user_id` bigint(20) NOT NULL COMMENT '用户Id',
  `province_id` bigint(20) NOT NULL COMMENT '省份Id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNION_KEY` (`user_id`,`province_id`,`name`)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8 COMMENT='市场主体表';