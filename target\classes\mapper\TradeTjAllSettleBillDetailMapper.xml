<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.settle.dao.TradeTjAllSettleBillDetailMapper">
    <resultMap id="BaseResultMap" type="com.sprixin.settle.entity.TradeTjAllSettleBillDetail">
        <id property="id" column="id" />
        <result property="provinceId" column="province_id" />
        <result property="subjectId" column="subject_id" />
        <result property="month" column="month" />
        <result property="code" column="code" />
        <result property="name" column="name" />
        <result property="plannedElectricity" column="planned_electricity" />
        <result property="settlementElectricity" column="settlement_electricity" />
        <result property="settlementPrice" column="settlement_price" />
        <result property="settlementFee" column="settlement_fee" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>



    <select id="getByProvinceSubjectMonthBatch" resultType="com.sprixin.settle.entity.TradeTjAllSettleBillDetail">
        SELECT * FROM trade_tj_all_settle_bill_detail
        WHERE province_id = #{provinceId}
        <if test="subjectIds != null and subjectIds.size() > 0">
            AND subject_id IN
            <foreach collection="subjectIds" item="sid" open="(" separator="," close=")">
                #{sid}
            </foreach>
        </if>
        <if test="months != null and months.size() > 0">
            AND month IN
            <foreach collection="months" item="m" open="(" separator="," close=")">
                #{m}
            </foreach>
        </if>
    </select>



    <insert id="replaceBatch" parameterType="java.util.List">
        REPLACE INTO trade_tj_all_settle_bill_detail (province_id, subject_id, month, code, name, planned_electricity, settlement_electricity, settlement_price, settlement_fee, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.provinceId}, #{item.subjectId}, #{item.month}, #{item.code}, #{item.name}, #{item.plannedElectricity}, #{item.settlementElectricity}, #{item.settlementPrice}, #{item.settlementFee}, NOW(), NOW())
        </foreach>
    </insert>

    <delete id="deleteBySubjectIds" parameterType="java.util.List">
        DELETE FROM trade_tj_all_settle_bill_detail
        WHERE subject_id IN
        <foreach collection="subjectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>