package com.sprixin.settle.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import technology.tabula.ObjectExtractor;
import technology.tabula.Page;
import technology.tabula.RectangularTextContainer;
import technology.tabula.Table;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TabulaQwenApiUtil3 {
    private static final Logger logger = LoggerFactory.getLogger(TabulaQwenApiUtil1.class);
    private static String API_URL;
    private static String API_KEY;
    private static String MODEL;

    @Value("${ai.model.url}")
    private String apiUrlProp;
    @Value("${ai.model.key}")
    private String apiKeyProp;
    @Value("${ai.model.name}")
    private String modelProp;

    @PostConstruct
    public void init() {
        API_URL = apiUrlProp;
        API_KEY = apiKeyProp;
        MODEL = modelProp;
    }

    /**
     * 主解析流程，采用三阶段混合模型：
     * 1. 全局识别 (省份+单元) -> 获取文档的“目录”和全局信息。
     * 2. 带上下文的分页提取 -> 逐页分析，并将数据归属到“目录”中的单元。
     * 3. Java端聚合 -> 将每页返回的、已归属的数据合并成最终结果。
     * @param pdfFile PDF文件
     * @return 包含所有结算实体数据的JSON数组字符串。
     */
     public static String parsePdf(File pdfFile) throws IOException {
        try (PDDocument document = WatermarkRemover.removeWatermark(pdfFile)) {
             String  fullText = PdfExtractorWithContext.getPdfValue(document);
            
            JSONObject documentStructure = identifyDocumentStructure(fullText);

            String province = documentStructure.getString("province");
            JSONArray identifiedUnits = documentStructure.getJSONArray("units");

            if (identifiedUnits == null || identifiedUnits.isEmpty()) {
                logger.warn("阶段一：未能从文档 {} 中识别出任何交易单元。", pdfFile.getName());
                return "[]";
            }

            // 状态机：当前交易单元
            String currentCompany = null;
            String currentTradingUnit = null;

            Map<String, JSONObject> finalResultsMap = new HashMap<>();

            int pageCount = document.getNumberOfPages();
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                logger.info("处理第{}页数据", pageNum);
                String pageText = extractPageText(document, pageNum);
                String pageTableData = extractPageTableData(extractor, pageNum);

                if (pageText.trim().isEmpty() && pageTableData.trim().isEmpty()) continue;

                // 1. 检测本页是否出现新的交易单元名
                for (Object unitObj : identifiedUnits) {
                    JSONObject unit = (JSONObject) unitObj;
                    String unitName = unit.getString("tradingUnitName");
                    if (pageText.contains(unitName) || pageTableData.contains(unitName)) {
                        currentCompany = unit.getString("company");
                        currentTradingUnit = unitName;
                        logger.info("检测到交易单元切换: {}-{}", currentCompany, currentTradingUnit);
                        break;
                    }
                }

                // 2. 调用大模型提取
                JSONArray pageDataArray = extractDataFromPageWithContext(pageText, pageTableData, identifiedUnits);

                // 3. Java层兜底修正归属
                if (currentCompany != null && currentTradingUnit != null) {
                    for (Object item : pageDataArray) {
                        JSONObject unitDataOnPage = (JSONObject) item;
                        unitDataOnPage.put("company", currentCompany);
                        unitDataOnPage.put("tradingUnitName", currentTradingUnit);
                    }
                }

                // 4. 聚合
                for (Object item : pageDataArray) {
                    JSONObject unitDataOnPage = (JSONObject) item;
                    String company = unitDataOnPage.getString("company");
                    String tradingUnitName = unitDataOnPage.getString("tradingUnitName");

                    if (company == null || tradingUnitName == null) continue;

                    String unitKey = company + "-" + tradingUnitName;

                    JSONObject masterUnitObject = finalResultsMap.get(unitKey);
                    if (masterUnitObject == null) {
                        masterUnitObject = new JSONObject();
                        masterUnitObject.put("company", company);
                        masterUnitObject.put("tradingUnitName", tradingUnitName);

                        JSONObject header = new JSONObject();
                        header.put("province", province);
                        masterUnitObject.put("header", header);

                        masterUnitObject.put("details", new JSONArray());
                        finalResultsMap.put(unitKey, masterUnitObject);
                    }

                    JSONObject pageHeader = unitDataOnPage.getJSONObject("header");
                    if (pageHeader != null) {
                        for (Map.Entry<String, Object> entry : pageHeader.entrySet()) {
                            if (entry.getValue() != null && !"province".equalsIgnoreCase(entry.getKey())) {
                                masterUnitObject.getJSONObject("header").put(entry.getKey(), entry.getValue());
                            }
                        }
                    }

                    JSONArray pageDetails = unitDataOnPage.getJSONArray("details");
                    if (pageDetails != null && !pageDetails.isEmpty()) {
                        masterUnitObject.getJSONArray("details").addAll(pageDetails);
                    }
                }
            }

            JSONArray finalArray = new JSONArray();
            for (JSONObject obj : finalResultsMap.values()) {
                finalArray.add(obj);
            }

            return finalArray.toJSONString();
        }
    }

    /**
     * [第一阶段] 调用大模型识别文档的整体结构（省份和所有交易单元）。
     */
    private static JSONObject identifyDocumentStructure(String fullText) throws IOException {
        String[] strs = fullText.split("\n");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strs.length; i++) {
            if (strs[i].startsWith("__text__")) {
                sb.append(strs[i].substring(9)).append("\n");
            }
        }   
        fullText = sb.toString();
        String prompt = buildStructureIdentificationPrompt(fullText);
        String apiResponse = callQwenApi(prompt);
        try {
            return JSONObject.parseObject(apiResponse);
        } catch (Exception e) {
            logger.error("解析第一阶段API响应失败，响应内容: {}", apiResponse, e);
            JSONObject emptyStructure = new JSONObject();
            emptyStructure.put("province", null);
            emptyStructure.put("units", new JSONArray());
            return emptyStructure;
        }
    }

    /**
     * [第二阶段] 调用大模型为已识别的单元列表从单页中提取详细数据。
     */
    private static JSONArray extractDataFromPageWithContext(String pageText, String pageTableData, JSONArray allUnits) throws IOException {
        String prompt = buildContextualPageExtractionPrompt(pageText, pageTableData, allUnits);
        logger.info("pageText:" + pageText);
        logger.info("pageTableData:" + pageTableData);
        String apiResponse = callQwenApi(prompt);
         logger.info("apiResponse:" + apiResponse);
        try {
            return JSONArray.parseArray(apiResponse);
        } catch (Exception e) {
            logger.error("解析第二阶段API响应失败，响应内容: {}", apiResponse, e);
            return new JSONArray();
        }
    }

  /**
     * [提示词构建] 为第一阶段的“结构识别”任务构建Prompt (V3 - 最终稳定版)。
     * 此版本增加了强制的空值处理和结构完整性约束，以确保 province 字段总是存在。
     */
    private static String buildStructureIdentificationPrompt(String fullText) {
        return "你是一个文档结构分析专家。你的任务是从下面的文本中识别出【省份】以及所有的【市场主体公司】及其下属的【交易单元】。\n\n" +
               "--- 文档纯文本 ---\n" + fullText + "\n\n" +
               "--- 分析规则 ---\n" +
               "1. **省份 (province)**: 关键信息。在文档大标题中寻找地理名称，例如 '山西电力交易中心' 中的 '山西'。\n" +
               "2. **市场主体 (company)**: 例如 '交城县明科光电科技有限公司'。\n" +
               "3. **交易单元 (tradingUnitName)**: 通常是带有 '一期', '二期' 等字样的名称。如果没有明确的'期'，则使用公司名作为交易单元名。\n\n" +
               "--- 关键输出规则 (必须严格遵守！) ---\n" +
               "1. **结构必须完整**: 你的返回结果**必须是一个单独的JSON对象**，包含 `province` 和 `units` 两个顶级键。绝对不能省略任何一个键。\n" +
               "2. **省份必须处理**: 如果你分析后找不到明确的省份，`province` 字段的**值必须是 `null`**，一定要在JSON对象中添加`province`属性。这是强制性要求。\n\n" +
               "--- 输出格式示例 ---\n" +
               "```json\n" +
               "{\n" +
               "  \"province\": \"山西\",\n" +
               "  \"units\": [\n" +
               "    {\n" +
               "      \"company\": \"公司全称\",\n" +
               "      \"tradingUnitName\": \"交易单元一名称\"\n" +
               "    }\n" +
               "  ]\n" +
               "}\n" +
               "```";
    }


    /**
     * [提示词构建] 为第二阶段的“带上下文分页提取”任务构建Prompt (V6 - 最终格式统一版)。
     */
    private static String buildContextualPageExtractionPrompt(String pageText, String pageTableData, JSONArray allUnits) {
        return "你是一位顶级的、极其精确的财务数据提取专家。你的任务是分析【单页】结算单内容，并将其数据正确归属到指定的交易单元中，同时严格统一输出格式。\n\n" +
               "--- 全局上下文 (文档中所有的交易单元) ---\n" + allUnits.toJSONString() + "\n\n" +
               "--- 当前页文本 ---\n" + pageText + "\n\n" +
               "--- 当前页表格数据 ---\n" + pageTableData + "\n\n" +
               "--- 提取指令 ---\n" +
               "1. **数据归属 (核心任务)**: 分析当前页的内容，判断出现的摘要信息和明细条目分别属于【全局上下文】列表中的哪一个交易单元。**注意：本页可能只包含一个单元的数据，也可能包含上一个单元的结尾和下一个单元的开头。**\n" +
               "2. **提取内容**: 提取你找到的所有`header`（摘要）和`details`（明细）信息。\n\n" +
               "--- 关键规则 1：统一字段映射 (MANDATORY!) ---\n" +
               "你必须将PDF中看到的各种列名，强制映射到以下唯一的、固定的输出字段名。这是最重要的规则！\n" +
               "   - **`name` (名称/描述)**: 无论源列名是'结算科目', '结算项目', 'settlementItem', 'settlementCategory', 'description', 还是'项目说明', 输出时**必须**使用 `name` 字段。\n" +
               "   - **`plannedElectricity` (计划电量)**: 无论源列名是'交易计划电量', 'plannedVolume', 'transactionPlanVolume', 输出时**必须**使用 `plannedElectricity` 字段。\n" +
               "   - **`settlementElectricity` (结算电量)**: 无论源列名是'结算电量/容量', '结算电量', 'settlementVolume', 'quantity', 输出时**必须**使用 `settlementElectricity` 字段。\n" +
               "   - **`settlementPrice` (结算单价)**: 无论源列名是'结算电价/均价', 'settlementPrice', 'price', 输出时**必须**使用 `settlementPrice` 字段。\n" +
               "   - **`settlementFee` (结算费用)**: 无论源列名是'结算电费', 'settlementFee', 'fee', 输出时**必须**使用 `settlementFee` 字段。\n\n" +
               "--- 关键规则 2：列对齐与空值 ---\n" +
               "   - **绝对禁止列数据漂移**。如果某行在某一列没有值，对应JSON字段必须为 `null`。\n" +
               "   - `settlementPrice`(单价)通常远小于`settlementFee`(总费用)。用此规则检查对齐是否正确。\n\n" +
               "--- 输出格式 (必须严格遵守以下字段名和结构) ---\n" +
               "请严格以JSON数组格式返回。数组中的每个对象代表一个在本页上找到了数据的交易单元。如果本页没有找到任何单元的数据，则返回空数组`[]`。\n" +
               "```json\n" +
               "[\n" +
               "  {\n" +
               "    \"company\": \"在本页找到数据的公司名\",\n" +
               "    \"tradingUnitName\": \"在本页找到数据的交易单元名\",\n" +
               "    \"header\": {\n" +
               "      \"province\": \"省份或null\",\n" +
               "      \"settlementDate\": \"YYYY-MM或null\",\n" +
               "      \"period\": \"期间或null\",\n" +
               "      \"onlineElectricity\": 数字或null,\n" +
               "      \"settlementElectricity\": 数字或null,\n" +
               "      \"contractElectricity\": 数字或null,\n" +
               "      \"deviationElectricity\": 数字或null,\n" +
               "      \"totalFee\": 数字或null\n" +
               "    },\n" +
               "    \"details\": [\n" +
               "      {\n" +
               "        \"code\": \"编码\",\n" +
               "        \"name\": \"名称或描述\",\n" +
               "        \"plannedElectricity\": 数字或null,\n" +
               "        \"settlementElectricity\": 数字或null,\n" +
               "        \"settlementPrice\": 数字或null,\n" +
               "        \"settlementFee\": 数字或null,\n" +
               "        \"remark\": \"备注信息或null\"\n" +
               "      }\n" +
               "    ]\n" +
               "  }\n" +
               "]\n" +
               "```";
    }

    private static String extractFullText(PDDocument document) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setSortByPosition(true);
        return stripper.getText(document);
    }
    
    private static String extractPageText(PDDocument document, int pageNum) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setSortByPosition(true);
        stripper.setStartPage(pageNum);
        stripper.setEndPage(pageNum);
        return stripper.getText(document);
    }

    private static String extractPageTableData(ObjectExtractor extractor, int pageNum) {
        StringBuilder tableText = new StringBuilder();
        try {
            Page page = extractor.extract(pageNum);
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();
            List<Table> tables = sea.extract(page);

            if (tables.isEmpty()) return "";

            for (Table table : tables) {
                List<List<RectangularTextContainer>> rows = table.getRows();
                if (rows.isEmpty()) continue;

                int maxColumns = 0;
                for(List<RectangularTextContainer> row : rows){
                    if(row.size() > maxColumns){
                        maxColumns = row.size();
                    }
                }
                if (maxColumns < 2) continue;

                for (List<RectangularTextContainer> row : rows) {
                    String firstCellText = row.isEmpty() ? "" : row.get(0).getText();
                    if (firstCellText.contains("合计") || firstCellText.contains("总计") || firstCellText.contains("小计")) {
                        continue;
                    }
                    for (int i = 0; i < maxColumns; i++) {
                        String cellText = (i < row.size()) ? normalizeCellText(row.get(i).getText()) : "[NULL]";
                        tableText.append(cellText).append("\t");
                    }
                    tableText.append("\n");
                }
                tableText.append("---TABLE_SEPARATOR---\n");
            }
        } catch (Exception e) {
             logger.error("提取页面 " + pageNum + " 的表格数据时出错: ", e);
             return "";
        }
        return tableText.toString();
    }

    private static String normalizeCellText(String text) {
        if (text == null || text.trim().isEmpty()) return "[NULL]";
        String cleaned = text.replaceAll("[\\s\\u00A0]+", " ").trim();
        return cleaned.matches("^-?[\\d,]+(\\.\\d+)?$") ? cleaned.replace(",", "") : cleaned;
    }

    private static String callQwenApi(String prompt) throws IOException {
        JSONArray messages = new JSONArray();
        JSONObject userMsg = new JSONObject();
        userMsg.put("role", "user");
        userMsg.put("content", prompt);
        messages.add(userMsg);

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL);
        requestBody.put("messages", messages);
        requestBody.put("temperature", 0.01);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", "Bearer " + API_KEY);
        headers.put("Content-Type", "application/json");

        try {
            HttpResponse response = HttpRequest.post(API_URL + "/v1/chat/completions")
                    .headerMap(headers, true)
                    .body(requestBody.toJSONString())
                    .timeout(120000)
                    .execute();

            if (!response.isOk()) {
                logger.error("API调用失败: {} - {}", response.getStatus(), response.body());
                throw new IOException("API调用失败: " + response.getStatus());
            }
            return parseApiResponse(response.body());
        } catch (Exception e) {
            logger.error("API调用异常: ", e);
            throw new IOException("API调用异常: " + e.getMessage());
        }
    }

    private static String parseApiResponse(String apiResponse) {
        try {
            JSONObject responseJson = JSONObject.parseObject(apiResponse);
            if (responseJson == null) return "[]";

            JSONArray choices = responseJson.getJSONArray("choices");
            if (choices != null && !choices.isEmpty()) {
                JSONObject message = choices.getJSONObject(0).getJSONObject("message");
                if (message != null) {
                    return message.getString("content");
                }
            }
        } catch (Exception e) {
            logger.error("解析API响应JSON时出错, 响应内容: {}", apiResponse, e);
        }
        return "[]";
    }

   
}