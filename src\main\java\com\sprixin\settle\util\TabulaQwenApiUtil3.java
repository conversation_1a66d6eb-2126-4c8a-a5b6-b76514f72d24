package com.sprixin.settle.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class TabulaQwenApiUtil3 {
    private static final Logger logger = LoggerFactory.getLogger(TabulaQwenApiUtil1.class);
    private static String API_URL;
    private static String API_KEY;
    private static String MODEL;

    @Value("${ai.model.url}")
    private String apiUrlProp;
    @Value("${ai.model.key}")
    private String apiKeyProp;
    @Value("${ai.model.name}")
    private String modelProp;

    @PostConstruct
    public void init() {
        API_URL = apiUrlProp;
        API_KEY = apiKeyProp;
        MODEL = modelProp;
    }

    /**
     * 主解析流程，采用三阶段混合模型：
     * 1. 全局识别 (省份+单元) -> 获取文档的“目录”和全局信息。
     * 2. 带上下文的分页提取 -> 逐页分析，并将数据归属到“目录”中的单元。
     * 3. Java端聚合 -> 将每页返回的、已归属的数据合并成最终结果。
     * @param pdfFile PDF文件
     * @return 包含所有结算实体数据的JSON数组字符串。
     */
     public static String parsePdf(File pdfFile) throws IOException {
        try (PDDocument document = WatermarkRemover.removeWatermark(pdfFile)) {
            String fullText = PdfExtractorWithContext.getCsv(document);
            logger.info("fullText:" + fullText);
            // 第一阶段：识别文档结构
            JSONObject documentStructure = identifyDocumentStructure(fullText);
            String province = documentStructure.getString("province");
            JSONArray identifiedUnits = documentStructure.getJSONArray("units");

            if (identifiedUnits == null || identifiedUnits.isEmpty()) {
                logger.warn("阶段一：未能从文档 {} 中识别出任何交易单元。", pdfFile.getName());
                return "[]";
            }

            // 第二阶段：全文档数据提取
            logger.info("开始全文档数据提取，识别到 {} 个交易单元", identifiedUnits.toString());

            // 调用大模型一次性提取所有数据
            JSONArray extractedData = extractDataFromFullDocument(fullText, identifiedUnits, province);

            if (extractedData == null || extractedData.isEmpty()) {
                logger.warn("阶段二：未能从文档 {} 中提取出任何数据。", pdfFile.getName());
                return "[]";
            }

            logger.info("成功提取到 {} 个交易单元的数据", extractedData.size());
            return extractedData.toJSONString();
        }
    }

    /**
     * [第一阶段] 调用大模型识别文档的整体结构（省份和所有交易单元）。
     */
    private static JSONObject identifyDocumentStructure(String fullText) throws IOException {
        String[] strs = fullText.split("\n");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strs.length; i++) {
            if (strs[i].startsWith("__text__")) {
                sb.append(strs[i].substring(9)).append("\n");
            }
        }   
        fullText = sb.toString();
        String prompt = buildStructureIdentificationPrompt(fullText);
        String apiResponse = callQwenApi(prompt);
        try {
            return JSONObject.parseObject(apiResponse);
        } catch (Exception e) {
            logger.error("解析第一阶段API响应失败，响应内容: {}", apiResponse, e);
            JSONObject emptyStructure = new JSONObject();
            emptyStructure.put("province", null);
            emptyStructure.put("units", new JSONArray());
            return emptyStructure;
        }
    }

    /**
     * [第二阶段] 调用大模型从整个文档中提取所有数据。
     */
    private static JSONArray extractDataFromFullDocument(String fullText, JSONArray allUnits, String province) throws IOException {
        String prompt = buildFullDocumentExtractionPrompt(fullText, allUnits, province);
        logger.info("开始调用大模型进行全文档数据提取");
        String apiResponse = callQwenApi(prompt);
        logger.info("全文档提取API响应: {}", apiResponse);
        try {
            return JSONArray.parseArray(apiResponse);
        } catch (Exception e) {
            logger.error("解析全文档提取API响应失败，响应内容: {}", apiResponse, e);
            return new JSONArray();
        }
    }

    
    /**
     * [提示词构建] 为第二阶段的"全文档数据提取"任务构建Prompt。
     */
    private static String buildFullDocumentExtractionPrompt(String fullText, JSONArray allUnits, String province) {
        return "你是一位顶级的、极其精确的财务数据提取专家。你的任务是从整个结算单文档中提取所有交易单元的完整数据，并严格按照指定格式输出。\n\n" +
               "--- 全局上下文 (文档中所有的交易单元) ---\n" + allUnits.toJSONString() + "\n\n" +
               "--- 文档省份信息 ---\n" + (province != null ? province : "未识别") + "\n\n" +
               "--- 完整文档文本 ---\n" + fullText + "\n\n" +
               "--- 提取指令 ---\n" +
               "1. **全文档分析**: 分析整个文档的内容，识别每个交易单元的摘要信息和明细数据。\n" +
               "2. **数据归属**: 将找到的所有数据正确归属到对应的交易单元。\n" +
               "3. **完整提取**: 确保提取每个交易单元的完整header和details信息。\n\n" +
               "--- 关键规则 1：统一字段映射 (MANDATORY!) ---\n" +
               "你必须将看到的各种列名，强制映射到以下唯一的、固定的输出字段名：\n" +
               "   - **`name` (名称/描述)**: 无论源列名是什么，输出时**必须**使用 `name` 字段。\n" +
               "   - **`plannedElectricity` (计划电量)**: 无论源列名是什么，输出时**必须**使用 `plannedElectricity` 字段。\n" +
               "   - **`settlementElectricity` (结算电量)**: 无论源列名是什么，输出时**必须**使用 `settlementElectricity` 字段。\n" +
               "   - **`settlementPrice` (结算单价)**: 无论源列名是什么，输出时**必须**使用 `settlementPrice` 字段。\n" +
               "   - **`settlementFee` (结算费用)**: 无论源列名是什么，输出时**必须**使用 `settlementFee` 字段。\n\n" +
               "--- 关键规则 2：数据完整性 ---\n" +
               "   - 确保每个交易单元的header包含完整的摘要信息。\n" +
               "   - 确保每个交易单元的details包含所有明细条目。\n" +
               "   - 如果某个字段没有值，设置为 `null`。\n\n" +
               "--- 输出格式 (必须严格遵守) ---\n" +
               "请严格以JSON数组格式返回，数组中的每个对象代表一个交易单元的完整数据：\n" +
               "```json\n" +
               "[\n" +
               "  {\n" +
               "    \"company\": \"公司名称\",\n" +
               "    \"tradingUnitName\": \"交易单元名称\",\n" +
               "    \"header\": {\n" +
               "      \"province\": \"" + (province != null ? province : "省份") + "\",\n" +
               "      \"settlementDate\": \"YYYY-MM\",\n" +
               "      \"period\": \"期间\",\n" +
               "      \"onlineElectricity\": 数字或null,\n" +
               "      \"settlementElectricity\": 数字或null,\n" +
               "      \"contractElectricity\": 数字或null,\n" +
               "      \"deviationElectricity\": 数字或null,\n" +
               "      \"totalFee\": 数字或null\n" +
               "    },\n" +
               "    \"details\": [\n" +
               "      {\n" +
               "        \"code\": \"编码\",\n" +
               "        \"name\": \"名称或描述\",\n" +
               "        \"plannedElectricity\": 数字或null,\n" +
               "        \"settlementElectricity\": 数字或null,\n" +
               "        \"settlementPrice\": 数字或null,\n" +
               "        \"settlementFee\": 数字或null,\n" +
               "        \"remark\": \"备注信息或null\"\n" +
               "      }\n" +
               "    ]\n" +
               "  }\n" +
               "]\n" +
               "```";
    }

  /**
     * [提示词构建] 为第一阶段的“结构识别”任务构建Prompt (V3 - 最终稳定版)。
     * 此版本增加了强制的空值处理和结构完整性约束，以确保 province 字段总是存在。
     */
    private static String buildStructureIdentificationPrompt(String fullText) {
        return "你是一个文档结构分析专家。你的任务是从下面的文本中识别出【省份】以及所有的【市场主体公司】及其下属的【交易单元】。\n\n" +
               "--- 文档纯文本 ---\n" + fullText + "\n\n" +
               "--- 分析规则 ---\n" +
               "1. **省份 (province)**: 关键信息。在文档大标题中寻找地理名称，例如 '山西电力交易中心' 中的 '山西'。\n" +
               "2. **市场主体 (company)**: 例如 '交城县明科光电科技有限公司'。\n" +
               "3. **交易单元 (tradingUnitName)**: 通常是带有 '一期', '二期' 等字样的名称。如果没有明确的'期'，则使用公司名作为交易单元名。\n\n" +
               "--- 关键输出规则 (必须严格遵守！) ---\n" +
               "1. **结构必须完整**: 你的返回结果**必须是一个单独的JSON对象**，包含 `province` 和 `units` 两个顶级键。绝对不能省略任何一个键。\n" +
               "2. **省份必须处理**: 如果你分析后找不到明确的省份，`province` 字段的**值必须是 `null`**，一定要在JSON对象中添加`province`属性。这是强制性要求。\n\n" +
               "--- 输出格式示例 ---\n" +
               "```json\n" +
               "{\n" +
               "  \"province\": \"山西\",\n" +
               "  \"units\": [\n" +
               "    {\n" +
               "      \"company\": \"公司全称\",\n" +
               "      \"tradingUnitName\": \"交易单元一名称\"\n" +
               "    }\n" +
               "  ]\n" +
               "}\n" +
               "```";
    }


    

   
    
   

    private static String callQwenApi(String prompt) throws IOException {
        JSONArray messages = new JSONArray();
        JSONObject userMsg = new JSONObject();
        userMsg.put("role", "user");
        userMsg.put("content", prompt);
        messages.add(userMsg);

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL);
        requestBody.put("messages", messages);
        requestBody.put("temperature", 0.01);

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", "Bearer " + API_KEY);
        headers.put("Content-Type", "application/json");

        try {
            HttpResponse response = HttpRequest.post(API_URL + "/v1/chat/completions")
                    .headerMap(headers, true)
                    .body(requestBody.toJSONString()).setReadTimeout(1200000)
                    .timeout(1200000)
                    .execute();

            if (!response.isOk()) {
                logger.error("API调用失败: {} - {}", response.getStatus(), response.body());
                throw new IOException("API调用失败: " + response.getStatus());
            }
            return parseApiResponse(response.body());
        } catch (Exception e) {
            logger.error("API调用异常: ", e);
            throw new IOException("API调用异常: " + e.getMessage());
        }
    }

    private static String parseApiResponse(String apiResponse) {
        try {
            JSONObject responseJson = JSONObject.parseObject(apiResponse);
            if (responseJson == null) return "[]";

            JSONArray choices = responseJson.getJSONArray("choices");
            if (choices != null && !choices.isEmpty()) {
                JSONObject message = choices.getJSONObject(0).getJSONObject("message");
                if (message != null) {
                    return message.getString("content");
                }
            }
        } catch (Exception e) {
            logger.error("解析API响应JSON时出错, 响应内容: {}", apiResponse, e);
        }
        return "[]";
    }

   
}