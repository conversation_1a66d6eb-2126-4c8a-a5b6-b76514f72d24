package com.sprixin.settle.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import technology.tabula.ObjectExtractor;
import technology.tabula.Page;
import technology.tabula.Table;
import technology.tabula.RectangularTextContainer;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

@Component
public class TabulaQwenApiUtil {
    private static String API_URL;
    private static String API_KEY;
    private static String MODEL;

    @Value("${ai.model.url}")
    private String apiUrlProp;
    @Value("${ai.model.key}")
    private String apiKeyProp;
    @Value("${ai.model.name}")
    private String modelProp;

    @PostConstruct
    public void init() {
        API_URL = apiUrlProp;
        API_KEY = apiKeyProp;
        MODEL = modelProp;
    }

    // 使用 hutool 的 HttpUtil，不需要预定义客户端

    /**
     * 结合PDF文本和表格数据提取结构化信息
     * @param pdfFile PDF文件
     * @return 大模型返回结果（JSON字符串）
     */
    public static String parsePdf(File pdfFile) throws IOException {
        // 1. 去除水印并加载文档
        PDDocument document = WatermarkRemover.removeWatermark(pdfFile);
        
        try {
            // 2. 提取PDF文本内容
            String pdfText = extractFirstPageText(document);
            
            // 3. 初始化结果集合
            JSONObject finalResult = new JSONObject();
            JSONArray allDetails = new JSONArray();
            
            // 4. 按页处理表格数据
            int pageCount = document.getNumberOfPages();
            for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
                // 提取当前页表格数据
                String pageTableData = extractPageTableData(document, pageNum);
                
                // 检查是否有有效表格数据
                if (pageTableData == null || pageTableData.trim().isEmpty() || 
                    !hasValidColumns(pageTableData)) {
                    System.out.println("跳过无有效表格数据的页面: " + pageNum);
                    continue;
                }
                
                // 如果是第一页，处理主表信息
                if (pageNum == 1) {
                    String prompt = buildFirstPagePrompt(pdfText, pageTableData);
                    String apiResponse = callQwenApi(prompt);
                    JSONObject pageResult = JSONObject.parseObject(apiResponse);
                    
                    if (pageResult != null) {
                        finalResult.putAll(pageResult);
                        if (pageResult.containsKey("details")) {
                            allDetails.addAll(pageResult.getJSONArray("details"));
                        }
                    }
                } else {
                    // 处理后续页面的明细表数据
                    String prompt = buildDetailPagePrompt(pageTableData);
                    String apiResponse = callQwenApi(prompt);
                    JSONObject pageResult = JSONObject.parseObject(apiResponse);
                    
                    if (pageResult != null && pageResult.containsKey("details")) {
                        allDetails.addAll(pageResult.getJSONArray("details"));
                    }
                }
            }
            
            // 5. 合并所有结果
            if (!allDetails.isEmpty()) {
                finalResult.put("details", allDetails);
            }
            
            return finalResult.toJSONString();
        } finally {
            if (document != null) {
                document.close();
            }
        }
    }

    /**
     * 检查表格数据是否有有效列
     */
    private static boolean hasValidColumns(String tableData) {
        if (tableData == null || tableData.isEmpty()) {
            return false;
        }
        
        // 检查表格是否有足够的数据列（至少2列）
        String[] lines = tableData.split("\n");
        for (String line : lines) {
            if (line.trim().isEmpty() || line.contains("---TABLE_SEPARATOR---")) {
                continue;
            }
            
            // 计算有效列数（非[NULL]的列）
            String[] columns = line.split("\t");
            int validColumnCount = 0;
            for (String col : columns) {
                if (!col.equals("[NULL]") && !col.trim().isEmpty()) {
                    validColumnCount++;
                }
            }
            
            // 如果有一行至少包含2个有效列，则认为有有效数据
            if (validColumnCount >= 2) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 提取PDF首页文本内容
     */
    private static String extractFirstPageText(PDDocument document) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setSortByPosition(true);
        stripper.setStartPage(1);
        stripper.setEndPage(1);
        return stripper.getText(document);
    }
    
    /**
     * 提取指定页的表格数据
     */
    private static String extractPageTableData(PDDocument document, int pageNum) throws IOException {
        StringBuilder tableText = new StringBuilder();
        ObjectExtractor extractor = new ObjectExtractor(document);
        Page page = extractor.extract(pageNum);
        
        SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();
        List<Table> tables = sea.extract(page);
        
        if (tables.isEmpty()) {
            System.out.println("页面 " + pageNum + " 未检测到表格");
            return "";
        }
        
        for (Table table : tables) {
            List<List<RectangularTextContainer>> rows = table.getRows();
            if (rows.isEmpty()) {
                continue;
            }
            
            int maxColumns = 0;
            for (List<RectangularTextContainer> row : rows) {
                if (row.size() > maxColumns) {
                    maxColumns = row.size();
                }
            }
            if (maxColumns < 2) {
                System.out.println("表格列数不足: " + maxColumns + "，跳过表格");
                continue;
            }
            
            for (List<RectangularTextContainer> row : rows) {
                // 跳过合计行
                boolean isTotal = false;
                for (RectangularTextContainer cell : row) {
                    String text = cell.getText();
                    if (text.contains("合计") || text.contains("总计") || text.contains("小计")) {
                        isTotal = true;
                        break;
                    }
                }
                if (isTotal) {
                    continue;
                }
                
                // 处理每行数据
                for (int i = 0; i < maxColumns; i++) {
                    if (i < row.size()) {
                        String cellText = normalizeCellText(row.get(i).getText());
                        tableText.append(cellText);
                    } else {
                        tableText.append("[NULL]");
                    }
                    tableText.append("\t");
                }
                tableText.append("\n");
            }
            tableText.append("---TABLE_SEPARATOR---\n");
        }
        
        return tableText.toString();
    }

    private static String normalizeCellText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return "[NULL]";
        }
        String cleaned = text.replaceAll("[\\s\\u00A0]+", " ").trim();
        
        if (cleaned.matches("^-?[\\d,]+(\\.[\\d]+)?$")) {
            return cleaned.replace(",", "");
        }
        return cleaned;
    }

    /**
     * 构建第一页的Prompt（包含主表信息）
     */
    private static String buildFirstPagePrompt(String pdfText, String tableData) {
        return "请从电力交易结算单中提取以下结构化数据：\n\n" +
               "### 文档文本（上下文信息）\n" +
               pdfText + "\n\n" +
               "### 表格数据\n" +
               tableData + "\n\n" +
               "### 提取规则\n" +
               "1. 主表字段：\n" +
               "   - province: 省份（从标题开头提取）\n" +
               "   - settlementDate: 结算日期（格式为YYYY-MM）\n" +
               "   - company: 市场主体\n" +
               "   - period: 期间（如'2025年05月'）\n" +
               "   - onlineElectricity: 实际上网电量\n" +
               "   - settlementElectricity: 结算电量\n" +
               "   - contractElectricity: 合同电量\n" +
               "   - deviationElectricity: 偏差电量\n" +
               "   - totalFee: 结算电费\n" +
               "2. 明细表字段（如果有）：\n" +
               "   - code: 结算科目编码\n" +
               "   - name: 结算科目名称\n" +
               "   - plannedElectricity: 交易计划电量\n" +
               "   - settlementElectricity: 结算电量/容量\n" +
               "   - settlementPrice: 结算单价/均价\n" +
               "   - settlementFee: 结算电费\n\n" +
               "### 输出要求\n" +
               "   - 严格JSON格式：{\"province\":\"\",\"settlementDate\":\"\",...,\"details\":[{...}]}\n" +
               "   - 所有数值字段转换为数字类型\n" +
               "   - 空值或[NULL]填0";
    }

    /**
     * 构建明细页的Prompt
     */
    private static String buildDetailPagePrompt(String tableData) {
        return "请从电力交易结算单明细表格中提取以下结构化数据：\n\n" +
               "### 表格数据\n" +
               tableData + "\n\n" +
               "### 提取字段\n" +
               "   - code: 结算科目编码\n" +
               "   - name: 结算科目名称\n" +
               "   - plannedElectricity: 交易计划电量\n" +
               "   - settlementElectricity: 结算电量/容量\n" +
               "   - settlementPrice: 结算单价/均价\n" +
               "   - settlementFee: 结算电费\n\n" +
               "### 输出要求\n" +
               "   - 严格JSON格式：{\"details\":[{...}]}\n" +
               "   - 所有数值字段转换为数字类型\n" +
               "   - 空值或[NULL]填0";
    }

    /**
     * 调用千问大模型API（无重试）
     */
    private static String callQwenApi(String prompt) throws IOException {
        JSONArray messages = new JSONArray();
        JSONObject userMsg = new JSONObject();
        userMsg.put("role", "user");
        userMsg.put("content", prompt);
        messages.add(userMsg);
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL);
        requestBody.put("messages", messages);
        requestBody.put("temperature", 0);
        requestBody.put("max_tokens", 10000);

        // 构建请求头
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", "Bearer " + API_KEY);
        headers.put("Content-Type", "application/json");

        try {
            // 使用 hutool 发送 POST 请求
            HttpResponse response = HttpRequest.post(API_URL + "/v1/chat/completions")
                    .headerMap(headers, true)
                    .body(requestBody.toJSONString())
                    .timeout(600000) // 600秒超时
                    .execute();

            if (!response.isOk()) {
                throw new IOException("API调用失败: " + response.getStatus() + " - " + response.body());
            }

            return parseApiResponse(response.body());
        } catch (Exception e) {
            if (e instanceof IOException) {
                throw (IOException) e;
            }
            throw new IOException("API调用异常: " + e.getMessage(), e);
        }
    }

    /**
     * 解析API响应
     */
    private static String parseApiResponse(String apiResponse) {
        JSONObject responseJson = JSONObject.parseObject(apiResponse);
        if (responseJson == null) return "{}";
        
        JSONArray choices = responseJson.getJSONArray("choices");
        if (choices != null && choices.size() > 0) {
            JSONObject choice = choices.getJSONObject(0);
            JSONObject message = choice.getJSONObject("message");
            return message.getString("content");
        }
        return "{}";
    }
}