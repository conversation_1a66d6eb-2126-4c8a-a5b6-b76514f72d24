#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1800176 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=41064, tid=13388
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-82cfd92dc41a2b13055e6ce64037136e-sock

Host: AMD Ryzen 5 4500U with Radeon Graphics         , 6 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Fri Aug  1 17:21:14 2025 elapsed time: 13.787622 seconds (0d 0h 0m 13s)

---------------  T H R E A D  ---------------

Current thread (0x000001867678c870):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=13388, stack(0x00000028ccf00000,0x00000028cd000000) (1024K)]


Current CompileTask:
C2:13787 4765       4       java.lang.Throwable::printEnclosedStackTrace (350 bytes)

Stack: [0x00000028ccf00000,0x00000028cd000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]
V  [jvm.dll+0x8ae341]
V  [jvm.dll+0x8b08be]
V  [jvm.dll+0x8b0fa3]
V  [jvm.dll+0x280c96]
V  [jvm.dll+0xc581d]
V  [jvm.dll+0xc5d53]
V  [jvm.dll+0x3b9162]
V  [jvm.dll+0x385315]
V  [jvm.dll+0x38477a]
V  [jvm.dll+0x248ed0]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001867d7227a0, length=38, elements={
0x000001866e7021b0, 0x0000018676780df0, 0x0000018676783850, 0x0000018676787920,
0x0000018676788740, 0x000001867678a060, 0x000001867678b620, 0x000001867678c870,
0x00000186767b71d0, 0x0000018676890ee0, 0x00000186776c5980, 0x000001867c046a30,
0x0000018677dd13f0, 0x0000018677dd1a50, 0x0000018677d9c030, 0x0000018677d9c690,
0x000001867c09e6d0, 0x000001867c02c910, 0x000001867c02c140, 0x00000186777b5740,
0x00000186777b5dd0, 0x00000186777b4a20, 0x00000186777b50b0, 0x000001867c659490,
0x000001867c6580e0, 0x000001867c65aed0, 0x000001867c65b560, 0x000001867c659b20,
0x000001867c65dcc0, 0x000001867c65a1b0, 0x000001867c65bbf0, 0x000001867c658770,
0x000001867c65a840, 0x000001867c656d30, 0x000001867c65c280, 0x000001867c658e00,
0x000001867c65cfa0, 0x000001867c65c910
}

Java Threads: ( => current thread )
  0x000001866e7021b0 JavaThread "main"                              [_thread_blocked, id=39736, stack(0x00000028cc700000,0x00000028cc800000) (1024K)]
  0x0000018676780df0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=44056, stack(0x00000028cc900000,0x00000028cca00000) (1024K)]
  0x0000018676783850 JavaThread "Finalizer"                  daemon [_thread_blocked, id=36468, stack(0x00000028cca00000,0x00000028ccb00000) (1024K)]
  0x0000018676787920 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=43044, stack(0x00000028ccb00000,0x00000028ccc00000) (1024K)]
  0x0000018676788740 JavaThread "Attach Listener"            daemon [_thread_blocked, id=45960, stack(0x00000028ccc00000,0x00000028ccd00000) (1024K)]
  0x000001867678a060 JavaThread "Service Thread"             daemon [_thread_blocked, id=22704, stack(0x00000028ccd00000,0x00000028cce00000) (1024K)]
  0x000001867678b620 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=35816, stack(0x00000028cce00000,0x00000028ccf00000) (1024K)]
=>0x000001867678c870 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=13388, stack(0x00000028ccf00000,0x00000028cd000000) (1024K)]
  0x00000186767b71d0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=27060, stack(0x00000028cd000000,0x00000028cd100000) (1024K)]
  0x0000018676890ee0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=34236, stack(0x00000028cd100000,0x00000028cd200000) (1024K)]
  0x00000186776c5980 JavaThread "Notification Thread"        daemon [_thread_blocked, id=44436, stack(0x00000028cd200000,0x00000028cd300000) (1024K)]
  0x000001867c046a30 JavaThread "Equinox resolver thread - Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=29800, stack(0x00000028cd900000,0x00000028cda00000) (1024K)]
  0x0000018677dd13f0 JavaThread "Equinox resolver thread - Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=34220, stack(0x00000028cda00000,0x00000028cdb00000) (1024K)]
  0x0000018677dd1a50 JavaThread "Equinox resolver thread - Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=33556, stack(0x00000028cdb00000,0x00000028cdc00000) (1024K)]
  0x0000018677d9c030 JavaThread "Equinox resolver thread - Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=42532, stack(0x00000028cdc00000,0x00000028cdd00000) (1024K)]
  0x0000018677d9c690 JavaThread "Equinox resolver thread - Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=27068, stack(0x00000028cdd00000,0x00000028cde00000) (1024K)]
  0x000001867c09e6d0 JavaThread "Equinox resolver thread - Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=32128, stack(0x00000028cde00000,0x00000028cdf00000) (1024K)]
  0x000001867c02c910 JavaThread "Active Thread: Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c"        [_thread_blocked, id=604, stack(0x00000028cdf00000,0x00000028ce000000) (1024K)]
  0x000001867c02c140 JavaThread "Framework Event Dispatcher: Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=16688, stack(0x00000028cd300000,0x00000028cd400000) (1024K)]
  0x00000186777b5740 JavaThread "Start Level: Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=26472, stack(0x00000028ce000000,0x00000028ce100000) (1024K)]
  0x00000186777b5dd0 JavaThread "Refresh Thread: Equinox Container: 4fb51995-b0ed-4f1b-ae62-5ac36a1bb42c" daemon [_thread_blocked, id=34364, stack(0x00000028ce200000,0x00000028ce300000) (1024K)]
  0x00000186777b4a20 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=42540, stack(0x00000028ce300000,0x00000028ce400000) (1024K)]
  0x00000186777b50b0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=4408, stack(0x00000028ce400000,0x00000028ce500000) (1024K)]
  0x000001867c659490 JavaThread "Worker-JM"                         [_thread_blocked, id=8384, stack(0x00000028ce100000,0x00000028ce200000) (1024K)]
  0x000001867c6580e0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=20092, stack(0x00000028ce600000,0x00000028ce700000) (1024K)]
  0x000001867c65aed0 JavaThread "Worker-0"                          [_thread_blocked, id=38092, stack(0x00000028ce700000,0x00000028ce800000) (1024K)]
  0x000001867c65b560 JavaThread "Worker-1"                          [_thread_blocked, id=27904, stack(0x00000028ce800000,0x00000028ce900000) (1024K)]
  0x000001867c659b20 JavaThread "Thread-2"                   daemon [_thread_in_native, id=17988, stack(0x00000028ce500000,0x00000028ce600000) (1024K)]
  0x000001867c65dcc0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=9800, stack(0x00000028ceb00000,0x00000028cec00000) (1024K)]
  0x000001867c65a1b0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=43532, stack(0x00000028cec00000,0x00000028ced00000) (1024K)]
  0x000001867c65bbf0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=8376, stack(0x00000028ced00000,0x00000028cee00000) (1024K)]
  0x000001867c658770 JavaThread "Thread-6"                   daemon [_thread_in_native, id=37744, stack(0x00000028cee00000,0x00000028cef00000) (1024K)]
  0x000001867c65a840 JavaThread "Thread-7"                   daemon [_thread_in_native, id=38488, stack(0x00000028cef00000,0x00000028cf000000) (1024K)]
  0x000001867c656d30 JavaThread "Thread-8"                   daemon [_thread_in_native, id=29240, stack(0x00000028cf000000,0x00000028cf100000) (1024K)]
  0x000001867c65c280 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=24384, stack(0x00000028cf200000,0x00000028cf300000) (1024K)]
  0x000001867c658e00 JavaThread "Worker-2"                          [_thread_blocked, id=37844, stack(0x00000028cf300000,0x00000028cf400000) (1024K)]
  0x000001867c65cfa0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=41308, stack(0x00000028cf100000,0x00000028cf200000) (1024K)]
  0x000001867c65c910 JavaThread "pool-1-thread-1"                   [_thread_in_vm, id=40216, stack(0x00000028cf400000,0x00000028cf500000) (1024K)]
Total: 38

Other Threads:
  0x0000018676766ad0 VMThread "VM Thread"                           [id=43276, stack(0x00000028cc800000,0x00000028cc900000) (1024K)]
  0x000001866e849c70 WatcherThread "VM Periodic Task Thread"        [id=42872, stack(0x00000028cc600000,0x00000028cc700000) (1024K)]
  0x000001866e711630 WorkerThread "GC Thread#0"                     [id=16436, stack(0x00000028cc300000,0x00000028cc400000) (1024K)]
  0x0000018677a71440 WorkerThread "GC Thread#1"                     [id=24136, stack(0x00000028cd400000,0x00000028cd500000) (1024K)]
  0x0000018677e965d0 WorkerThread "GC Thread#2"                     [id=2340, stack(0x00000028cd500000,0x00000028cd600000) (1024K)]
  0x0000018677e96970 WorkerThread "GC Thread#3"                     [id=34416, stack(0x00000028cd600000,0x00000028cd700000) (1024K)]
  0x000001867c166010 WorkerThread "GC Thread#4"                     [id=39920, stack(0x00000028cd700000,0x00000028cd800000) (1024K)]
  0x000001867c1663b0 WorkerThread "GC Thread#5"                     [id=37380, stack(0x00000028cd800000,0x00000028cd900000) (1024K)]
Total: 8

Threads with active compile tasks:
C2 CompilerThread0  13891 4765       4       java.lang.Throwable::printEnclosedStackTrace (350 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001860f000000-0x000001860fba0000-0x000001860fba0000), size 12189696, SharedBaseAddress: 0x000001860f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000018610000000-0x0000018650000000, reserved size: 1073741824
Narrow klass base: 0x000001860f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 6 total, 6 available
 Memory: 15591M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 6

Heap:
 PSYoungGen      total 22528K, used 21937K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 99% used [0x00000000d5580000,0x00000000d6ad4778,0x00000000d6b00000)
  from space 512K, 18% used [0x00000000d7180000,0x00000000d7198000,0x00000000d7200000)
  to   space 512K, 0% used [0x00000000d7100000,0x00000000d7100000,0x00000000d7180000)
 ParOldGen       total 68608K, used 22046K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 32% used [0x0000000080000000,0x0000000081587ab8,0x0000000084300000)
 Metaspace       used 30512K, committed 31296K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K

Card table byte_map: [0x000001866dfc0000,0x000001866e3d0000] _byte_map_base: 0x000001866dbc0000

Marking Bits: (ParMarkBitMap*) 0x00007ffce3e6a340
 Begin Bits: [0x0000018671ca0000, 0x0000018673ca0000)
 End Bits:   [0x0000018673ca0000, 0x0000018675ca0000)

Polling page: 0x000001866aa90000

Metaspace:

Usage:
  Non-class:     26.90 MB used.
      Class:      2.90 MB used.
       Both:     29.80 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      27.38 MB ( 43%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.19 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      30.56 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  4.42 MB
       Class:  12.69 MB
        Both:  17.11 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 558.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 489.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1692.
num_chunk_merges: 9.
num_chunk_splits: 1107.
num_chunks_enlarged: 725.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2578Kb max_used=2578Kb free=117421Kb
 bounds [0x0000018607ad0000, 0x0000018607d60000, 0x000001860f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=10224Kb max_used=10224Kb free=109775Kb
 bounds [0x0000018600000000, 0x0000018600a00000, 0x0000018607530000]
CodeHeap 'non-nmethods': size=5760Kb used=1342Kb max_used=1389Kb free=4417Kb
 bounds [0x0000018607530000, 0x00000186077a0000, 0x0000018607ad0000]
CodeCache: size=245760Kb, used=14144Kb, max_used=14191Kb, free=231613Kb
 total_blobs=5475, nmethods=4840, adapters=541, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 13.748 Thread 0x00000186767b71d0 4795       3       com.google.gson.stream.JsonReader::<init> (114 bytes)
Event: 13.748 Thread 0x00000186767b71d0 nmethod 4795 0x00000186009c9310 code [0x00000186009c94e0, 0x00000186009c9ae0]
Event: 13.748 Thread 0x00000186767b71d0 4796       3       com.google.gson.Gson::fromJson (10 bytes)
Event: 13.748 Thread 0x00000186767b71d0 nmethod 4796 0x00000186009c9c90 code [0x00000186009c9e60, 0x00000186009ca0b0]
Event: 13.748 Thread 0x00000186767b71d0 4797       3       com.google.gson.reflect.TypeToken::get (9 bytes)
Event: 13.748 Thread 0x00000186767b71d0 nmethod 4797 0x00000186009ca210 code [0x00000186009ca3c0, 0x00000186009ca548]
Event: 13.748 Thread 0x00000186767b71d0 4798   !   3       com.google.gson.Gson::fromJson (259 bytes)
Event: 13.750 Thread 0x00000186767b71d0 nmethod 4798 0x00000186009ca610 code [0x00000186009cab60, 0x00000186009ccfb0]
Event: 13.750 Thread 0x00000186767b71d0 4800       3       java.util.logging.ConsoleHandler::publish (10 bytes)
Event: 13.750 Thread 0x00000186767b71d0 nmethod 4800 0x00000186009ce890 code [0x00000186009cea40, 0x00000186009cec48]
Event: 13.750 Thread 0x00000186767b71d0 4799       3       com.google.gson.stream.JsonReader::peek (144 bytes)
Event: 13.751 Thread 0x00000186767b71d0 nmethod 4799 0x00000186009ced10 code [0x00000186009cefc0, 0x00000186009cfd00]
Event: 13.751 Thread 0x00000186767b71d0 4801   !   3       java.io.PrintStream::flush (60 bytes)
Event: 13.751 Thread 0x00000186767b71d0 nmethod 4801 0x00000186009d0090 code [0x00000186009d03a0, 0x00000186009d14e0]
Event: 13.751 Thread 0x00000186767b71d0 4802   !   3       java.util.logging.StreamHandler::flush0 (26 bytes)
Event: 13.751 Thread 0x00000186767b71d0 nmethod 4802 0x00000186009d1c90 code [0x00000186009d1e60, 0x00000186009d2198]
Event: 13.769 Thread 0x00000186767b71d0 4803       3       java.lang.StackStreamFactory$AbstractStackWalker::<init> (88 bytes)
Event: 13.770 Thread 0x00000186767b71d0 nmethod 4803 0x00000186009d2310 code [0x00000186009d2500, 0x00000186009d29b8]
Event: 13.770 Thread 0x00000186767b71d0 4804       3       java.lang.StackStreamFactory$AbstractStackWalker::beginStackWalk (43 bytes)
Event: 13.770 Thread 0x00000186767b71d0 nmethod 4804 0x00000186009d2b90 code [0x00000186009d2d60, 0x00000186009d3160]

GC Heap History (20 events):
Event: 9.305 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29678K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767bb40,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 4875K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804c2c20,0x0000000084300000)
 Metaspace       used 17101K, committed 17728K, reserved 1114112K
  class space    used 1783K, committed 2048K, reserved 1048576K
}
Event: 9.314 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4078K [0x00000000d5580000, 0x00000000d7f00000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727b9d8,0x00000000d7280000)
  to   space 8704K, 0% used [0x00000000d7680000,0x00000000d7680000,0x00000000d7f00000)
 ParOldGen       total 68608K, used 8682K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008087a9e0,0x0000000084300000)
 Metaspace       used 17101K, committed 17728K, reserved 1114112K
  class space    used 1783K, committed 2048K, reserved 1048576K
}
Event: 9.711 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 29166K [0x00000000d5580000, 0x00000000d7f00000, 0x0000000100000000)
  eden space 25088K, 100% used [0x00000000d5580000,0x00000000d6e00000,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727b9d8,0x00000000d7280000)
  to   space 8704K, 0% used [0x00000000d7680000,0x00000000d7680000,0x00000000d7f00000)
 ParOldGen       total 68608K, used 8682K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008087a9e0,0x0000000084300000)
 Metaspace       used 20299K, committed 20928K, reserved 1114112K
  class space    used 1991K, committed 2240K, reserved 1048576K
}
Event: 9.715 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 4461K [0x00000000d5580000, 0x00000000d7b00000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 4608K, 96% used [0x00000000d7680000,0x00000000d7adb458,0x00000000d7b00000)
  to   space 6144K, 0% used [0x00000000d6f00000,0x00000000d6f00000,0x00000000d7500000)
 ParOldGen       total 68608K, used 8690K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008087c9e0,0x0000000084300000)
 Metaspace       used 20299K, committed 20928K, reserved 1114112K
  class space    used 1991K, committed 2240K, reserved 1048576K
}
Event: 9.763 GC heap before
{Heap before GC invocations=7 (full 0):
 PSYoungGen      total 29184K, used 7721K [0x00000000d5580000, 0x00000000d7b00000, 0x0000000100000000)
  eden space 24576K, 13% used [0x00000000d5580000,0x00000000d58aefd8,0x00000000d6d80000)
  from space 4608K, 96% used [0x00000000d7680000,0x00000000d7adb458,0x00000000d7b00000)
  to   space 6144K, 0% used [0x00000000d6f00000,0x00000000d6f00000,0x00000000d7500000)
 ParOldGen       total 68608K, used 8690K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008087c9e0,0x0000000084300000)
 Metaspace       used 20932K, committed 21504K, reserved 1114112K
  class space    used 2052K, committed 2304K, reserved 1048576K
}
Event: 9.768 GC heap after
{Heap after GC invocations=7 (full 0):
 PSYoungGen      total 27648K, used 2858K [0x00000000d5580000, 0x00000000d7700000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 3072K, 93% used [0x00000000d6f00000,0x00000000d71cab00,0x00000000d7200000)
  to   space 4096K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7700000)
 ParOldGen       total 68608K, used 10332K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a173e0,0x0000000084300000)
 Metaspace       used 20932K, committed 21504K, reserved 1114112K
  class space    used 2052K, committed 2304K, reserved 1048576K
}
Event: 9.768 GC heap before
{Heap before GC invocations=8 (full 1):
 PSYoungGen      total 27648K, used 2858K [0x00000000d5580000, 0x00000000d7700000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 3072K, 93% used [0x00000000d6f00000,0x00000000d71cab00,0x00000000d7200000)
  to   space 4096K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7700000)
 ParOldGen       total 68608K, used 10332K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a173e0,0x0000000084300000)
 Metaspace       used 20932K, committed 21504K, reserved 1114112K
  class space    used 2052K, committed 2304K, reserved 1048576K
}
Event: 9.811 GC heap after
{Heap after GC invocations=8 (full 1):
 PSYoungGen      total 27648K, used 0K [0x00000000d5580000, 0x00000000d7700000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 3072K, 0% used [0x00000000d6f00000,0x00000000d6f00000,0x00000000d7200000)
  to   space 4096K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7700000)
 ParOldGen       total 68608K, used 12719K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 18% used [0x0000000080000000,0x0000000080c6bd50,0x0000000084300000)
 Metaspace       used 20919K, committed 21504K, reserved 1114112K
  class space    used 2048K, committed 2304K, reserved 1048576K
}
Event: 10.502 GC heap before
{Heap before GC invocations=9 (full 1):
 PSYoungGen      total 27648K, used 24576K [0x00000000d5580000, 0x00000000d7700000, 0x0000000100000000)
  eden space 24576K, 100% used [0x00000000d5580000,0x00000000d6d80000,0x00000000d6d80000)
  from space 3072K, 0% used [0x00000000d6f00000,0x00000000d6f00000,0x00000000d7200000)
  to   space 4096K, 0% used [0x00000000d7300000,0x00000000d7300000,0x00000000d7700000)
 ParOldGen       total 68608K, used 12719K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 18% used [0x0000000080000000,0x0000000080c6bd50,0x0000000084300000)
 Metaspace       used 23048K, committed 23680K, reserved 1114112K
  class space    used 2210K, committed 2496K, reserved 1048576K
}
Event: 10.503 GC heap after
{Heap after GC invocations=9 (full 1):
 PSYoungGen      total 26112K, used 2027K [0x00000000d5580000, 0x00000000d7500000, 0x0000000100000000)
  eden space 24064K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d00000)
  from space 2048K, 99% used [0x00000000d7300000,0x00000000d74fae88,0x00000000d7500000)
  to   space 2560K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7280000)
 ParOldGen       total 68608K, used 12727K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 18% used [0x0000000080000000,0x0000000080c6dd50,0x0000000084300000)
 Metaspace       used 23048K, committed 23680K, reserved 1114112K
  class space    used 2210K, committed 2496K, reserved 1048576K
}
Event: 11.239 GC heap before
{Heap before GC invocations=10 (full 1):
 PSYoungGen      total 26112K, used 26091K [0x00000000d5580000, 0x00000000d7500000, 0x0000000100000000)
  eden space 24064K, 100% used [0x00000000d5580000,0x00000000d6d00000,0x00000000d6d00000)
  from space 2048K, 99% used [0x00000000d7300000,0x00000000d74fae88,0x00000000d7500000)
  to   space 2560K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7280000)
 ParOldGen       total 68608K, used 12727K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 18% used [0x0000000080000000,0x0000000080c6dd50,0x0000000084300000)
 Metaspace       used 26347K, committed 27008K, reserved 1114112K
  class space    used 2539K, committed 2816K, reserved 1048576K
}
Event: 11.244 GC heap after
{Heap after GC invocations=10 (full 1):
 PSYoungGen      total 26112K, used 2534K [0x00000000d5580000, 0x00000000d7480000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c80000)
  from space 2560K, 99% used [0x00000000d7000000,0x00000000d72799f8,0x00000000d7280000)
  to   space 2048K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7480000)
 ParOldGen       total 68608K, used 13963K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 20% used [0x0000000080000000,0x0000000080da2df0,0x0000000084300000)
 Metaspace       used 26347K, committed 27008K, reserved 1114112K
  class space    used 2539K, committed 2816K, reserved 1048576K
}
Event: 12.194 GC heap before
{Heap before GC invocations=11 (full 1):
 PSYoungGen      total 26112K, used 26086K [0x00000000d5580000, 0x00000000d7480000, 0x0000000100000000)
  eden space 23552K, 100% used [0x00000000d5580000,0x00000000d6c80000,0x00000000d6c80000)
  from space 2560K, 99% used [0x00000000d7000000,0x00000000d72799f8,0x00000000d7280000)
  to   space 2048K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7480000)
 ParOldGen       total 68608K, used 13963K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 20% used [0x0000000080000000,0x0000000080da2df0,0x0000000084300000)
 Metaspace       used 28036K, committed 28800K, reserved 1114112K
  class space    used 2730K, committed 3072K, reserved 1048576K
}
Event: 12.196 GC heap after
{Heap after GC invocations=11 (full 1):
 PSYoungGen      total 24576K, used 1280K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 23040K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c00000)
  from space 1536K, 83% used [0x00000000d7280000,0x00000000d73c0000,0x00000000d7400000)
  to   space 2560K, 0% used [0x00000000d6f00000,0x00000000d6f00000,0x00000000d7180000)
 ParOldGen       total 68608K, used 15800K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 23% used [0x0000000080000000,0x0000000080f6e040,0x0000000084300000)
 Metaspace       used 28036K, committed 28800K, reserved 1114112K
  class space    used 2730K, committed 3072K, reserved 1048576K
}
Event: 13.331 GC heap before
{Heap before GC invocations=12 (full 1):
 PSYoungGen      total 24576K, used 24320K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 23040K, 100% used [0x00000000d5580000,0x00000000d6c00000,0x00000000d6c00000)
  from space 1536K, 83% used [0x00000000d7280000,0x00000000d73c0000,0x00000000d7400000)
  to   space 2560K, 0% used [0x00000000d6f00000,0x00000000d6f00000,0x00000000d7180000)
 ParOldGen       total 68608K, used 15800K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 23% used [0x0000000080000000,0x0000000080f6e040,0x0000000084300000)
 Metaspace       used 30319K, committed 31040K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K
}
Event: 13.334 GC heap after
{Heap after GC invocations=12 (full 1):
 PSYoungGen      total 25088K, used 1051K [0x00000000d5580000, 0x00000000d7300000, 0x0000000100000000)
  eden space 22528K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b80000)
  from space 2560K, 41% used [0x00000000d6f00000,0x00000000d7006f90,0x00000000d7180000)
  to   space 1536K, 0% used [0x00000000d7180000,0x00000000d7180000,0x00000000d7300000)
 ParOldGen       total 68608K, used 16904K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 24% used [0x0000000080000000,0x0000000081082088,0x0000000084300000)
 Metaspace       used 30319K, committed 31040K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K
}
Event: 13.562 GC heap before
{Heap before GC invocations=13 (full 1):
 PSYoungGen      total 25088K, used 23579K [0x00000000d5580000, 0x00000000d7300000, 0x0000000100000000)
  eden space 22528K, 100% used [0x00000000d5580000,0x00000000d6b80000,0x00000000d6b80000)
  from space 2560K, 41% used [0x00000000d6f00000,0x00000000d7006f90,0x00000000d7180000)
  to   space 1536K, 0% used [0x00000000d7180000,0x00000000d7180000,0x00000000d7300000)
 ParOldGen       total 68608K, used 16904K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 24% used [0x0000000080000000,0x0000000081082088,0x0000000084300000)
 Metaspace       used 30424K, committed 31168K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K
}
Event: 13.565 GC heap after
{Heap after GC invocations=13 (full 1):
 PSYoungGen      total 22528K, used 96K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 512K, 18% used [0x00000000d7180000,0x00000000d7198000,0x00000000d7200000)
  to   space 512K, 0% used [0x00000000d7100000,0x00000000d7100000,0x00000000d7180000)
 ParOldGen       total 68608K, used 18951K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081281ea8,0x0000000084300000)
 Metaspace       used 30424K, committed 31168K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K
}
Event: 13.744 GC heap before
{Heap before GC invocations=14 (full 1):
 PSYoungGen      total 22528K, used 22112K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 100% used [0x00000000d5580000,0x00000000d6b00000,0x00000000d6b00000)
  from space 512K, 18% used [0x00000000d7180000,0x00000000d7198000,0x00000000d7200000)
  to   space 512K, 0% used [0x00000000d7100000,0x00000000d7100000,0x00000000d7180000)
 ParOldGen       total 68608K, used 18951K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081281ea8,0x0000000084300000)
 Metaspace       used 30457K, committed 31168K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K
}
Event: 13.744 GC heap after
{Heap after GC invocations=14 (full 1):
 PSYoungGen      total 22528K, used 128K [0x00000000d5580000, 0x00000000d7200000, 0x0000000100000000)
  eden space 22016K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b00000)
  from space 512K, 25% used [0x00000000d7100000,0x00000000d7120000,0x00000000d7180000)
  to   space 512K, 0% used [0x00000000d7180000,0x00000000d7180000,0x00000000d7200000)
 ParOldGen       total 68608K, used 18983K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 27% used [0x0000000080000000,0x0000000081289ea8,0x0000000084300000)
 Metaspace       used 30457K, committed 31168K, reserved 1114112K
  class space    used 2969K, committed 3264K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.088 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.143 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.209 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.220 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.445 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.448 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.482 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.575 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 7.415 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250730-1736\eclipse_11913.dll
Event: 9.155 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna4971177454679219085.dll

Deoptimization events (20 events):
Event: 12.874 Thread 0x000001866e7021b0 DEOPT PACKING pc=0x0000018607c89124 sp=0x00000028cc7fe540
Event: 12.874 Thread 0x000001866e7021b0 DEOPT UNPACKING pc=0x0000018607583aa2 sp=0x00000028cc7fe400 mode 2
Event: 12.874 Thread 0x000001866e7021b0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018607b08748 relative=0x00000000000000a8
Event: 12.874 Thread 0x000001866e7021b0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018607b08748 method=java.lang.CharacterDataLatin1.digit(II)I @ 7 c2
Event: 12.874 Thread 0x000001866e7021b0 DEOPT PACKING pc=0x0000018607b08748 sp=0x00000028cc7fe4a0
Event: 12.874 Thread 0x000001866e7021b0 DEOPT UNPACKING pc=0x0000018607583aa2 sp=0x00000028cc7fe420 mode 2
Event: 13.152 Thread 0x000001867c65c910 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018607adcaa8 relative=0x0000000000000188
Event: 13.152 Thread 0x000001867c65c910 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018607adcaa8 method=java.lang.String.equals(Ljava/lang/Object;)Z @ 33 c2
Event: 13.152 Thread 0x000001867c65c910 DEOPT PACKING pc=0x0000018607adcaa8 sp=0x00000028cf4feaa0
Event: 13.152 Thread 0x000001867c65c910 DEOPT UNPACKING pc=0x0000018607583aa2 sp=0x00000028cf4fea30 mode 2
Event: 13.153 Thread 0x000001867c65c910 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018607d03c24 relative=0x0000000000000264
Event: 13.153 Thread 0x000001867c65c910 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018607d03c24 method=java.lang.String.getChars(II[CI)V @ 24 c2
Event: 13.153 Thread 0x000001867c65c910 DEOPT PACKING pc=0x0000018607d03c24 sp=0x00000028cf4feae0
Event: 13.153 Thread 0x000001867c65c910 DEOPT UNPACKING pc=0x0000018607583aa2 sp=0x00000028cf4feaa0 mode 2
Event: 13.178 Thread 0x000001867c65c910 DEOPT PACKING pc=0x00000186006330d7 sp=0x00000028cf4fe9e0
Event: 13.178 Thread 0x000001867c65c910 DEOPT UNPACKING pc=0x0000018607584242 sp=0x00000028cf4fdf10 mode 0
Event: 13.181 Thread 0x000001867c65c910 DEOPT PACKING pc=0x00000186006330d7 sp=0x00000028cf4fe9e0
Event: 13.181 Thread 0x000001867c65c910 DEOPT UNPACKING pc=0x0000018607584242 sp=0x00000028cf4fdf10 mode 0
Event: 13.275 Thread 0x000001867c65c910 DEOPT PACKING pc=0x00000186009172ab sp=0x00000028cf4fe660
Event: 13.275 Thread 0x000001867c65c910 DEOPT UNPACKING pc=0x0000018607584242 sp=0x00000028cf4fdba8 mode 0

Classes loaded (20 events):
Event: 13.151 Loading class sun/util/logging/resources/logging
Event: 13.151 Loading class sun/util/logging/resources/logging done
Event: 13.151 Loading class sun/util/logging/resources/logging_zh
Event: 13.151 Loading class sun/util/logging/resources/logging_zh done
Event: 13.151 Loading class sun/util/logging/resources/logging_zh
Event: 13.151 Loading class sun/util/logging/resources/logging_zh done
Event: 13.151 Loading class sun/util/logging/resources/logging_zh_CN
Event: 13.151 Loading class sun/util/logging/resources/logging_zh_CN done
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans done
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans done
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans_CN
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans_CN done
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans_CN
Event: 13.152 Loading class sun/util/logging/resources/logging_zh_Hans_CN done
Event: 13.152 Loading class java/util/Formatter$DateTime
Event: 13.152 Loading class java/util/Formatter$DateTime done
Event: 13.152 Loading class java/time/ZonedDateTime$1
Event: 13.152 Loading class java/time/ZonedDateTime$1 done

Classes unloaded (7 events):
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a2c00 'java/lang/invoke/LambdaForm$MH+0x00000186101a2c00'
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a2800 'java/lang/invoke/LambdaForm$MH+0x00000186101a2800'
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a2400 'java/lang/invoke/LambdaForm$MH+0x00000186101a2400'
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a2000 'java/lang/invoke/LambdaForm$MH+0x00000186101a2000'
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a1c00 'java/lang/invoke/LambdaForm$BMH+0x00000186101a1c00'
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a1800 'java/lang/invoke/LambdaForm$DMH+0x00000186101a1800'
Event: 9.777 Thread 0x0000018676766ad0 Unloading class 0x00000186101a0800 'java/lang/invoke/LambdaForm$DMH+0x00000186101a0800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 13.777 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d602cb80}> (0x00000000d602cb80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.777 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d6045960}> (0x00000000d6045960) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.777 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d605e290}> (0x00000000d605e290) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.777 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d6076bc0}> (0x00000000d6076bc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.778 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d608f778}> (0x00000000d608f778) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.778 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d60a80a8}> (0x00000000d60a80a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.778 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d60c09d8}> (0x00000000d60c09d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.778 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d60d9fa8}> (0x00000000d60d9fa8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.778 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d60f28d8}> (0x00000000d60f28d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.779 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d610b208}> (0x00000000d610b208) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.779 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d6123d78}> (0x00000000d6123d78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.779 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d613c6a8}> (0x00000000d613c6a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.779 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d6154fd8}> (0x00000000d6154fd8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.779 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d616e0b0}> (0x00000000d616e0b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.779 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d61869e0}> (0x00000000d61869e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.780 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d619f310}> (0x00000000d619f310) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.780 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d61b7f30}> (0x00000000d61b7f30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.780 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d61d0860}> (0x00000000d61d0860) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.780 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d61e9190}> (0x00000000d61e9190) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.781 Thread 0x000001867c65c910 Exception <a 'java/io/IOException'{0x00000000d6202f98}> (0x00000000d6202f98) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 10.889 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10.889 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10.997 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 10.997 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 11.001 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 11.001 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 11.023 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 11.023 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 11.239 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 11.244 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 12.159 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 12.196 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 13.196 Executing VM operation: Cleanup
Event: 13.219 Executing VM operation: Cleanup done
Event: 13.331 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 13.334 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 13.562 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 13.565 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 13.744 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 13.744 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 9.239 Thread 0x00000186777b5740 Thread added: 0x000001867c65aed0
Event: 9.269 Thread 0x00000186777b5740 Thread added: 0x000001867c65b560
Event: 9.604 Thread 0x000001867c911650 Thread exited: 0x000001867c911650
Event: 10.695 Thread 0x00000186767b71d0 Thread added: 0x000001867cf74e20
Event: 10.723 Thread 0x00000186777b5740 Thread added: 0x000001867c659b20
Event: 10.851 Thread 0x000001867c659b20 Thread exited: 0x000001867c659b20
Event: 11.293 Thread 0x000001867cf74e20 Thread exited: 0x000001867cf74e20
Event: 11.348 Thread 0x000001866e7021b0 Thread added: 0x000001867c659b20
Event: 11.349 Thread 0x000001866e7021b0 Thread added: 0x000001867c65dcc0
Event: 11.349 Thread 0x000001866e7021b0 Thread added: 0x000001867c65a1b0
Event: 11.349 Thread 0x000001866e7021b0 Thread added: 0x000001867c65bbf0
Event: 11.349 Thread 0x000001866e7021b0 Thread added: 0x000001867c658770
Event: 11.349 Thread 0x000001866e7021b0 Thread added: 0x000001867c65a840
Event: 11.349 Thread 0x000001866e7021b0 Thread added: 0x000001867c656d30
Event: 11.349 Thread 0x00000186767b71d0 Thread added: 0x000001867cc57030
Event: 11.975 Thread 0x000001866e7021b0 Thread added: 0x000001867c65c280
Event: 12.115 Thread 0x000001867c65aed0 Thread added: 0x000001867c658e00
Event: 12.470 Thread 0x000001867cc57030 Thread exited: 0x000001867cc57030
Event: 12.869 Thread 0x000001866e7021b0 Thread added: 0x000001867c65cfa0
Event: 12.869 Thread 0x000001866e7021b0 Thread added: 0x000001867c65c910


Dynamic libraries:
0x00007ff6ef570000 - 0x00007ff6ef57e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffd5a210000 - 0x00007ffd5a408000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd58ff0000 - 0x00007ffd590b2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd57d40000 - 0x00007ffd58036000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd3ce50000 - 0x00007ffd3ce67000 	C:\InetPub\ftproot\Tipray\Ldterm\ghijt64.DLL
0x00007ffd58d90000 - 0x00007ffd58e41000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd582d0000 - 0x00007ffd5836e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd58230000 - 0x00007ffd582cf000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd586e0000 - 0x00007ffd58803000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd57ae0000 - 0x00007ffd57b07000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffd57b10000 - 0x00007ffd57c10000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd50720000 - 0x00007ffd50738000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffd58e50000 - 0x00007ffd58fed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd58200000 - 0x00007ffd58222000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd5a0f0000 - 0x00007ffd5a11b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd580e0000 - 0x00007ffd581f9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd58040000 - 0x00007ffd580dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd4ebd0000 - 0x00007ffd4ee6a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffd4fd00000 - 0x00007ffd4fd1e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffd3c3e0000 - 0x00007ffd3c665000 	C:\InetPub\ftproot\Tipray\Ldterm\LdHook64.dll
0x00007ffd59100000 - 0x00007ffd5922b000 	C:\WINDOWS\System32\ole32.dll
0x00007ffd58380000 - 0x00007ffd586d3000 	C:\WINDOWS\System32\combase.dll
0x00007ffd590f0000 - 0x00007ffd590f8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd4eab0000 - 0x00007ffd4eb54000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffd58870000 - 0x00007ffd5891d000 	C:\WINDOWS\System32\shcore.dll
0x00007ffd570f0000 - 0x00007ffd570fc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.DLL
0x00007ffd590c0000 - 0x00007ffd590ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd575b0000 - 0x00007ffd57626000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\Vozokopot.dll
0x00007ffd3c2f0000 - 0x00007ffd3c317000 	C:\Inetpub\ftproot\Tipray\LdTerm\ghhlp64.dll
0x00007ffd3c270000 - 0x00007ffd3c280000 	C:\InetPub\ftproot\Tipray\Ldterm\HookDataInteractionx64.dll
0x00007ffd3c0a0000 - 0x00007ffd3c113000 	C:\InetPub\ftproot\Tipray\Ldterm\LdSmartEnc64.dll
0x00007ffd42370000 - 0x00007ffd4237b000 	C:\WINDOWS\SYSTEM32\FLTLIB.DLL
0x00007ffd3c1b0000 - 0x00007ffd3c1ef000 	C:\Inetpub\ftproot\Tipray\LdTerm\LdUserInjectDll64.dll
0x00007ffd0fde0000 - 0x00007ffd101c9000 	C:\Inetpub\ftproot\Tipray\LdTerm\LdWaterMarkHook64.dll
0x00007ffd40d90000 - 0x00007ffd40f37000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.5915_none_919facb6cc8c4195\gdiplus.dll
0x00007ffd4f280000 - 0x00007ffd4f646000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathCore.dll
0x00007ffd59ed0000 - 0x00007ffd59f3b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd59d00000 - 0x00007ffd59dda000 	C:\WINDOWS\System32\COMDLG32.dll
0x00007ffd59b20000 - 0x00007ffd59b7b000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffd593a0000 - 0x00007ffd59b0e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd59250000 - 0x00007ffd5931d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd4e770000 - 0x00007ffd4eaac000 	C:\WINDOWS\SYSTEM32\msi.dll
0x00007ffd56bc0000 - 0x00007ffd56bfb000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd511e0000 - 0x00007ffd511f4000 	C:\WINDOWS\SYSTEM32\WTSAPI32.dll
0x00007ffd4f0a0000 - 0x00007ffd4f0bd000 	C:\WINDOWS\SYSTEM32\MPR.dll
0x00007ffd56c00000 - 0x00007ffd56cca000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd59b10000 - 0x00007ffd59b18000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd4e200000 - 0x00007ffd4e209000 	C:\WINDOWS\SYSTEM32\wsock32.dll
0x00007ffd4e1d0000 - 0x00007ffd4e200000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathOM.dll
0x00007ffd4e060000 - 0x00007ffd4e067000 	C:\WINDOWS\SYSTEM32\MSIMG32.dll
0x00007ffd3f7d0000 - 0x00007ffd3f992000 	C:\Inetpub\ftproot\Tipray\LdTerm\LdPrintMonitor64.dll
0x00007ffd4daa0000 - 0x00007ffd4dace000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathFw.dll
0x00007ffd4e030000 - 0x00007ffd4e05d000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathNet.dll
0x00007ffd4de80000 - 0x00007ffd4def8000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathFile.dll
0x00007ffd4dd00000 - 0x00007ffd4de2e000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathProcess.dll
0x00007ffd4dc00000 - 0x00007ffd4dc58000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathBolo2.dll
0x00007ffd4de50000 - 0x00007ffd4de7b000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathProtect.dll
0x00007ffd4de40000 - 0x00007ffd4de47000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathManifest.dll
0x00007ffd51b50000 - 0x00007ffd51b5c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffd2eff0000 - 0x00007ffd2f07d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffce31b0000 - 0x00007ffce3f47000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffd57650000 - 0x00007ffd5769b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd4b910000 - 0x00007ffd4b937000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd4de30000 - 0x00007ffd4de3a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd57630000 - 0x00007ffd57642000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd56090000 - 0x00007ffd560a2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd51af0000 - 0x00007ffd51afa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffd553c0000 - 0x00007ffd555c1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd40d50000 - 0x00007ffd40d84000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd57a50000 - 0x00007ffd57ad2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd50c10000 - 0x00007ffd50c1f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffd4fce0000 - 0x00007ffd4fcff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffd4f1d0000 - 0x00007ffd4f244000 	C:\WINDOWS\SYSTEM32\Wlanapi.dll
0x00007ffd577d0000 - 0x00007ffd577f4000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd555d0000 - 0x00007ffd55d74000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd57180000 - 0x00007ffd571ab000 	C:\WINDOWS\System32\Wldp.dll
0x00007ffd4fcc0000 - 0x00007ffd4fcd8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffd50630000 - 0x00007ffd50640000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffd4e2f0000 - 0x00007ffd4e3fa000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd56ee0000 - 0x00007ffd56f4a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffd4fbc0000 - 0x00007ffd4fbd6000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffd505f0000 - 0x00007ffd50600000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffd4a4d0000 - 0x00007ffd4a515000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250730-1736\eclipse_11913.dll
0x00007ffd570d0000 - 0x00007ffd570e8000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd56800000 - 0x00007ffd56838000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd57750000 - 0x00007ffd5777e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd3bd90000 - 0x00007ffd3bdd9000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna4971177454679219085.dll
0x00007ffd516e0000 - 0x00007ffd516f7000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd51680000 - 0x00007ffd5169d000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\InetPub\ftproot\Tipray\Ldterm;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\WINDOWS\LVUAAgentInstBaseRoot\system32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.5915_none_919facb6cc8c4195;c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250730-1736;C:\Users\<USER>\AppData\Local\Temp\jna-146731693

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java\ss_ws --pipe=\\.\pipe\lsp-82cfd92dc41a2b13055e6ce64037136e-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\jdk1.8.0_151
PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk1.8.0_151\bin;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\Program Files\nodejs\;D:\Program Files\MATLAB\R2023b\runtime\win64;D:\Program Files\MATLAB\R2023b\bin;D:\Program Files\Java\jdk1.8.0_151\jre\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;E:\Programs\cursor\resources\app\bin;D:\software\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\software\Programs\Kiro\bin;D:\Programs\Microsoft VS Code\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 20 days 20:12 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2375, Current Mhz: 2375, Mhz Limit: 2375

Memory: 4k page, system-wide physical 15591M (372M free)
TotalPageFile size 38375M (AvailPageFile size 22M)
current process WorkingSet (physical memory assigned to process): 203M, peak: 203M
current process commit charge ("private bytes"): 314M, peak: 317M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
