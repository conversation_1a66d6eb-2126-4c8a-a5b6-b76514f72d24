version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 1
JvmtiExport can_post_on_exceptions 0
# 474 ciObject found
instanceKlass java/lang/Throwable
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass java/time/LocalDate$1
instanceKlass java/time/ZonedDateTime$1
instanceKlass java/util/Formatter$DateTime
instanceKlass java/util/logging/Level$RbAccess
instanceKlass java/lang/Throwable$PrintStreamOrWriter
instanceKlass  @bci java/util/logging/LogRecord inferCaller ()V 18 <appendix> member <vmtarget> ; # java/util/logging/LogRecord$$Lambda+0x00000186102de530
instanceKlass java/lang/StackStreamFactory$FrameBuffer
instanceKlass java/lang/StackStreamFactory
instanceKlass  @bci java/util/logging/LogRecord$CallerFinder get ()Ljava/util/Optional; 4 <appendix> member <vmtarget> ; # java/util/logging/LogRecord$CallerFinder$$Lambda+0x00000186102dcd30
instanceKlass  @bci java/util/logging/LogRecord$CallerFinder <clinit> ()V 0 <appendix> argL0 ; # java/util/logging/LogRecord$CallerFinder$$Lambda+0x00000186102dcb10
instanceKlass java/util/logging/LogRecord$CallerFinder
instanceKlass java/util/logging/LogManager$CloseOnReset
instanceKlass java/util/logging/StreamHandler$1
instanceKlass java/util/logging/Handler$1
instanceKlass java/util/logging/ErrorManager
instanceKlass jdk/internal/logger/SimpleConsoleLogger$Formatting
instanceKlass  @bci java/util/logging/SimpleFormatter <init> ()V 5 <appendix> argL0 ; # java/util/logging/SimpleFormatter$$Lambda+0x00000186102dbbe8
instanceKlass java/util/logging/Formatter
instanceKlass java/time/Clock
instanceKlass java/time/InstantSource
instanceKlass java/util/logging/LogRecord
instanceKlass com/google/gson/internal/JsonReaderInternalAccess
instanceKlass sun/nio/ch/WindowsAsynchronousFileChannelImpl$ReadTask
instanceKlass sun/nio/ch/Iocp$ResultHandler
instanceKlass sun/nio/ch/PendingFuture
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageProducer$Headers
instanceKlass org/eclipse/equinox/internal/app/EclipseAppHandle$1
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppHandle getStartupMonitors ()[Lorg/osgi/framework/ServiceReference; 34 <appendix> argL0 ; # org/eclipse/equinox/internal/app/EclipseAppHandle$$Lambda+0x00000186101779c8
instanceKlass java/time/LocalTime$1
instanceKlass  @bci java/util/regex/Pattern CIRange (II)Ljava/util/regex/Pattern$CharPredicate; 2 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x00000186102da1a0
instanceKlass  @bci java/util/regex/Pattern SingleI (II)Ljava/util/regex/Pattern$BmpCharPredicate; 2 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x00000186102d9f10
instanceKlass java/time/format/Parsed
instanceKlass java/time/format/DateTimeParseContext
instanceKlass org/eclipse/jdt/ls/core/internal/LogReader$LogEntry
instanceKlass java/io/PrintWriter$1
instanceKlass jdk/internal/access/JavaIOPrintWriterAccess
instanceKlass org/eclipse/jdt/ls/core/internal/LogReader$LogSession
instanceKlass java/text/CalendarBuilder
instanceKlass java/text/ParsePosition
instanceKlass org/eclipse/jdt/ls/core/internal/LogReader
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ConcurrentMessageProcessor$1
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler <init> (Lorg/eclipse/jdt/ls/core/internal/managers/ProjectsManager;Lorg/eclipse/jdt/ls/core/internal/JavaClientConnection;Lorg/eclipse/jdt/ls/core/internal/handlers/BaseDocumentLifeCycleHandler;)V 35 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler$$Lambda+0x000001861031a2a0
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/WorkspaceEventsHandler
instanceKlass org/eclipse/lsp4j/jsonrpc/StandardLauncher
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ConcurrentMessageProcessor
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageProducer
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 80 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x00000186102d8ff0
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/EndpointProxy <init> (Lorg/eclipse/lsp4j/jsonrpc/Endpoint;Ljava/util/Collection;)V 197 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/EndpointProxy$$Lambda+0x000001861034fab8
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/EndpointProxy <init> (Lorg/eclipse/lsp4j/jsonrpc/Endpoint;Ljava/util/Collection;)V 178 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/EndpointProxy$$Lambda+0x000001861034f880
instanceKlass org/eclipse/lsp4j/jsonrpc/services/EndpointProxy
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/RemoteEndpoint <clinit> ()V 11 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/RemoteEndpoint$$Lambda+0x000001861034f408
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/ResponseError
instanceKlass org/eclipse/lsp4j/jsonrpc/RemoteEndpoint
instanceKlass  @bci java/lang/reflect/Executable sharedToString (IZ[Ljava/lang/Class;[Ljava/lang/Class;)Ljava/lang/String; 29 <appendix> argL0 ; # java/lang/reflect/Executable$$Lambda+0x00000186102d8db0
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 24 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x0000018610323b30
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint 220 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861034c000
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$1 (Ljava/lang/Object;Lorg/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$MethodInfo;)V 3 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x00000186103238e8
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 7 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint$$Lambda+0x00000186103236b0
instanceKlass org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/ParentProcessWatcher apply (Lorg/eclipse/lsp4j/jsonrpc/MessageConsumer;)Lorg/eclipse/lsp4j/jsonrpc/MessageConsumer; 2 <appendix> member <vmtarget> ; # org/eclipse/jdt/ls/core/internal/ParentProcessWatcher$$Lambda+0x0000018610319af0
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Message
instanceKlass org/eclipse/lsp4j/jsonrpc/json/StreamMessageConsumer
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MessageConstants
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory$BoundField
instanceKlass com/google/gson/internal/bind/ReflectiveTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory$DummyTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/JsonAdapterAnnotationTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/MapTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/CollectionTypeAdapterFactory
instanceKlass com/google/gson/internal/bind/ArrayTypeAdapter$1
instanceKlass com/google/gson/internal/bind/DefaultDateTypeAdapter$1
instanceKlass java/util/concurrent/atomic/AtomicLongArray
instanceKlass com/google/gson/internal/bind/NumberTypeAdapter$1
instanceKlass com/google/gson/internal/bind/ObjectTypeAdapter$1
instanceKlass com/google/gson/internal/bind/EnumTypeAdapter$1
instanceKlass com/google/gson/internal/bind/TypeAdapters$31
instanceKlass java/util/Currency
instanceKlass com/google/gson/internal/bind/TypeAdapters$32
instanceKlass java/util/concurrent/atomic/AtomicIntegerArray
instanceKlass com/google/gson/internal/bind/TypeAdapters$30
instanceKlass java/util/BitSet
instanceKlass com/google/gson/internal/bind/TypeAdapters$29
instanceKlass com/google/gson/internal/bind/TypeAdapters
instanceKlass com/google/gson/internal/ConstructorConstructor
instanceKlass com/google/gson/internal/sql/SqlTimestampTypeAdapter$1
instanceKlass com/google/gson/internal/sql/SqlTimeTypeAdapter$1
instanceKlass com/google/gson/internal/sql/SqlDateTypeAdapter$1
instanceKlass com/google/gson/internal/bind/DefaultDateTypeAdapter$DateType
instanceKlass com/google/gson/internal/sql/SqlTypesSupport
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/MessageTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EnumTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/TupleTypeAdapters$TwoTypeAdapterFactory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/EitherTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/ThrowableTypeAdapter$Factory
instanceKlass org/eclipse/lsp4j/jsonrpc/json/adapters/CollectionTypeAdapter$Factory
instanceKlass com/google/gson/FormattingStyle
instanceKlass com/google/gson/stream/JsonWriter
instanceKlass com/google/gson/stream/JsonReader
instanceKlass com/google/gson/ToNumberStrategy
instanceKlass com/google/gson/Gson
instanceKlass com/google/gson/internal/Excluder
instanceKlass com/google/gson/FieldNamingStrategy
instanceKlass com/google/gson/GsonBuilder
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/CancelParams
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MessageJsonHandler
instanceKlass org/eclipse/lsp4j/DidSaveNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidOpenNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidChangeNotebookDocumentParams
instanceKlass org/eclipse/lsp4j/DidCloseNotebookDocumentParams
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionResolveHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/NavigateToTypeDefinitionHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/NavigateToDefinitionHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/HoverHandler
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/DocumentSymbolHandler
instanceKlass org/eclipse/lsp4j/TextDocumentIdentifier
instanceKlass org/eclipse/lsp4j/WorkspaceDiagnosticReport
instanceKlass org/eclipse/lsp4j/adapters/WorkspaceSymbolResponseAdapter
instanceKlass org/eclipse/lsp4j/DeleteFilesParams
instanceKlass org/eclipse/lsp4j/RenameFilesParams
instanceKlass org/eclipse/lsp4j/CreateFilesParams
instanceKlass org/eclipse/lsp4j/WorkspaceSymbol
instanceKlass org/eclipse/lsp4j/DidChangeWorkspaceFoldersParams
instanceKlass org/eclipse/lsp4j/DidChangeWatchedFilesParams
instanceKlass org/eclipse/lsp4j/DidChangeConfigurationParams
instanceKlass org/eclipse/lsp4j/Hover
instanceKlass org/eclipse/lsp4j/Moniker
instanceKlass org/eclipse/lsp4j/Command
instanceKlass org/eclipse/lsp4j/adapters/CodeActionResponseAdapter
instanceKlass org/eclipse/lsp4j/adapters/DocumentDiagnosticReportTypeAdapter
instanceKlass org/eclipse/lsp4j/CompletionList
instanceKlass org/eclipse/lsp4j/TextEdit
instanceKlass org/eclipse/lsp4j/PrepareRenameDefaultBehavior
instanceKlass org/eclipse/lsp4j/PrepareRenameResult
instanceKlass org/eclipse/lsp4j/Range
instanceKlass org/eclipse/lsp4j/adapters/PrepareRenameResponseAdapter
instanceKlass org/eclipse/lsp4j/FoldingRange
instanceKlass org/eclipse/lsp4j/ColorPresentation
instanceKlass org/eclipse/lsp4j/LinkedEditingRanges
instanceKlass org/eclipse/lsp4j/InlineValueEvaluatableExpression
instanceKlass org/eclipse/lsp4j/InlineValueVariableLookup
instanceKlass org/eclipse/lsp4j/adapters/InlineValueResponseAdapter
instanceKlass org/eclipse/lsp4j/SelectionRange
instanceKlass org/eclipse/lsp4j/ColorInformation
instanceKlass org/eclipse/lsp4j/DocumentSymbol
instanceKlass org/eclipse/lsp4j/SymbolInformation
instanceKlass org/eclipse/lsp4j/adapters/DocumentSymbolResponseAdapter
instanceKlass org/eclipse/lsp4j/SignatureHelp
instanceKlass org/eclipse/lsp4j/SemanticTokensDelta
instanceKlass org/eclipse/lsp4j/adapters/SemanticTokensFullDeltaResponseAdapter
instanceKlass org/eclipse/lsp4j/SemanticTokens
instanceKlass org/eclipse/lsp4j/CallHierarchyIncomingCall
instanceKlass org/eclipse/lsp4j/CallHierarchyOutgoingCall
instanceKlass org/eclipse/lsp4j/CallHierarchyItem
instanceKlass org/eclipse/lsp4j/TypeHierarchyItem
instanceKlass org/eclipse/lsp4j/LocationLink
instanceKlass org/eclipse/lsp4j/Location
instanceKlass com/google/gson/internal/GsonTypes$WildcardTypeImpl
instanceKlass com/google/gson/internal/GsonPreconditions
instanceKlass com/google/gson/internal/GsonTypes$ParameterizedTypeImpl
instanceKlass com/google/gson/internal/GsonTypes
instanceKlass com/google/gson/TypeAdapter
instanceKlass com/google/gson/reflect/TypeToken
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller callOnce (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;Ljava/util/function/Consumer;)Z 14 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000018610186558
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder <clinit> ()V 9 <appendix> member <vmtarget> ; # org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder$$Lambda+0x000001861032e208
instanceKlass  @cpi org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder 59 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861032d000
instanceKlass org/eclipse/core/internal/content/ContentTypeManager$DebuggingHolder
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeBuilder applyPreferences (Lorg/eclipse/core/internal/content/ContentTypeCatalog;Lorg/eclipse/core/runtime/preferences/IEclipsePreferences;Z)V 4 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001861032cc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001861032c800
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeBuilder applyPreferences (Lorg/eclipse/core/internal/content/ContentTypeCatalog;Lorg/eclipse/core/runtime/preferences/IEclipsePreferences;Z)V 4 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861032c400
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeBuilder applyPreferences (Lorg/eclipse/core/internal/content/ContentTypeCatalog;Lorg/eclipse/core/runtime/preferences/IEclipsePreferences;Z)V 4 <appendix> member <vmtarget> ; # org/eclipse/core/internal/content/ContentTypeBuilder$$Lambda+0x0000018610171c58
instanceKlass  @cpi org/eclipse/core/internal/content/ContentTypeBuilder 424 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861032c000
instanceKlass org/eclipse/core/runtime/preferences/IPreferenceNodeVisitor
instanceKlass org/eclipse/lsp4j/adapters/LocationLinkListAdapter
instanceKlass com/google/gson/TypeAdapterFactory
instanceKlass org/eclipse/lsp4j/WorkspaceEdit
instanceKlass org/eclipse/core/internal/content/FileSpec
instanceKlass org/eclipse/lsp4j/DidSaveTextDocumentParams
instanceKlass org/eclipse/lsp4j/InlayHintParams
instanceKlass org/eclipse/core/internal/content/ContentType
instanceKlass org/eclipse/lsp4j/DocumentFormattingParams
instanceKlass org/eclipse/lsp4j/DidOpenTextDocumentParams
instanceKlass org/eclipse/core/internal/content/Util
instanceKlass org/eclipse/lsp4j/DidChangeTextDocumentParams
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 118 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x0000018610170db0
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 109 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x0000018610170b18
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 100 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x0000018610170880
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 91 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x00000186101705e8
instanceKlass  @bci org/eclipse/core/internal/content/ContentTypeCatalog <init> (Lorg/eclipse/core/internal/content/ContentTypeManager;I)V 82 <appendix> argL0 ; # org/eclipse/core/internal/content/ContentTypeCatalog$$Lambda+0x0000018610170350
instanceKlass org/eclipse/lsp4j/DidCloseTextDocumentParams
instanceKlass org/eclipse/lsp4j/WillSaveTextDocumentParams
instanceKlass org/eclipse/jdt/internal/core/index/Index
instanceKlass org/eclipse/lsp4j/DocumentRangeFormattingParams
instanceKlass org/eclipse/lsp4j/CodeAction
instanceKlass org/eclipse/lsp4j/InlayHint
instanceKlass org/eclipse/lsp4j/CodeLens
instanceKlass org/eclipse/lsp4j/InlineValueParams
instanceKlass org/eclipse/lsp4j/DocumentLink
instanceKlass org/eclipse/lsp4j/WorkDoneProgressAndPartialResultParams
instanceKlass sun/nio/cs/ThreadLocalCoders$Cache
instanceKlass sun/nio/cs/ThreadLocalCoders
instanceKlass org/eclipse/lsp4j/CompletionItem
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryType
instanceKlass org/eclipse/lsp4j/InitializeResult
instanceKlass org/eclipse/lsp4j/SetTraceParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressCancelParams
instanceKlass org/eclipse/lsp4j/services/NotebookDocumentService
instanceKlass org/eclipse/lsp4j/InitializedParams
instanceKlass org/eclipse/lsp4j/InitializeParams
instanceKlass org/eclipse/jdt/ls/core/internal/JavaClientConnection
instanceKlass org/eclipse/lsp4j/jsonrpc/CancelChecker
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass java/lang/Deprecated
instanceKlass org/eclipse/lsp4j/jsonrpc/json/JsonRpcMethodProvider
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonDelegate
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints getSupportedMethods (Ljava/lang/Class;Ljava/util/Set;)Ljava/util/Map; 29 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints$$Lambda+0x000001861030b358
instanceKlass org/eclipse/jdt/ls/core/internal/ActionableNotification
instanceKlass org/eclipse/jdt/core/search/SearchMatch
instanceKlass org/eclipse/jdt/ls/core/internal/StatusReport
instanceKlass org/eclipse/jdt/internal/compiler/lookup/IQualifiedTypeResolutionListener
instanceKlass org/eclipse/jdt/ls/core/internal/ProgressReport
instanceKlass org/eclipse/jdt/internal/core/search/matching/MatchLocator
instanceKlass org/eclipse/jdt/ls/core/internal/EventNotification
instanceKlass org/eclipse/lsp4j/ExecuteCommandParams
instanceKlass org/eclipse/jdt/internal/core/search/IndexSelector
instanceKlass org/eclipse/lsp4j/ShowDocumentResult
instanceKlass org/eclipse/jdt/core/search/SearchDocument
instanceKlass org/eclipse/jdt/internal/core/search/PatternSearchJob
instanceKlass org/eclipse/lsp4j/WorkspaceFolder
instanceKlass org/eclipse/lsp4j/MessageActionItem
instanceKlass org/eclipse/lsp4j/ApplyWorkspaceEditResponse
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonNotification
instanceKlass org/eclipse/jdt/internal/core/search/matching/TypeDeclarationPattern$PackageNameSet
instanceKlass org/eclipse/lsp4j/jsonrpc/json/JsonRpcMethod
instanceKlass org/eclipse/lsp4j/jsonrpc/json/ResponseJsonAdapter
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getParameterTypes (Ljava/lang/reflect/Method;)[Ljava/lang/reflect/Type; 17 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$$Lambda+0x000001861030a8a0
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getParameterTypes (Ljava/lang/reflect/Method;)[Ljava/lang/reflect/Type; 7 <appendix> argL0 ; # org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$$Lambda+0x000001861030a660
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonRequest
instanceKlass org/eclipse/lsp4j/PublishDiagnosticsParams
instanceKlass org/eclipse/lsp4j/RegistrationParams
instanceKlass org/eclipse/lsp4j/UnregistrationParams
instanceKlass org/eclipse/lsp4j/WorkDoneProgressCreateParams
instanceKlass org/eclipse/lsp4j/ShowDocumentParams
instanceKlass org/eclipse/lsp4j/ProgressParams
instanceKlass org/eclipse/lsp4j/MessageParams
instanceKlass org/eclipse/lsp4j/ApplyWorkspaceEditParams
instanceKlass org/eclipse/lsp4j/LogTraceParams
instanceKlass org/eclipse/jdt/internal/core/search/TypeNameRequestorWrapper
instanceKlass org/eclipse/jdt/internal/core/search/AbstractSearchScope
instanceKlass java/util/concurrent/CompletableFuture
instanceKlass org/eclipse/jdt/internal/core/search/IRestrictedAccessMethodRequestor
instanceKlass org/eclipse/jdt/core/search/ISearchPattern
instanceKlass org/eclipse/jdt/core/search/SearchEngine
instanceKlass org/eclipse/lsp4j/ConfigurationParams
instanceKlass org/eclipse/lsp4j/jsonrpc/services/JsonSegment
instanceKlass org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints getSupportedMethods (Ljava/lang/Class;Ljava/util/Set;)Ljava/util/Map; 11 <appendix> member <vmtarget> ; # org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints$$Lambda+0x0000018610309a18
instanceKlass org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil$MethodInfo
instanceKlass org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints
instanceKlass org/eclipse/lsp4j/jsonrpc/json/MethodProvider
instanceKlass org/eclipse/lsp4j/jsonrpc/Endpoint
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageProducer
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageConsumer
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageIssueHandler
instanceKlass org/eclipse/lsp4j/jsonrpc/Launcher$Builder
instanceKlass org/eclipse/lsp4j/jsonrpc/Launcher
instanceKlass org/eclipse/jdt/ls/core/internal/JavaClientConnection$JavaLanguageClient
instanceKlass org/eclipse/jdt/ls/core/internal/lsp/ExecuteCommandProposedClient
instanceKlass org/eclipse/lsp4j/services/LanguageClient
instanceKlass org/eclipse/jdt/internal/launching/JREContainer$1
instanceKlass org/eclipse/jdt/internal/launching/JREContainer
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager variableNames ()[Ljava/lang/String; 7 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x000001861027aea8
instanceKlass  @bci org/eclipse/jdt/launching/AbstractVMInstallType getVMInstalls ()[Lorg/eclipse/jdt/launching/IVMInstall; 4 <appendix> argL0 ; # org/eclipse/jdt/launching/AbstractVMInstallType$$Lambda+0x000001861030c1f8
instanceKlass  @bci org/eclipse/jdt/launching/VMStandin convertToRealVM ()Lorg/eclipse/jdt/launching/IVMInstall; 155 <appendix> member <vmtarget> ; # org/eclipse/jdt/launching/VMStandin$$Lambda+0x00000186103059a0
instanceKlass  @cpi org/eclipse/jdt/launching/VMStandin 173 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610308000
instanceKlass org/eclipse/jdt/launching/IVMRunner
instanceKlass org/eclipse/jdt/internal/launching/CompositeId
instanceKlass org/eclipse/jdt/ls/core/internal/javafx/FXLibraryLocationResolver
instanceKlass org/eclipse/jdt/internal/launching/JavaFxLibraryResolver
instanceKlass org/eclipse/jdt/launching/ILibraryLocationResolver
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType getLibraryLocationResolvers ()[Lorg/eclipse/jdt/launching/ILibraryLocationResolver; 27 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x00000186103075d0
instanceKlass org/eclipse/jdt/launching/LibraryLocation
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerProxy$1
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerSetter
instanceKlass org/eclipse/osgi/internal/url/NullURLStreamHandlerService
instanceKlass org/osgi/service/url/URLStreamHandlerSetter
instanceKlass  @bci org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl createInternalURLStreamHandler (Ljava/lang/String;)Ljava/net/URLStreamHandler; 18 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl$$Lambda+0x000001861029e9f8
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlFactoryHolder getHandler (Ljava/lang/String;)Ljava/lang/Object; 35 <appendix> member <vmtarget> ; # org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlFactoryHolder$$Lambda+0x000001861029e7b0
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$4
instanceKlass org/eclipse/jdt/internal/launching/LibraryInfo
instanceKlass org/eclipse/jdt/launching/AbstractVMInstall
instanceKlass org/eclipse/jdt/launching/IVMInstall3
instanceKlass org/eclipse/jdt/launching/IVMInstall2
instanceKlass sun/nio/ch/Invoker$GroupAndInvokeCount
instanceKlass sun/nio/ch/Invoker
instanceKlass org/eclipse/jdt/internal/launching/VMDefinitionsContainer
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType <clinit> ()V 78 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x00000186102bfab0
instanceKlass  @bci org/eclipse/jdt/internal/launching/StandardVMType <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/jdt/internal/launching/StandardVMType$$Lambda+0x00000186102bf890
instanceKlass org/eclipse/jdt/launching/AbstractVMInstallType
instanceKlass org/eclipse/jdt/launching/IVMInstallType
instanceKlass org/eclipse/debug/core/model/ISourceLocator
instanceKlass org/eclipse/jdt/internal/launching/sourcelookup/advanced/AdvancedSourceLookupSupport
instanceKlass org/eclipse/debug/internal/core/groups/GroupMemberChangeListener
instanceKlass org/eclipse/jdt/ls/core/internal/ParentProcessWatcher
instanceKlass sun/nio/ch/PendingIoCache
instanceKlass sun/nio/ch/AsynchronousChannelGroupImpl$1
instanceKlass sun/nio/ch/AsynchronousChannelGroupImpl$2
instanceKlass sun/nio/ch/Iocp$EventHandlerTask
instanceKlass  @bci sun/nio/ch/ThreadPool defaultThreadFactory ()Ljava/util/concurrent/ThreadFactory; 6 <appendix> argL0 ; # sun/nio/ch/ThreadPool$$Lambda+0x00000186102d1120
instanceKlass sun/nio/ch/ThreadPool
instanceKlass sun/nio/ch/Iocp$CompletionStatus
instanceKlass java/nio/channels/AsynchronousChannelGroup
instanceKlass sun/nio/ch/WindowsAsynchronousFileChannelImpl$DefaultIocpHolder
instanceKlass sun/nio/ch/Groupable
instanceKlass sun/nio/ch/Iocp$OverlappedChannel
instanceKlass java/nio/channels/AsynchronousFileChannel
instanceKlass org/eclipse/core/internal/watson/ElementTreeIterator
instanceKlass  @bci org/eclipse/core/internal/resources/Resource accept (Lorg/eclipse/core/resources/IResourceProxyVisitor;II)V 50 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610304c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610304800
instanceKlass  @bci org/eclipse/core/internal/resources/Resource accept (Lorg/eclipse/core/resources/IResourceProxyVisitor;II)V 50 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610304400
instanceKlass  @bci org/eclipse/core/internal/resources/Resource accept (Lorg/eclipse/core/resources/IResourceProxyVisitor;II)V 50 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/Resource$$Lambda+0x000001861020ef58
instanceKlass  @cpi org/eclipse/core/internal/resources/Resource 1633 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610304000
instanceKlass org/eclipse/core/internal/resources/ResourceProxy
instanceKlass org/eclipse/debug/internal/core/LaunchManager$ResourceProxyVisitor
instanceKlass org/eclipse/core/resources/IResourceProxyVisitor
instanceKlass org/eclipse/debug/core/ILaunchDelegate
instanceKlass org/eclipse/debug/core/ILaunchMode
instanceKlass org/eclipse/debug/core/ILaunchConfiguration
instanceKlass java/net/ProtocolFamily
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$PipeStreamProvider
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory getPipeFile ()Ljava/io/File; 58 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$$Lambda+0x00000186102b6d58
instanceKlass org/eclipse/jdt/launching/StandardClasspathProvider
instanceKlass org/eclipse/jdt/launching/environments/IExecutionEnvironmentsManager
instanceKlass org/eclipse/jdt/launching/IVMInstall
instanceKlass org/eclipse/jdt/launching/IVMConnector
instanceKlass org/eclipse/jdt/launching/IRuntimeClasspathEntry
instanceKlass org/eclipse/jdt/launching/IRuntimeClasspathEntryResolver
instanceKlass org/eclipse/jdt/launching/IRuntimeClasspathProvider
instanceKlass org/eclipse/jdt/launching/JavaRuntime
instanceKlass org/eclipse/jdt/internal/launching/LaunchingPlugin$1$1
instanceKlass org/eclipse/debug/core/model/IDebugElement
instanceKlass org/eclipse/debug/core/ILaunch
instanceKlass org/eclipse/debug/core/model/ISuspendResume
instanceKlass org/eclipse/debug/core/model/IStepFilters
instanceKlass org/eclipse/debug/core/model/IStep
instanceKlass org/eclipse/debug/core/model/IDropToFrame
instanceKlass  @bci org/eclipse/core/internal/runtime/AdapterManager registerFactory (Lorg/eclipse/core/runtime/IAdapterFactory;Ljava/lang/String;)V 5 <appendix> argL0 ; # org/eclipse/core/internal/runtime/AdapterManager$$Lambda+0x0000018610186318
instanceKlass org/eclipse/debug/core/model/IDisconnect
instanceKlass java/lang/ProcessHandleImpl$Info
instanceKlass java/lang/ProcessHandle$Info
instanceKlass  @bci java/lang/ProcessHandleImpl lambda$static$1 ()Ljava/util/concurrent/Executor; 45 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186102bc800
instanceKlass  @bci java/lang/ProcessHandleImpl lambda$static$1 ()Ljava/util/concurrent/Executor; 45 <appendix> member <vmtarget> ; # java/lang/ProcessHandleImpl$$Lambda+0x00000186102ceb58
instanceKlass org/eclipse/debug/internal/core/commands/ForEachCommand$ExclusiveRule
instanceKlass  @cpi java/lang/ProcessHandleImpl 436 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186102bc400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186102bc000
instanceKlass  @bci java/lang/ProcessHandleImpl <clinit> ()V 32 <appendix> argL0 ; # java/lang/ProcessHandleImpl$$Lambda+0x00000186102ce938
instanceKlass org/eclipse/debug/core/commands/AbstractDebugCommand
instanceKlass java/lang/ProcessHandleImpl
instanceKlass java/lang/ProcessHandle
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$StreamProvider
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory
instanceKlass org/eclipse/debug/core/commands/IStepFiltersHandler
instanceKlass org/eclipse/debug/core/commands/IResumeHandler
instanceKlass org/eclipse/jdt/core/manipulation/CoreASTProvider$WAIT_FLAG
instanceKlass org/eclipse/debug/core/commands/ISuspendHandler
instanceKlass org/eclipse/debug/core/commands/IDisconnectHandler
instanceKlass org/eclipse/debug/core/commands/IDropToFrameHandler
instanceKlass org/eclipse/debug/core/commands/IStepReturnHandler
instanceKlass org/eclipse/debug/core/commands/IStepIntoHandler
instanceKlass org/eclipse/debug/core/commands/IStepOverHandler
instanceKlass org/eclipse/debug/core/commands/ITerminateHandler
instanceKlass org/eclipse/debug/core/commands/IDebugCommandHandler
instanceKlass org/eclipse/jdt/core/manipulation/CoreASTProvider
instanceKlass org/eclipse/debug/internal/core/commands/CommandAdapterFactory
instanceKlass org/eclipse/jdt/ls/core/internal/MovingAverage
instanceKlass org/eclipse/debug/core/DebugPlugin$1$1
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BaseDiagnosticsHandler
instanceKlass org/eclipse/jdt/core/IProblemRequestor
instanceKlass org/eclipse/debug/internal/core/DebugOptions
instanceKlass org/eclipse/debug/core/DebugPlugin$AsynchRunner
instanceKlass org/eclipse/debug/core/DebugPlugin$EventNotifier
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BaseDocumentLifeCycleHandler
instanceKlass org/eclipse/lsp4j/PartialResultParams
instanceKlass org/eclipse/debug/core/model/IProcess
instanceKlass org/eclipse/debug/core/model/ITerminate
instanceKlass org/eclipse/lsp4j/WorkDoneProgressParams
instanceKlass org/eclipse/debug/core/ILaunchManager
instanceKlass org/eclipse/debug/core/ILaunchConfigurationListener
instanceKlass org/eclipse/debug/core/IExpressionManager
instanceKlass org/eclipse/debug/core/IBreakpointManager
instanceKlass org/eclipse/lsp4j/TextDocumentPositionParams
instanceKlass org/eclipse/debug/core/IMemoryBlockManager
instanceKlass org/eclipse/debug/core/IDebugEventSetListener
instanceKlass java/util/concurrent/Executors$DefaultThreadFactory
instanceKlass org/eclipse/jdt/ls/core/internal/ProjectUtils
instanceKlass org/eclipse/debug/core/ILaunchesListener
instanceKlass org/eclipse/jdt/launching/IVMInstallChangedListener
instanceKlass com/google/gson/JsonElement
instanceKlass org/eclipse/jdt/core/ClasspathContainerInitializer
instanceKlass org/eclipse/jdt/ls/core/internal/managers/TelemetryManager
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$11
instanceKlass org/eclipse/core/runtime/jobs/ProgressProvider
instanceKlass org/eclipse/jdt/ls/core/internal/LanguageServerApplication
instanceKlass org/eclipse/equinox/app/IApplication
instanceKlass org/eclipse/core/runtime/internal/adaptor/EclipseAppLauncher
instanceKlass org/eclipse/jdt/ls/core/internal/JobHelpers$ProjectRegistryRefreshJobMatcher
instanceKlass org/eclipse/jdt/ls/core/internal/JobHelpers$IJobMatcher
instanceKlass org/eclipse/jdt/ls/core/internal/JobHelpers
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/BundleUtils
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferencesWrapper
instanceKlass org/eclipse/equinox/security/storage/ISecurePreferences
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferencesContainer
instanceKlass org/eclipse/equinox/internal/security/storage/PersistedPath
instanceKlass  @bci org/eclipse/equinox/internal/security/storage/SecurePreferencesRoot load ()V 241 <appendix> member <vmtarget> ; # org/eclipse/equinox/internal/security/storage/SecurePreferencesRoot$$Lambda+0x00000186102a8b38
instanceKlass javax/crypto/spec/PBEKeySpec
instanceKlass org/eclipse/equinox/internal/security/storage/PasswordExt
instanceKlass org/eclipse/equinox/internal/security/storage/JavaEncryption
instanceKlass org/eclipse/equinox/security/storage/provider/IPreferencesContainer
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferences
instanceKlass org/eclipse/equinox/internal/security/storage/friends/IStorageConstants
instanceKlass org/eclipse/equinox/internal/security/storage/StorageUtils
instanceKlass org/eclipse/equinox/internal/security/storage/SecurePreferencesMapper
instanceKlass org/eclipse/equinox/security/storage/SecurePreferencesFactory
instanceKlass com/sun/jna/Function$PostCallRead
instanceKlass com/sun/jna/NativeString
instanceKlass com/sun/jna/Library$Handler$FunctionInfo
instanceKlass com/sun/jna/internal/ReflectionUtils
instanceKlass com/sun/jna/Native$3
instanceKlass com/sun/jna/Library$Handler
instanceKlass com/sun/jna/win32/W32APIFunctionMapper
instanceKlass com/sun/jna/FunctionMapper
instanceKlass com/sun/jna/win32/W32APIOptions
instanceKlass org/eclipse/core/net/internal/proxy/win32/ProxyProviderWin32$WinHttp
instanceKlass com/sun/jna/win32/StdCallLibrary
instanceKlass com/sun/jna/win32/StdCall
instanceKlass com/sun/jna/AltCallingConvention
instanceKlass org/eclipse/core/internal/net/ProxyData
instanceKlass org/eclipse/equinox/internal/security/auth/AuthPlugin
instanceKlass org/eclipse/core/internal/net/ProxyType
instanceKlass org/eclipse/core/net/proxy/IProxyChangeEvent
instanceKlass org/eclipse/core/internal/net/AbstractProxyProvider
instanceKlass org/eclipse/core/internal/net/ProxyManager
instanceKlass org/eclipse/core/net/proxy/IProxyService
instanceKlass  @bci org/eclipse/core/internal/net/Policy <clinit> ()V 8 <appendix> argL0 ; # org/eclipse/core/internal/net/Policy$$Lambda+0x000001861029d618
instanceKlass org/eclipse/core/internal/net/Policy
instanceKlass org/eclipse/core/net/proxy/IProxyData
instanceKlass org/eclipse/core/internal/net/PreferenceManager
instanceKlass org/eclipse/core/internal/net/Activator
instanceKlass org/eclipse/core/internal/net/ProxySelector
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogEntryImpl
instanceKlass org/eclipse/equinox/log/ExtendedLogEntry
instanceKlass java/lang/StackTraceElement$HashedModules
instanceKlass org/eclipse/osgi/framework/log/FrameworkLogEntry
instanceKlass org/eclipse/jdt/ls/core/internal/DiagnosticsState
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ContentProviderManager
instanceKlass org/eclipse/jdt/ls/core/internal/managers/DigestStore
instanceKlass org/eclipse/lsp4j/jsonrpc/messages/Either
instanceKlass org/eclipse/text/templates/TemplateStoreCore
instanceKlass com/sun/org/apache/xml/internal/serializer/WriterChain
instanceKlass com/sun/org/apache/xalan/internal/xsltc/trax/DOM2TO
instanceKlass javax/xml/transform/stax/StAXSource
instanceKlass javax/xml/transform/sax/SAXSource
instanceKlass javax/xml/transform/stream/StreamSource
instanceKlass com/sun/org/apache/xml/internal/serializer/NamespaceMappings$MappingRecord
instanceKlass com/sun/org/apache/xml/internal/serializer/NamespaceMappings
instanceKlass java/nio/charset/Charset$1
instanceKlass java/nio/charset/Charset$2
instanceKlass java/nio/charset/Charset$ThreadTrackHolder
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder$1
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder
instanceKlass  @bci jdk/xml/internal/SecuritySupport getResourceAsStream (Ljava/lang/String;)Ljava/io/InputStream; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000186102c8210
instanceKlass com/sun/org/apache/xml/internal/serializer/Encodings$EncodingInfos
instanceKlass com/sun/org/apache/xml/internal/serializer/Encodings
instanceKlass com/sun/org/apache/xml/internal/serializer/ToStream$CharacterBuffer
instanceKlass com/sun/org/apache/xml/internal/serializer/EncodingInfo
instanceKlass com/sun/org/apache/xml/internal/serializer/ToStream$BoolStack
instanceKlass com/sun/org/apache/xml/internal/serializer/ElemContext
instanceKlass org/xml/sax/helpers/AttributesImpl
instanceKlass com/sun/org/apache/xml/internal/serializer/CharInfo$CharKey
instanceKlass sun/util/ResourceBundleEnumeration
instanceKlass com/sun/org/apache/xml/internal/serializer/CharInfo
instanceKlass com/sun/org/apache/xml/internal/serializer/SerializerBase
instanceKlass com/sun/org/apache/xml/internal/serializer/SerializerConstants
instanceKlass com/sun/org/apache/xml/internal/serializer/SerializationHandler
instanceKlass com/sun/org/apache/xml/internal/serializer/Serializer
instanceKlass com/sun/org/apache/xml/internal/serializer/DOMSerializer
instanceKlass org/xml/sax/ext/DeclHandler
instanceKlass com/sun/org/apache/xml/internal/serializer/XSLOutputAttributes
instanceKlass com/sun/org/apache/xml/internal/serializer/ExtendedLexicalHandler
instanceKlass org/xml/sax/ext/LexicalHandler
instanceKlass com/sun/org/apache/xml/internal/serializer/ExtendedContentHandler
instanceKlass javax/xml/transform/dom/DOMResult
instanceKlass javax/xml/transform/stax/StAXResult
instanceKlass javax/xml/transform/sax/SAXResult
instanceKlass com/sun/org/apache/xalan/internal/xsltc/runtime/output/TransletOutputHandlerFactory
instanceKlass javax/xml/transform/dom/DOMSource
instanceKlass com/sun/org/apache/xml/internal/utils/XMLReaderManager
instanceKlass com/sun/org/apache/xml/internal/serializer/OutputPropertiesFactory
instanceKlass javax/xml/transform/Transformer
instanceKlass com/sun/org/apache/xalan/internal/xsltc/DOMCache
instanceKlass  @bci javax/xml/catalog/CatalogFeatures setProperties (Ljavax/xml/catalog/CatalogFeatures$Builder;)V 5 <appendix> member <vmtarget> ; # javax/xml/catalog/CatalogFeatures$$Lambda+0x00000186102c1700
instanceKlass javax/xml/catalog/CatalogMessages
instanceKlass javax/xml/catalog/Util
instanceKlass  @bci javax/xml/transform/FactoryFinder newInstance (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/ClassLoader;Z)Ljava/lang/Object; 107 <appendix> member <vmtarget> ; # javax/xml/transform/FactoryFinder$$Lambda+0x00000186102c0c88
instanceKlass jdk/xml/internal/JdkProperty
instanceKlass jdk/xml/internal/XMLSecurityManager
instanceKlass com/sun/org/apache/xalan/internal/utils/FeaturePropertyBase
instanceKlass jdk/xml/internal/JdkXmlFeatures
instanceKlass javax/xml/catalog/CatalogFeatures$Builder
instanceKlass javax/xml/catalog/CatalogFeatures
instanceKlass jdk/xml/internal/TransformErrorListener
instanceKlass javax/xml/transform/ErrorListener
instanceKlass com/sun/org/apache/xalan/internal/xsltc/compiler/SourceLoader
instanceKlass  @bci javax/xml/transform/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 123 <appendix> member <vmtarget> ; # javax/xml/transform/FactoryFinder$$Lambda+0x00000186101fd130
instanceKlass javax/xml/transform/FactoryFinder$1
instanceKlass  @bci javax/xml/transform/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 24 <appendix> member <vmtarget> ; # javax/xml/transform/FactoryFinder$$Lambda+0x00000186101fccd8
instanceKlass javax/xml/transform/FactoryFinder
instanceKlass javax/xml/transform/TransformerFactory
instanceKlass javax/xml/transform/stream/StreamResult
instanceKlass javax/xml/transform/Source
instanceKlass javax/xml/transform/Result
instanceKlass org/eclipse/text/templates/TemplateReaderWriter
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x00000186101fbd30
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x00000186101fbb00
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x00000186101fb8e0
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager reloadTemplateStore ()V 24 <appendix> argL0 ; # org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager$$Lambda+0x000001861028cca8
instanceKlass org/eclipse/text/templates/TemplatePersistenceData
instanceKlass org/eclipse/jface/text/templates/Template
instanceKlass java/util/ResourceBundle$Control$2
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleControlProviderHolder lambda$static$0 ()Ljava/util/List; 11 <appendix> argL0 ; # java/util/ResourceBundle$ResourceBundleControlProviderHolder$$Lambda+0x00000186101fb468
instanceKlass java/util/ServiceLoader$ProviderSpliterator
instanceKlass java/util/spi/ResourceBundleControlProvider
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleControlProviderHolder <clinit> ()V 0 <appendix> argL0 ; # java/util/ResourceBundle$ResourceBundleControlProviderHolder$$Lambda+0x00000186101fada8
instanceKlass java/util/ResourceBundle$ResourceBundleControlProviderHolder
instanceKlass org/eclipse/jface/text/templates/TextTemplateMessages
instanceKlass org/eclipse/jface/text/IRegion
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/FormatterHandler
instanceKlass org/apache/commons/lang3/text/StrTokenizer
instanceKlass org/apache/commons/lang3/text/StrBuilder
instanceKlass org/apache/commons/lang3/builder/Builder
instanceKlass  @bci java/util/regex/CharPredicates forUnicodeBlock (Ljava/lang/String;)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/CharPredicates$$Lambda+0x00000186101fa2c0
instanceKlass java/lang/Character$Subset
instanceKlass org/apache/commons/lang3/StringUtils
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/apache/commons/lang3/ArraySorter
instanceKlass org/apache/commons/lang3/text/StrMatcher
instanceKlass org/apache/commons/lang3/text/StrSubstitutor
instanceKlass org/apache/commons/lang3/text/StrLookup
instanceKlass org/eclipse/osgi/storage/bundlefile/ZipBundleFile$1
instanceKlass  @bci org/eclipse/osgi/storage/bundlefile/ZipBundleFile getPaths ()Ljava/lang/Iterable; 1 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/bundlefile/ZipBundleFile$$Lambda+0x000001861014f548
instanceKlass org/eclipse/jdt/ls/core/internal/ResourceUtils
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$ReferencedLibraries
instanceKlass java/util/UUID$Holder
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences
instanceKlass org/eclipse/jface/text/templates/TemplateContextType
instanceKlass org/eclipse/jface/text/templates/TemplateVariableResolver
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder
instanceKlass sun/util/resources/provider/NonBaseLocaleDataMetaInfo
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter createSupportedLocaleString (Ljava/lang/String;)Ljava/lang/String; 6 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x00000186101f83d0
instanceKlass sun/util/locale/provider/BaseLocaleDataMetaInfo
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getTimeZoneNameProvider ()Ljava/util/spi/TimeZoneNameProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x00000186101f7f50
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getTimeZoneNameProvider ()Ljava/util/spi/TimeZoneNameProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x00000186101f78a8
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass sun/nio/cs/Surrogate
instanceKlass sun/nio/cs/Surrogate$Parser
instanceKlass  @bci org/eclipse/core/internal/preferences/SortedProperties <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/core/internal/preferences/SortedProperties$$Lambda+0x00000186101a94e8
instanceKlass  @bci org/eclipse/core/runtime/Plugin savePluginPreferences ()V 41 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/Plugin$$Lambda+0x00000186101c6c10
instanceKlass org/eclipse/core/runtime/Preferences$1
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences firePreferenceEvent (Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V 41 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x00000186101a92b0
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/eclipse/jdt/ls/core/internal/Environment
instanceKlass org/eclipse/jdt/ls/core/internal/JDTEnvironmentUtils
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/LogHandler$1
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatSymbolsProvider ()Ljava/text/spi/DateFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000065
instanceKlass java/text/DateFormatSymbols
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x800000069
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x800000068
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000062
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Calendar$Builder
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000063
instanceKlass java/util/Calendar
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatProvider ()Ljava/text/spi/DateFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000064
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/DefaultLogFilter
instanceKlass org/eclipse/core/runtime/ILogListener
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/LogHandler
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610285000
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610284c00
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences fireNodeEvent (Lorg/eclipse/core/runtime/preferences/IEclipsePreferences$NodeChangeEvent;Z)V 26 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/EclipsePreferences$$Lambda+0x00000186101a9078
instanceKlass  @cpi org/eclipse/core/internal/preferences/EclipsePreferences 1057 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610284800
instanceKlass org/eclipse/jdt/internal/core/manipulation/MembersOrderPreferenceCacheCommon
instanceKlass org/eclipse/jdt/core/manipulation/JavaManipulation
instanceKlass java/nio/channels/AsynchronousByteChannel
instanceKlass java/nio/channels/AsynchronousChannel
instanceKlass java/net/SocketAddress
instanceKlass org/eclipse/jdt/ls/core/internal/syntaxserver/IExtendedProtocol
instanceKlass java/net/Authenticator
instanceKlass org/eclipse/jdt/ls/core/internal/lsp/JavaProtocolExtensions
instanceKlass org/eclipse/lsp4j/services/WorkspaceService
instanceKlass org/eclipse/lsp4j/services/TextDocumentService
instanceKlass org/eclipse/lsp4j/services/LanguageServer
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$2
instanceKlass org/eclipse/core/internal/events/NotificationManager$1
instanceKlass org/eclipse/jdt/ls/core/internal/BaseJDTLanguageServer
instanceKlass org/eclipse/jdt/internal/core/ModelUpdater
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$21$1
instanceKlass  @bci org/eclipse/jdt/internal/core/search/processing/JobManager reset ()V 38 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/processing/JobManager$$Lambda+0x0000018610273a08
instanceKlass org/eclipse/jdt/internal/core/ExternalAnnotationTracker$DirectoryNode
instanceKlass org/eclipse/jdt/internal/core/ExternalAnnotationTracker
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$RootInfo
instanceKlass org/eclipse/jdt/internal/core/JavaProject$ResolvedClasspath
instanceKlass org/eclipse/jdt/internal/core/ClasspathChange
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper loadPropertyResourceBundle (Ljava/lang/Module;Ljava/lang/Module;Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 14 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x00000186101f46d0
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper loadResourceBundle (Ljava/lang/Module;Ljava/lang/Module;Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 13 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x00000186101f44a8
instanceKlass java/util/ResourceBundle$3
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass  @bci java/util/ResourceBundle getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/util/ResourceBundle$$Lambda+0x00000186101f39c8
instanceKlass  @bci jdk/xml/internal/SecuritySupport getResourceBundle (Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 2 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000186101f37a0
instanceKlass com/sun/org/apache/xerces/internal/dom/DOMMessageFormatter
instanceKlass org/w3c/dom/Attr
instanceKlass com/sun/org/apache/xerces/internal/dom/NamedNodeMapImpl
instanceKlass org/w3c/dom/NamedNodeMap
instanceKlass com/sun/org/apache/xerces/internal/dom/CharacterDataImpl$1
instanceKlass org/w3c/dom/Text
instanceKlass org/w3c/dom/CharacterData
instanceKlass com/sun/org/apache/xerces/internal/dom/DeepNodeListImpl
instanceKlass com/sun/org/apache/xerces/internal/dom/DeferredDocumentImpl$RefCount
instanceKlass com/sun/org/apache/xerces/internal/dom/NodeListCache
instanceKlass org/w3c/dom/TypeInfo
instanceKlass org/w3c/dom/ElementTraversal
instanceKlass org/w3c/dom/Element
instanceKlass org/w3c/dom/DocumentType
instanceKlass com/sun/org/apache/xerces/internal/dom/NodeImpl
instanceKlass org/w3c/dom/events/EventTarget
instanceKlass org/w3c/dom/NodeList
instanceKlass org/w3c/dom/Document
instanceKlass org/w3c/dom/ranges/DocumentRange
instanceKlass org/w3c/dom/events/DocumentEvent
instanceKlass org/w3c/dom/traversal/DocumentTraversal
instanceKlass com/sun/org/apache/xerces/internal/dom/DeferredNode
instanceKlass org/w3c/dom/Node
instanceKlass org/eclipse/jdt/internal/core/util/Util
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$SecondaryTypes
instanceKlass org/eclipse/jdt/core/IJavaModelStatusConstants
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610279400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610279000
instanceKlass org/eclipse/jdt/internal/compiler/ast/TypeOrLambda
instanceKlass org/eclipse/jdt/internal/compiler/CompilationResult
instanceKlass lombok/eclipse/agent/PatchDelegate$BindingTuple
instanceKlass org/eclipse/jdt/internal/compiler/ast/Invocation
instanceKlass org/eclipse/jdt/internal/compiler/ast/IPolyExpression
instanceKlass lombok/eclipse/agent/PatchDelegate
instanceKlass lombok/eclipse/agent/PatchDelegatePortal
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$Delegate
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState$RootInfos
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$PersistedClasspathContainer
instanceKlass org/eclipse/jdt/internal/core/ClasspathAttribute
instanceKlass org/eclipse/jdt/internal/core/ClasspathEntry
instanceKlass org/eclipse/jdt/core/eval/IEvaluationContext
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$PerProjectInfo
instanceKlass org/eclipse/jdt/internal/compiler/env/IModulePathEntry
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$VariablesAndContainersLoadHelper
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$20
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$19
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$14
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$13
instanceKlass org/eclipse/jdt/internal/formatter/DefaultCodeFormatterOptions
instanceKlass org/eclipse/jdt/core/formatter/DefaultCodeFormatterConstants
instanceKlass org/eclipse/jdt/internal/compiler/util/Util
instanceKlass org/eclipse/jdt/internal/compiler/impl/IrritantSet
instanceKlass org/eclipse/jdt/internal/core/util/ICacheEnumeration
instanceKlass org/eclipse/jdt/internal/formatter/TokenTraverser
instanceKlass org/eclipse/text/edits/TextEdit
instanceKlass org/eclipse/jdt/core/formatter/CodeFormatter
instanceKlass lombok/patcher/scripts/WrapperMethodDescriptor
instanceKlass lombok/patcher/scripts/SetSymbolDuringMethodCallScript$1
instanceKlass org/eclipse/jdt/core/SourceRange
instanceKlass org/eclipse/jdt/internal/compiler/util/JRTUtil$JrtFileVisitor
instanceKlass org/eclipse/jdt/core/IOrdinaryClassFile
instanceKlass org/eclipse/jdt/internal/core/util/ReferenceInfoAdapter
instanceKlass org/eclipse/jdt/internal/compiler/ISourceElementRequestor
instanceKlass org/eclipse/jdt/core/search/TypeNameMatchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/env/ISourceType
instanceKlass org/eclipse/jdt/internal/compiler/env/IGenericType
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemHandler
instanceKlass org/eclipse/jdt/core/search/SearchParticipant
instanceKlass org/eclipse/jdt/core/search/TypeNameMatch
instanceKlass org/eclipse/jdt/core/search/MethodNameMatch
instanceKlass org/eclipse/jdt/internal/compiler/ASTVisitor
instanceKlass org/eclipse/jdt/core/search/IParallelizable
instanceKlass org/eclipse/jdt/core/search/SearchPattern
instanceKlass org/eclipse/jdt/internal/core/search/IndexQueryRequestor
instanceKlass org/eclipse/jdt/core/search/IJavaSearchScope
instanceKlass org/eclipse/jdt/internal/core/search/BasicSearchEngine
instanceKlass org/eclipse/jdt/core/ITypeParameter
instanceKlass org/eclipse/jdt/core/ISourceRange
instanceKlass org/eclipse/jdt/core/IAnnotation
instanceKlass org/eclipse/jdt/internal/core/AbstractModule
instanceKlass org/eclipse/jdt/core/IModuleDescription
instanceKlass org/eclipse/jdt/internal/core/NameLookup
instanceKlass org/eclipse/jface/text/IDocument
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache$1
instanceKlass org/eclipse/jdt/internal/compiler/env/IBinaryInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelCache
instanceKlass org/eclipse/jdt/core/IType
instanceKlass org/eclipse/jdt/core/IAnnotatable
instanceKlass org/eclipse/jdt/core/IMember
instanceKlass org/eclipse/jdt/internal/core/hierarchy/HierarchyBuilder
instanceKlass org/eclipse/jdt/internal/core/hierarchy/TypeHierarchy
instanceKlass org/eclipse/jdt/core/ITypeHierarchy
instanceKlass org/eclipse/jdt/core/dom/ASTNode
instanceKlass org/eclipse/jdt/core/dom/StructuralPropertyDescriptor
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEvent
instanceKlass org/eclipse/jdt/internal/core/dom/rewrite/RewriteEventStore
instanceKlass org/eclipse/jdt/core/dom/ASTVisitor
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor
instanceKlass org/eclipse/jdt/internal/codeassist/CompletionEngine$1
instanceKlass  @bci org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding <clinit> ()V 58 <appendix> argL0 ; # org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$$Lambda+0x000001861024bc08
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ParameterNonNullDefaultProvider
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$3
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$2
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReductionResult
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ElementValuePair
instanceKlass org/eclipse/jdt/internal/compiler/lookup/AnnotationBinding
instanceKlass org/eclipse/jdt/internal/compiler/env/IUpdatableModule
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeBindingVisitor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/HotSwappable
instanceKlass org/eclipse/jdt/internal/compiler/parser/ScannerHelper
instanceKlass org/eclipse/jdt/core/Signature
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants$CloseMethodRecord
instanceKlass org/eclipse/jdt/internal/core/IJavaElementRequestor
instanceKlass org/eclipse/jdt/internal/core/INamingRequestor
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Substitution
instanceKlass org/eclipse/jdt/core/search/SearchRequestor
instanceKlass org/eclipse/jdt/core/CompletionRequestor
instanceKlass lombok/patcher/scripts/ReplaceMethodCallScript$1
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNode
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Binding
instanceKlass org/eclipse/jdt/internal/codeassist/MissingTypesGuesser$GuessedTypeRequestor
instanceKlass org/eclipse/jdt/internal/codeassist/UnresolvedReferenceNameFinder$UnresolvedReferenceNameRequestor
instanceKlass org/eclipse/jdt/core/CompletionProposal
instanceKlass org/eclipse/jdt/core/compiler/IProblem
instanceKlass org/eclipse/jdt/internal/compiler/lookup/Scope
instanceKlass org/eclipse/jdt/core/CompletionContext
instanceKlass org/eclipse/jdt/internal/compiler/env/INameEnvironment
instanceKlass org/eclipse/jdt/internal/compiler/lookup/InvocationSite
instanceKlass org/eclipse/jdt/internal/compiler/ast/ASTNode
instanceKlass org/eclipse/jdt/internal/codeassist/impl/Engine
instanceKlass org/eclipse/jdt/internal/codeassist/ICompletionEngine
instanceKlass org/eclipse/jdt/internal/codeassist/RelevanceConstants
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeConstants
instanceKlass org/eclipse/jdt/internal/codeassist/ISearchRequestor
instanceKlass org/eclipse/jdt/internal/compiler/impl/ReferenceContext
instanceKlass org/eclipse/jdt/internal/compiler/ICompilerRequestor
instanceKlass org/eclipse/jdt/internal/compiler/Compiler
instanceKlass org/eclipse/jdt/internal/compiler/problem/ProblemSeverities
instanceKlass org/eclipse/jdt/internal/compiler/impl/ITypeRequestor
instanceKlass org/eclipse/jdt/internal/core/builder/ClasspathLocation
instanceKlass org/eclipse/jdt/core/IBuffer
instanceKlass org/eclipse/jdt/core/IBufferFactory
instanceKlass org/eclipse/jdt/internal/core/BufferManager
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$8
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager <clinit> ()V 139 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x0000018610223940
instanceKlass org/eclipse/core/internal/jobs/JobQueue$2
instanceKlass  @bci org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry <init> (Ljava/io/File;Lorg/eclipse/core/runtime/IPath;)V 24 <appendix> member <vmtarget> ; # org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry$$Lambda+0x0000018610223718
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexNamesRegistry
instanceKlass org/eclipse/jdt/internal/compiler/util/SimpleLookupTable
instanceKlass org/eclipse/jdt/internal/compiler/parser/Parser
instanceKlass org/eclipse/jdt/internal/compiler/lookup/TypeIds
instanceKlass org/eclipse/jdt/internal/compiler/ast/OperatorIds
instanceKlass org/eclipse/jdt/internal/compiler/parser/ConflictedParser
instanceKlass org/eclipse/jdt/internal/compiler/parser/ParserBasicInformation
instanceKlass org/eclipse/jdt/internal/compiler/IProblemFactory
instanceKlass org/eclipse/jdt/internal/core/index/IndexLocation
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IndexRequest
instanceKlass org/eclipse/jdt/internal/core/search/processing/JobManager
instanceKlass org/eclipse/jdt/internal/core/search/indexing/IIndexConstants
instanceKlass  @bci org/eclipse/jdt/internal/core/JavaModelManager <init> ()V 404 <appendix> argL0 ; # org/eclipse/jdt/internal/core/JavaModelManager$$Lambda+0x0000018610220000
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$3
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$2
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$EclipsePreferencesListener
instanceKlass org/eclipse/jdt/core/IElementChangedListener
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessingState
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager wakeUp (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 23 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000001861018b280
instanceKlass org/eclipse/jdt/internal/core/ExternalFoldersManager
instanceKlass org/eclipse/jdt/internal/core/util/Util$Comparer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$CompilationParticipants
instanceKlass org/eclipse/jdt/internal/core/BatchInitializationMonitor
instanceKlass org/eclipse/jdt/internal/core/JavaModelOperation
instanceKlass org/eclipse/jdt/internal/codeassist/ISelectionRequestor
instanceKlass org/eclipse/jdt/internal/core/JavaElementInfo
instanceKlass org/eclipse/jdt/core/IJavaModelStatus
instanceKlass org/eclipse/jdt/internal/compiler/env/IElementInfo
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$1
instanceKlass org/eclipse/jdt/internal/core/util/LRUCache
instanceKlass org/eclipse/jdt/internal/core/search/IRestrictedAccessTypeRequestor
instanceKlass org/eclipse/jdt/core/IJavaElementDelta
instanceKlass org/eclipse/jdt/internal/compiler/util/SuffixConstants
instanceKlass org/eclipse/jdt/internal/compiler/env/ICompilationUnit
instanceKlass org/eclipse/jdt/internal/compiler/env/IDependent
instanceKlass org/eclipse/jdt/core/ICompilationUnit
instanceKlass org/lombokweb/asm/Handle
instanceKlass org/eclipse/jdt/core/IBufferChangedListener
instanceKlass org/eclipse/jdt/core/IPackageFragmentRoot
instanceKlass org/eclipse/jdt/core/IClassFile
instanceKlass org/eclipse/jdt/core/ITypeRoot
instanceKlass org/eclipse/jdt/core/ICodeAssist
instanceKlass org/eclipse/jdt/core/ISourceReference
instanceKlass org/eclipse/jdt/core/IPackageFragment
instanceKlass org/eclipse/jdt/core/ISourceManipulation
instanceKlass org/eclipse/jdt/internal/core/search/processing/IJob
instanceKlass org/eclipse/jdt/core/IAccessRule
instanceKlass org/eclipse/jdt/internal/compiler/util/Util$Displayable
instanceKlass org/eclipse/jdt/core/IJavaProject
instanceKlass org/eclipse/jdt/core/IClasspathContainer
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager
instanceKlass  @bci java/util/Comparator comparingDouble (Ljava/util/function/ToDoubleFunction;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x00000186101e79f0
instanceKlass  @bci org/eclipse/jdt/core/JavaCore <clinit> ()V 200 <appendix> argL0 ; # org/eclipse/jdt/core/JavaCore$$Lambda+0x0000018610211200
instanceKlass  @cpi org/eclipse/jdt/core/JavaCore 2471 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610212400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610212000
instanceKlass java/util/function/ToDoubleFunction
instanceKlass org/eclipse/jdt/core/compiler/CharOperation
instanceKlass org/eclipse/jdt/internal/compiler/impl/CompilerOptions
instanceKlass org/eclipse/jdt/core/IClasspathAttribute
instanceKlass org/eclipse/jdt/internal/compiler/env/IModule
instanceKlass org/eclipse/jdt/core/IWorkingCopy
instanceKlass org/eclipse/jdt/core/IRegion
instanceKlass org/eclipse/jdt/core/search/TypeNameRequestor
instanceKlass org/eclipse/jdt/core/IClasspathEntry
instanceKlass org/eclipse/jdt/core/IJavaModel
instanceKlass org/eclipse/jdt/core/IParent
instanceKlass org/eclipse/jdt/core/IOpenable
instanceKlass org/eclipse/jdt/core/IJavaElement
instanceKlass org/eclipse/core/resources/IWorkspaceRunnable
instanceKlass org/eclipse/core/internal/events/NodeIDMap
instanceKlass org/eclipse/core/internal/events/ResourceDeltaInfo
instanceKlass org/eclipse/jdt/core/WorkingCopyOwner
instanceKlass org/eclipse/core/internal/dtree/NodeComparison
instanceKlass org/eclipse/core/internal/events/ResourceComparator
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ISourceDownloader
instanceKlass org/eclipse/core/internal/events/ResourceDeltaFactory
instanceKlass org/eclipse/core/resources/IMarkerDelta
instanceKlass org/eclipse/text/templates/ContextTypeRegistry
instanceKlass org/eclipse/jdt/ls/core/contentassist/ICompletionContributionService
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/PreferenceManager
instanceKlass org/eclipse/core/runtime/SubMonitor$RootInfo
instanceKlass org/eclipse/core/runtime/SubMonitor
instanceKlass org/eclipse/core/resources/team/ResourceRuleFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610209000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610208c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610208800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610208400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610208000
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$KeyIterator
instanceKlass org/eclipse/core/resources/IResourceChangeEvent
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass  @bci org/apache/felix/scr/impl/manager/ComponentContextImpl createNewFieldHandlerMap ()Ljava/util/Map; 4 <appendix> argL0 ; # org/apache/felix/scr/impl/manager/ComponentContextImpl$$Lambda+0x0000018610206000
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getLog (Lorg/osgi/framework/Bundle;)Lorg/eclipse/core/runtime/ILog; 13 <appendix> member <vmtarget> ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x00000186101c6788
instanceKlass org/eclipse/core/internal/runtime/Log
instanceKlass org/eclipse/core/internal/preferences/BundleStateScope
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$Resolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$FieldSearchResult
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldUtils
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$1
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$ReferenceMethodImpl
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$State
instanceKlass org/apache/felix/scr/impl/inject/InitReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler
instanceKlass org/apache/felix/scr/impl/inject/field/FieldMethods
instanceKlass org/eclipse/core/internal/resources/Rules
instanceKlass org/eclipse/core/internal/resources/CheckMissingNaturesListener
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610205c00
instanceKlass org/eclipse/core/internal/resources/ResourceChangeListenerRegistrar
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610205800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610205400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager startJob (Lorg/eclipse/core/internal/jobs/Worker;)Lorg/eclipse/core/runtime/jobs/Job; 45 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000001861018b038
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1541 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610205000
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610204c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610204800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610204400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager endJob (Lorg/eclipse/core/internal/jobs/InternalJob;Lorg/eclipse/core/runtime/IStatus;ZZ)V 14 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000001861018adf0
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1497 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610204000
instanceKlass  @bci org/eclipse/core/internal/resources/AliasManager buildAliasedProjectsSet ()V 30 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/AliasManager$$Lambda+0x00000186102004d8
instanceKlass org/eclipse/core/internal/filesystem/FileStoreUtil
instanceKlass  @bci org/eclipse/core/internal/resources/AliasManager$LocationMap <init> (Lorg/eclipse/core/internal/resources/AliasManager;)V 14 <appendix> argL0 ; # org/eclipse/core/internal/resources/AliasManager$LocationMap$$Lambda+0x0000018610200240
instanceKlass org/eclipse/core/internal/resources/AliasManager$LocationMap
instanceKlass org/eclipse/core/internal/resources/AliasManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshMonitor
instanceKlass org/eclipse/core/internal/refresh/MonitorManager
instanceKlass org/eclipse/core/resources/IResourceDeltaVisitor
instanceKlass org/eclipse/core/resources/IPathVariableChangeListener
instanceKlass org/eclipse/core/internal/refresh/RefreshManager
instanceKlass org/eclipse/core/resources/refresh/IRefreshResult
instanceKlass  @bci org/eclipse/core/internal/resources/ContentDescriptionManager getCurrentPlatformState ()Ljava/lang/String; 39 <appendix> argL0 ; # org/eclipse/core/internal/resources/ContentDescriptionManager$$Lambda+0x00000186101df888
instanceKlass  @bci org/eclipse/core/internal/properties/PropertyBucket$PropertyEntry <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/core/internal/properties/PropertyBucket$PropertyEntry$$Lambda+0x00000186101df260
instanceKlass  @cpi org/eclipse/jdt/internal/launching/StandardVMType 1152 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101da800
instanceKlass org/eclipse/core/internal/resources/ProjectContentTypes
instanceKlass org/eclipse/core/internal/utils/Cache
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager
instanceKlass  @bci org/eclipse/core/internal/resources/CharsetManager initPreferenceChangeListener ()V 2 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/CharsetManager$$Lambda+0x00000186101de660
instanceKlass  @bci org/eclipse/core/internal/jobs/Worker <init> (Lorg/eclipse/core/internal/jobs/WorkerPool;)V 10 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x00000186101da400
instanceKlass  @bci org/eclipse/core/internal/jobs/Worker <init> (Lorg/eclipse/core/internal/jobs/WorkerPool;)V 10 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x00000186101da000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d9c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d9800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d9400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager now ()J 5 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000001861018aba8
instanceKlass java/util/function/LongUnaryOperator
instanceKlass org/eclipse/core/internal/jobs/JobChangeEvent
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000186101d9000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186101d8c00
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101d8800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager schedule (Lorg/eclipse/core/internal/jobs/InternalJob;J)V 5 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000001861018a6f8
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobManager 1514 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101d8400
instanceKlass org/eclipse/core/internal/resources/CharsetDeltaJob$ICharsetListenerFilter
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 29 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861014ecb0
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 19 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861014ea60
instanceKlass  @bci org/osgi/framework/FrameworkUtil lambda$4 (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 9 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861014e818
instanceKlass org/eclipse/core/runtime/PerformanceStats
instanceKlass org/eclipse/core/internal/events/ResourceStats
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList$ListenerEntry
instanceKlass org/eclipse/core/internal/resources/CharsetManager$ResourceChangeListener
instanceKlass org/eclipse/core/resources/IResourceChangeListener
instanceKlass org/eclipse/core/internal/resources/CharsetManager
instanceKlass org/eclipse/core/internal/localstore/Bucket$Entry
instanceKlass org/eclipse/core/internal/localstore/BucketTree
instanceKlass org/eclipse/core/internal/localstore/Bucket$Visitor
instanceKlass org/eclipse/core/internal/localstore/Bucket
instanceKlass org/eclipse/core/internal/properties/PropertyManager2
instanceKlass org/eclipse/core/internal/events/LifecycleEvent
instanceKlass org/eclipse/core/internal/resources/LinkDescription
instanceKlass  @bci jdk/internal/reflect/MethodHandleBooleanFieldAccessorImpl setBoolean (Ljava/lang/Object;Z)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000186101d8000
instanceKlass java/lang/Short$ShortCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d2c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d2800
instanceKlass com/sun/jna/NativeMappedConverter
instanceKlass com/sun/jna/Klass
instanceKlass com/sun/jna/Native$Buffers
instanceKlass com/sun/jna/VarArgsChecker
instanceKlass com/sun/jna/NativeLibrary$NativeLibraryDisposer
instanceKlass com/sun/jna/NativeLibrary$1
instanceKlass com/sun/jna/SymbolProvider
instanceKlass com/sun/jna/NativeLibrary
instanceKlass org/eclipse/core/internal/filesystem/local/Win32Handler$FileAPIh
instanceKlass com/sun/jna/Memory$MemoryDisposer
instanceKlass com/sun/jna/internal/Cleaner$Cleanable
instanceKlass com/sun/jna/internal/Cleaner
instanceKlass com/sun/jna/WeakMemoryHolder
instanceKlass org/eclipse/core/filesystem/provider/FileInfo
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d2400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d2000
instanceKlass  @bci jdk/internal/reflect/MethodHandleBooleanFieldAccessorImpl getBoolean (Ljava/lang/Object;)Z 20 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000186101d1c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101d1800
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x00000186101d1400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186101d1000
instanceKlass com/sun/jna/Structure$StructField
instanceKlass com/sun/jna/Structure$LayoutInfo
instanceKlass java/lang/Class$AnnotationData
instanceKlass java/lang/annotation/Documented
instanceKlass com/sun/jna/Structure$FieldOrder
instanceKlass  @bci com/sun/jna/Structure fieldOrder ()Ljava/util/List; 84 <appendix> member <vmtarget> ; # com/sun/jna/Structure$$Lambda+0x00000186101cd690
instanceKlass  @bci com/sun/jna/Structure getFieldList ()Ljava/util/List; 84 <appendix> member <vmtarget> ; # com/sun/jna/Structure$$Lambda+0x00000186101cd448
instanceKlass  @bci com/sun/jna/Structure validateFields ()V 75 <appendix> member <vmtarget> ; # com/sun/jna/Structure$$Lambda+0x00000186101cd200
instanceKlass com/sun/jna/platform/win32/WinBase
instanceKlass com/sun/jna/platform/win32/BaseTSD
instanceKlass com/sun/jna/platform/win32/WinDef
instanceKlass com/sun/jna/Library
instanceKlass com/sun/jna/win32/W32APITypeMapper$2
instanceKlass com/sun/jna/DefaultTypeMapper$Entry
instanceKlass com/sun/jna/win32/W32APITypeMapper$1
instanceKlass com/sun/jna/TypeConverter
instanceKlass com/sun/jna/DefaultTypeMapper
instanceKlass com/sun/jna/TypeMapper
instanceKlass com/sun/jna/Structure$ByReference
instanceKlass com/sun/jna/Native$2
instanceKlass com/sun/jna/Structure$FFIType$FFITypes
instanceKlass com/sun/jna/Native$ffi_callback
instanceKlass com/sun/jna/JNIEnv
instanceKlass com/sun/jna/PointerType
instanceKlass com/sun/jna/NativeMapped
instanceKlass com/sun/jna/WString
instanceKlass com/sun/jna/win32/DLLCallback
instanceKlass com/sun/jna/CallbackProxy
instanceKlass com/sun/jna/Callback
instanceKlass com/sun/jna/Structure$ByValue
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass java/io/FileOutputStream$1
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass  @bci sun/security/provider/AbstractDrbg$SeederHolder <clinit> ()V 42 <appendix> member <vmtarget> ; # sun/security/provider/AbstractDrbg$SeederHolder$$Lambda+0x00000186101e4380
instanceKlass  @cpi sun/security/provider/AbstractDrbg$SeederHolder 91 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101ccc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186101cc800
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$PlatformResolver
instanceKlass java/net/spi/InetAddressResolver
instanceKlass java/net/spi/InetAddressResolver$LookupPolicy
instanceKlass java/net/Inet4AddressImpl
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass  @bci sun/security/provider/AbstractDrbg <clinit> ()V 12 <appendix> argL0 ; # sun/security/provider/AbstractDrbg$$Lambda+0x00000186101e19d8
instanceKlass  @cpi sun/security/provider/AbstractDrbg 383 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101cc400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186101cc000
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass  @bci sun/security/provider/DRBG <init> (Ljava/security/SecureRandomParameters;)V 26 <appendix> argL0 ; # sun/security/provider/DRBG$$Lambda+0x00000186101e0478
instanceKlass java/security/SecureRandomSpi
instanceKlass java/io/File$TempDirectory
instanceKlass com/sun/jna/Native$5
instanceKlass com/sun/jna/Platform
instanceKlass com/sun/jna/Native$1
instanceKlass com/sun/jna/Callback$UncaughtExceptionHandler
instanceKlass com/sun/jna/Native
instanceKlass com/sun/jna/Version
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/LoggerFinderLoader
instanceKlass  @bci java/lang/System$LoggerFinder accessProvider ()Ljava/lang/System$LoggerFinder; 8 <appendix> argL0 ; # java/lang/System$LoggerFinder$$Lambda+0x00000186100feb38
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000011
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/logging/LogManager$2
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000022
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000021
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass com/sun/jna/FromNativeContext
instanceKlass com/sun/jna/FromNativeConverter
instanceKlass com/sun/jna/ToNativeConverter
instanceKlass com/sun/jna/ToNativeContext
instanceKlass com/sun/jna/Structure
instanceKlass com/sun/jna/Pointer
instanceKlass org/eclipse/core/internal/filesystem/local/NativeHandler
instanceKlass org/eclipse/core/internal/filesystem/local/LocalFileNativesManager
instanceKlass javax/xml/parsers/DocumentBuilder
instanceKlass javax/xml/parsers/DocumentBuilderFactory
instanceKlass org/eclipse/core/internal/runtime/XmlProcessorFactory
instanceKlass org/eclipse/core/internal/resources/IModelObjectConstants
instanceKlass java/util/AbstractMap$2$1
instanceKlass  @bci org/eclipse/core/internal/resources/ProjectVariableProviderManager <clinit> ()V 14 <appendix> argL0 ; # org/eclipse/core/internal/resources/ProjectVariableProviderManager$$Lambda+0x00000186101be438
instanceKlass  @cpi org/eclipse/core/internal/resources/ProjectVariableProviderManager 171 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101c4000
instanceKlass org/eclipse/core/resources/variableresolvers/PathVariableResolver
instanceKlass org/eclipse/core/internal/resources/ProjectVariableProviderManager$Descriptor
instanceKlass org/eclipse/core/internal/resources/ProjectVariableProviderManager
instanceKlass org/eclipse/core/internal/resources/ProjectPathVariableManager
instanceKlass  @bci org/eclipse/core/internal/filesystem/local/LocalFile createExecutor (I)Ljava/util/concurrent/ForkJoinPool; 15 <appendix> argL0 ; # org/eclipse/core/internal/filesystem/local/LocalFile$$Lambda+0x00000186101c2000
instanceKlass  @bci org/eclipse/core/internal/filesystem/local/LocalFile createExecutor (I)Ljava/util/concurrent/ForkJoinPool; 5 <appendix> argL0 ; # org/eclipse/core/internal/filesystem/local/LocalFile$$Lambda+0x00000186101c1be0
instanceKlass java/util/concurrent/ForkJoinPool$2
instanceKlass jdk/internal/access/JavaUtilConcurrentFJPAccess
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JLSFsUtils
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass org/eclipse/core/filesystem/IFileStore
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JDTLSFilesystemActivator$1
instanceKlass org/eclipse/jdt/ls/core/internal/filesystem/JDTLSFilesystemActivator
instanceKlass org/eclipse/core/filesystem/IFileSystem
instanceKlass org/eclipse/core/internal/filesystem/InternalFileSystemCore
instanceKlass org/eclipse/core/filesystem/EFS
instanceKlass org/eclipse/core/internal/localstore/FileStoreRoot
instanceKlass org/eclipse/core/filesystem/IFileInfo
instanceKlass org/eclipse/core/filesystem/URIUtil
instanceKlass org/eclipse/core/internal/resources/MarkerAttributeMap
instanceKlass org/eclipse/core/internal/resources/MarkerSet
instanceKlass org/eclipse/core/internal/resources/MarkerReader
instanceKlass org/eclipse/core/internal/watson/ElementTree$ChildIDsCache
instanceKlass  @bci org/eclipse/core/internal/resources/SaveManager initSnap (Lorg/eclipse/core/runtime/IProgressMonitor;)V 67 <appendix> argL0 ; # org/eclipse/core/internal/resources/SaveManager$$Lambda+0x00000186101b6ce0
instanceKlass java/io/FilenameFilter
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint recursiveFindRpcMethods (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;)V 24 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x00000186101bac00
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager cancel (Lorg/eclipse/core/internal/jobs/InternalJob;)Z 15 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x000001861018a4b0
instanceKlass org/eclipse/core/internal/utils/ObjectMap
instanceKlass org/eclipse/core/internal/dtree/DataTreeLookup
instanceKlass org/eclipse/core/internal/dtree/DataTreeReader
instanceKlass org/eclipse/core/internal/watson/ElementTreeReader$1
instanceKlass org/eclipse/core/internal/dtree/IDataFlattener
instanceKlass org/eclipse/core/internal/watson/ElementTreeReader
instanceKlass org/eclipse/core/resources/IFileState
instanceKlass org/eclipse/core/internal/events/BuilderPersistentInfo
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x00000186101ba800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101ba400
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x00000186101ba000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b9c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b9800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b9400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b9000
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x00000186101b8c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b8800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b8400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101b8000
instanceKlass org/eclipse/core/internal/resources/SafeFileTable
instanceKlass org/eclipse/core/internal/resources/SavedState
instanceKlass org/eclipse/core/internal/resources/SyncInfoReader
instanceKlass org/eclipse/core/internal/resources/WorkspaceTreeReader
instanceKlass org/eclipse/core/resources/ISavedState
instanceKlass org/eclipse/core/resources/ISaveContext
instanceKlass org/eclipse/core/internal/resources/SaveManager
instanceKlass org/eclipse/core/internal/watson/IElementInfoFlattener
instanceKlass org/eclipse/core/internal/resources/SyncInfoWriter
instanceKlass org/eclipse/core/internal/resources/Synchronizer
instanceKlass org/eclipse/core/internal/resources/MarkerWriter
instanceKlass org/eclipse/core/internal/resources/MarkerDeltaManager
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache$MarkerTypeDefinition
instanceKlass org/eclipse/core/internal/resources/MarkerTypeDefinitionCache
instanceKlass org/eclipse/core/internal/resources/MarkerInfo
instanceKlass org/eclipse/core/internal/resources/IMarkerSetElement
instanceKlass org/eclipse/core/internal/resources/MarkerManager
instanceKlass  @bci org/eclipse/core/internal/events/NotificationManager$NotifyJob <init> (Lorg/eclipse/core/internal/events/NotificationManager;)V 13 <appendix> argL0 ; # org/eclipse/core/internal/events/NotificationManager$NotifyJob$$Lambda+0x00000186101abbe0
instanceKlass org/eclipse/core/internal/events/ResourceChangeListenerList
instanceKlass org/eclipse/core/internal/events/NotificationManager
instanceKlass java/util/stream/SortedOps
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getBundles0 (Ljava/lang/String;Ljava/lang/String;)Ljava/util/stream/Stream; 90 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x0000018610199d80
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform getBundles0 (Ljava/lang/String;Ljava/lang/String;)Ljava/util/stream/Stream; 80 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x0000018610199b40
instanceKlass org/eclipse/core/internal/events/BuildManager$DeltaCache
instanceKlass org/eclipse/core/resources/ICommand
instanceKlass org/eclipse/core/resources/IResourceDelta
instanceKlass org/eclipse/core/internal/events/InternalBuilder
instanceKlass org/eclipse/core/resources/IBuildContext
instanceKlass org/eclipse/core/internal/events/BuildManager
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager$1
instanceKlass org/eclipse/core/internal/resources/FilterDescriptor
instanceKlass org/eclipse/core/resources/IFilterMatcherDescriptor
instanceKlass org/eclipse/core/internal/resources/FilterTypeManager
instanceKlass org/eclipse/core/resources/IProjectNatureDescriptor
instanceKlass org/eclipse/core/internal/resources/NatureManager
instanceKlass org/eclipse/core/internal/events/ILifecycleListener
instanceKlass org/eclipse/core/internal/resources/PathVariableManager
instanceKlass  @bci org/eclipse/core/internal/resources/WorkspaceRoot getProject (Ljava/lang/String;)Lorg/eclipse/core/resources/IProject; 95 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/WorkspaceRoot$$Lambda+0x00000186101a7db0
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/FieldPosition$Delegate
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/text/DigitList
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000066
instanceKlass java/text/DecimalFormatSymbols
instanceKlass sun/util/resources/Bundles$2
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000010
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x80000005f
instanceKlass sun/util/resources/LocaleData$LocaleDataResourceBundleProvider
instanceKlass java/util/spi/ResourceBundleProvider
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getNumberFormatProvider ()Ljava/text/spi/NumberFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000067
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000061
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/FieldPosition
instanceKlass java/text/Format
instanceKlass org/eclipse/core/internal/preferences/PreferencesService$1
instanceKlass  @bci org/eclipse/core/internal/preferences/PreferencesService getLookupOrder (Ljava/lang/String;Ljava/lang/String;)[Ljava/lang/String; 29 <appendix> argL0 ; # org/eclipse/core/internal/preferences/PreferencesService$$Lambda+0x00000186101a8588
instanceKlass  @bci org/eclipse/core/internal/localstore/FileSystemResourceManager <init> (Lorg/eclipse/core/internal/resources/Workspace;)V 6 <appendix> member <vmtarget> ; # org/eclipse/core/internal/localstore/FileSystemResourceManager$$Lambda+0x00000186101a7b88
instanceKlass org/eclipse/core/internal/localstore/RefreshLocalVisitor
instanceKlass org/eclipse/core/internal/localstore/ILocalStoreConstants
instanceKlass org/eclipse/core/internal/localstore/IHistoryStore
instanceKlass org/eclipse/core/internal/localstore/IUnifiedTreeVisitor
instanceKlass org/eclipse/core/internal/localstore/FileSystemResourceManager
instanceKlass org/eclipse/core/runtime/jobs/MultiRule
instanceKlass org/eclipse/core/internal/jobs/OrderedLock
instanceKlass org/eclipse/core/internal/resources/WorkManager$NotifyRule
instanceKlass org/eclipse/core/internal/resources/WorkManager
instanceKlass org/eclipse/core/runtime/NullProgressMonitor
instanceKlass org/eclipse/core/runtime/preferences/IExportedPreferences
instanceKlass  @bci org/eclipse/core/internal/resources/WorkspacePreferences <init> ()V 189 <appendix> member <vmtarget> ; # org/eclipse/core/internal/resources/WorkspacePreferences$$Lambda+0x0000018610197c48
instanceKlass org/eclipse/core/runtime/Preferences$IPropertyChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$INodeChangeListener
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences$IPreferenceChangeListener
instanceKlass  @bci org/eclipse/core/runtime/Plugin getPluginPreferences ()Lorg/eclipse/core/runtime/Preferences; 65 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/Plugin$$Lambda+0x0000018610199168
instanceKlass org/eclipse/core/runtime/Preferences
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a5800
instanceKlass lombok/eclipse/agent/PatchFixesShadowLoaded
instanceKlass lombok/core/LombokNode
instanceKlass lombok/core/DiagnosticsReceiver
instanceKlass lombok/launch/PatchFixesHider$Util
instanceKlass lombok/launch/PatchFixesHider$LombokDeps
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 41 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x00000186101a4400
instanceKlass lombok/launch/PackageShader
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a4000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a3c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a3800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a3400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186101a3000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a1400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a1000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186101a0c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a0400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186101a0000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019bc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019b800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019b000
instanceKlass jdk/internal/vm/annotation/ForceInline
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/io/ObjectOutput
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectInput
instanceKlass java/lang/foreign/ValueLayout
instanceKlass java/lang/foreign/MemoryLayout
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861019a000
instanceKlass org/eclipse/core/internal/runtime/Product
instanceKlass org/eclipse/core/runtime/IProduct
instanceKlass lombok/patcher/scripts/WrapReturnValuesScript$1
instanceKlass org/eclipse/equinox/internal/app/ProductExtensionBranding
instanceKlass org/eclipse/core/internal/runtime/FindSupport
instanceKlass org/eclipse/core/runtime/FileLocator
instanceKlass  @bci org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl getBundles (Ljava/lang/String;Ljava/lang/String;)[Lorg/osgi/framework/Bundle; 281 <appendix> argL0 ; # org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl$$Lambda+0x000001861014e5d8
instanceKlass org/eclipse/core/runtime/SafeRunner
instanceKlass  @bci org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper runInitializer (Lorg/eclipse/core/runtime/IConfigurationElement;)V 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper$$Lambda+0x000001861016b3d0
instanceKlass org/eclipse/core/runtime/IExecutableExtensionFactory
instanceKlass org/eclipse/core/runtime/IExecutableExtension
instanceKlass org/eclipse/core/runtime/preferences/AbstractPreferenceInitializer
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTree
instanceKlass org/eclipse/core/internal/dtree/AbstractDataTreeNode
instanceKlass org/eclipse/core/internal/watson/ElementTree
instanceKlass org/eclipse/core/internal/runtime/DataArea
instanceKlass org/eclipse/core/internal/runtime/MetaDataKeeper
instanceKlass org/eclipse/core/internal/resources/LocalMetaArea
instanceKlass org/eclipse/core/internal/resources/LocationValidator
instanceKlass org/eclipse/core/runtime/Platform$OS
instanceKlass org/eclipse/core/internal/utils/FileUtil
instanceKlass org/eclipse/core/internal/watson/IElementContentVisitor
instanceKlass org/eclipse/core/resources/IResourceFilterDescription
instanceKlass org/eclipse/core/resources/IMarker
instanceKlass org/eclipse/core/resources/team/IResourceTree
instanceKlass org/eclipse/core/resources/IResourceProxy
instanceKlass org/eclipse/core/runtime/Platform
instanceKlass org/eclipse/core/resources/team/FileModificationValidationContext
instanceKlass org/eclipse/core/resources/team/IMoveDeleteHook
instanceKlass org/eclipse/core/internal/resources/ModelObject
instanceKlass org/eclipse/core/resources/IProject
instanceKlass org/eclipse/core/resources/IPathVariableManager
instanceKlass org/eclipse/core/resources/IProjectDescription
instanceKlass org/eclipse/core/internal/resources/InternalTeamHook
instanceKlass org/eclipse/core/internal/watson/IElementComparator
instanceKlass org/eclipse/core/internal/dtree/IComparator
instanceKlass org/eclipse/core/resources/IBuildConfiguration
instanceKlass org/eclipse/core/resources/IResourceRuleFactory
instanceKlass org/eclipse/core/resources/ISynchronizer
instanceKlass org/eclipse/core/internal/properties/IPropertyManager
instanceKlass org/eclipse/core/internal/resources/IManager
instanceKlass org/eclipse/core/resources/IWorkspaceDescription
instanceKlass org/eclipse/core/resources/IFile
instanceKlass org/eclipse/core/resources/IEncodedStorage
instanceKlass org/eclipse/core/resources/IStorage
instanceKlass org/eclipse/core/resources/IFolder
instanceKlass org/eclipse/core/internal/watson/IPathRequestor
instanceKlass org/eclipse/core/internal/resources/ResourceInfo
instanceKlass org/eclipse/core/internal/utils/IStringPoolParticipant
instanceKlass org/eclipse/core/runtime/ICoreRunnable
instanceKlass org/eclipse/core/internal/watson/IElementTreeData
instanceKlass org/eclipse/core/internal/jobs/WorkerPool
instanceKlass org/eclipse/core/internal/jobs/JobQueue
instanceKlass org/eclipse/core/internal/jobs/DeadlockDetector
instanceKlass org/eclipse/core/internal/jobs/LockManager
instanceKlass org/eclipse/core/runtime/jobs/JobChangeAdapter
instanceKlass  @bci org/eclipse/core/internal/jobs/JobManager <init> ()V 52 <appendix> member <vmtarget> ; # org/eclipse/core/internal/jobs/JobManager$$Lambda+0x0000018610183c40
instanceKlass  @bci java/util/logging/LogRecord$CallerFinder <clinit> ()V 0 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610183400
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 50 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x0000018610183a20
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 41 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x0000018610183800
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 32 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x0000018610182cb8
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 23 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x0000018610182a98
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 14 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x0000018610182878
instanceKlass  @bci org/eclipse/core/internal/jobs/JobListeners <init> ()V 5 <appendix> argL0 ; # org/eclipse/core/internal/jobs/JobListeners$$Lambda+0x0000018610182658
instanceKlass  @cpi org/eclipse/core/internal/jobs/JobListeners 385 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610183000
instanceKlass org/eclipse/core/internal/jobs/JobListeners$IListenerDoit
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeEvent
instanceKlass org/eclipse/core/internal/jobs/JobListeners
instanceKlass org/eclipse/core/internal/jobs/ImplicitJobs
instanceKlass org/eclipse/core/internal/jobs/JobManager$1
instanceKlass org/eclipse/core/internal/jobs/InternalJobGroup
instanceKlass org/eclipse/core/runtime/ProgressMonitorWrapper
instanceKlass org/eclipse/core/runtime/IProgressMonitorWithBlocking
instanceKlass org/eclipse/core/runtime/jobs/ILock
instanceKlass org/eclipse/core/runtime/jobs/IJobChangeListener
instanceKlass org/eclipse/core/internal/jobs/JobManager
instanceKlass org/eclipse/core/runtime/jobs/IJobManager
instanceKlass org/eclipse/core/internal/jobs/JobOSGiUtils
instanceKlass org/eclipse/core/internal/jobs/JobActivator
instanceKlass org/eclipse/core/resources/IWorkspaceRoot
instanceKlass org/eclipse/core/resources/IContainer
instanceKlass org/eclipse/core/resources/IResource
instanceKlass org/eclipse/core/runtime/jobs/ISchedulingRule
instanceKlass org/eclipse/core/runtime/PlatformObject
instanceKlass org/eclipse/core/internal/resources/ICoreConstants
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/Formatter
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass jdk/internal/util/ByteArray
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1075 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000e
instanceKlass  @bci java/time/format/DateTimeFormatter <clinit> ()V 1067 <appendix> argL0 ; # java/time/format/DateTimeFormatter$$Lambda+0x80000000d
instanceKlass java/time/Period
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/format/DateTimeFormatterBuilder$TextPrinterParser
instanceKlass java/time/format/DateTimeTextProvider$1
instanceKlass java/time/format/DateTimeTextProvider
instanceKlass java/time/format/DateTimeTextProvider$LocaleStore
instanceKlass java/time/format/DateTimeFormatterBuilder$InstantPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$StringLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$ZoneIdPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$OffsetIdPrinterParser
instanceKlass java/time/format/DecimalStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$CompositePrinterParser
instanceKlass java/time/chrono/AbstractChronology
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/format/DateTimeFormatterBuilder$CharLiteralPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$NumberPrinterParser
instanceKlass java/time/format/DateTimeFormatterBuilder$DateTimePrinterParser
instanceKlass java/time/temporal/JulianFields
instanceKlass java/time/temporal/IsoFields
instanceKlass  @bci java/time/format/DateTimeFormatterBuilder <clinit> ()V 0 <appendix> argL0 ; # java/time/format/DateTimeFormatterBuilder$$Lambda+0x80000000f
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/format/DateTimeFormatterBuilder
instanceKlass java/time/format/DateTimeFormatter
instanceKlass org/eclipse/osgi/internal/debug/EclipseDebugTrace
instanceKlass org/eclipse/core/internal/utils/Policy$1
instanceKlass org/eclipse/core/runtime/IProgressMonitor
instanceKlass org/eclipse/core/internal/utils/Policy
instanceKlass org/eclipse/core/resources/ResourcesPlugin$WorkspaceInitCustomizer
instanceKlass org/eclipse/core/resources/IWorkspace
instanceKlass org/eclipse/core/runtime/IAdaptable
instanceKlass org/eclipse/jdt/ls/core/internal/managers/ProjectsManager
instanceKlass org/eclipse/jdt/ls/core/internal/managers/IProjectsManager
instanceKlass org/eclipse/core/resources/ISaveParticipant
instanceKlass org/eclipse/core/internal/runtime/LogServiceFactory
instanceKlass org/eclipse/equinox/internal/app/AppCommands
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001861017b400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861017b000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861017ac00
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form names 6 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861017a800
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861017a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861017a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610179c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610179800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610179400
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> form names 10 function resolvedHandle form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610179000
instanceKlass  @bci org/eclipse/equinox/internal/app/EclipseAppDescriptor getInstanceID ()Ljava/lang/String; 31 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610178c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610178800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610178400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610178000
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer$RegisterService
instanceKlass org/eclipse/equinox/app/IApplicationContext
instanceKlass org/osgi/service/application/ApplicationHandle
instanceKlass org/osgi/service/application/ApplicationDescriptor
instanceKlass org/eclipse/osgi/service/runnable/ApplicationLauncher
instanceKlass org/eclipse/osgi/service/runnable/ApplicationRunnable
instanceKlass org/eclipse/equinox/internal/app/IBranding
instanceKlass org/eclipse/osgi/service/runnable/ParameterizedRunnable
instanceKlass org/eclipse/equinox/internal/app/EclipseAppContainer
instanceKlass org/osgi/service/application/ScheduledApplication
instanceKlass org/eclipse/equinox/internal/app/AppPersistence
instanceKlass org/eclipse/equinox/internal/app/Activator
instanceKlass org/eclipse/equinox/internal/app/CommandLineArgs
instanceKlass org/eclipse/core/internal/preferences/legacy/InitLegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/legacy/ProductPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/IProductPreferencesService
instanceKlass org/eclipse/core/internal/runtime/AuthorizationHandler
instanceKlass org/eclipse/core/runtime/IBundleGroupProvider
instanceKlass  @bci org/eclipse/core/internal/runtime/InternalPlatform <clinit> ()V 98 <appendix> argL0 ; # org/eclipse/core/internal/runtime/InternalPlatform$$Lambda+0x0000018610174000
instanceKlass org/eclipse/core/runtime/ILog
instanceKlass org/eclipse/core/internal/runtime/InternalPlatform
instanceKlass org/eclipse/core/runtime/Plugin
instanceKlass org/osgi/util/promise/DeferredPromiseImpl$ResolveWith
instanceKlass  @bci org/osgi/util/promise/PromiseFactory$All run ()V 81 <appendix> member <vmtarget> ; # org/osgi/util/promise/PromiseFactory$All$$Lambda+0x0000018610172690
instanceKlass  @cpi org/osgi/util/promise/PromiseFactory$All 144 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610173000
instanceKlass org/osgi/util/promise/PromiseImpl$Result
instanceKlass org/osgi/util/promise/PromiseFactory$All
instanceKlass org/osgi/util/promise/PromiseImpl$InlineCallback
instanceKlass org/eclipse/core/internal/content/ContentTypeHandler
instanceKlass org/eclipse/core/runtime/QualifiedName
instanceKlass org/apache/felix/scr/impl/inject/methods/ActivateMethod$1
instanceKlass org/apache/felix/scr/impl/inject/MethodResult
instanceKlass org/eclipse/core/internal/content/ContentTypeManager$ContentTypeRegistryChangeListener
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$OpenStatusImpl
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod$1
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$Resolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$MethodInfo
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$1
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003e
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass  @bci java/lang/Class methodToString (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/String; 42 <appendix> argL0 ; # java/lang/Class$$Lambda+0x00000186100f3288
instanceKlass org/eclipse/core/internal/content/BasicDescription
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$IContentTypeChangeListener
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$ISelectionPolicy
instanceKlass org/eclipse/core/internal/content/ContentTypeBuilder
instanceKlass org/eclipse/core/internal/content/ContentTypeCatalog
instanceKlass org/eclipse/core/internal/preferences/BundleStateScopeServiceFactory
instanceKlass org/eclipse/core/internal/preferences/Activator$1
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861016ec00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016e800
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861016e400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016d800
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry$ListenerInfo
instanceKlass org/eclipse/core/internal/preferences/PreferenceServiceRegistryHelper
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JavaLanguageServerPlugin$1 run (Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/runtime/IStatus; 22 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861016d400
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JavaLanguageServerPlugin$1 run (Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/runtime/IStatus; 22 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861016d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016cc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016c800
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/JavaLanguageServerPlugin$1 run (Lorg/eclipse/core/runtime/IProgressMonitor;)Lorg/eclipse/core/runtime/IStatus; 22 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861016c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861016c000
instanceKlass org/eclipse/core/runtime/ListenerList$ListenerListIterator
instanceKlass org/eclipse/core/internal/preferences/OSGiPreferencesServiceManager
instanceKlass org/osgi/service/prefs/PreferencesService
instanceKlass org/eclipse/core/runtime/preferences/AbstractScope
instanceKlass org/eclipse/core/runtime/preferences/IScopeContext
instanceKlass org/eclipse/core/runtime/Path
instanceKlass org/eclipse/core/runtime/Path$Constants
instanceKlass org/eclipse/core/runtime/IPath
instanceKlass org/eclipse/core/internal/preferences/ImmutableMap
instanceKlass org/eclipse/core/internal/preferences/EclipsePreferences
instanceKlass org/eclipse/core/runtime/preferences/IScope
instanceKlass org/eclipse/core/internal/preferences/PreferencesService
instanceKlass org/eclipse/core/runtime/preferences/IPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/ILegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesOSGiUtils
instanceKlass org/eclipse/core/internal/preferences/Activator
instanceKlass org/eclipse/core/runtime/preferences/IEclipsePreferences
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils
instanceKlass org/eclipse/core/internal/content/ILazySource
instanceKlass org/eclipse/core/internal/adapter/AdapterManagerListener
instanceKlass org/eclipse/core/internal/runtime/IAdapterManagerProvider
instanceKlass org/eclipse/core/runtime/IRegistryEventListener
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryCommandProvider
instanceKlass org/eclipse/osgi/framework/console/CommandProvider
instanceKlass org/eclipse/core/internal/registry/RegistryProviderFactory
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryProviderOSGI
instanceKlass sun/nio/fs/WindowsFileCopy
instanceKlass org/eclipse/osgi/internal/loader/buddy/PolicyHandler
instanceKlass org/eclipse/core/internal/registry/TemporaryObjectManager
instanceKlass org/eclipse/core/internal/registry/RegistryIndexChildren
instanceKlass org/eclipse/core/internal/registry/RegistryIndexElement
instanceKlass org/eclipse/core/internal/registry/CombinedEventDelta
instanceKlass org/eclipse/core/internal/registry/Contribution
instanceKlass org/eclipse/core/runtime/Assert
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610160c00
instanceKlass java/lang/invoke/DirectMethodHandle$1
instanceKlass org/eclipse/core/internal/runtime/LocalizationUtils
instanceKlass org/eclipse/core/runtime/Status
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 29 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610160800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610160400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610160000
instanceKlass  @bci org/eclipse/osgi/util/NLS <clinit> ()V 7 <appendix> argL0 ; # org/eclipse/osgi/util/NLS$$Lambda+0x000001861014cd40
instanceKlass org/eclipse/osgi/util/NLS
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass java/util/ResourceBundle
instanceKlass org/eclipse/core/internal/runtime/ResourceTranslator
instanceKlass org/eclipse/core/runtime/spi/RegistryContributor
instanceKlass org/eclipse/core/runtime/IContributor
instanceKlass org/eclipse/core/runtime/ContributorFactoryOSGi
instanceKlass org/eclipse/core/internal/registry/osgi/EclipseBundleListener
instanceKlass org/eclipse/core/internal/registry/HashtableOfStringAndInt
instanceKlass org/eclipse/core/internal/registry/KeyedHashSet
instanceKlass org/eclipse/core/runtime/IConfigurationElement
instanceKlass org/eclipse/core/internal/registry/Handle
instanceKlass org/eclipse/core/internal/registry/RegistryObjectManager
instanceKlass org/eclipse/core/internal/registry/IObjectManager
instanceKlass  @bci org/eclipse/core/internal/registry/RegistryProperties getContextProperty (Ljava/lang/String;)Ljava/lang/String; 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/registry/RegistryProperties$$Lambda+0x000001861015ad08
instanceKlass org/eclipse/core/internal/registry/RegistryTimestamp
instanceKlass org/eclipse/core/internal/registry/TableReader
instanceKlass org/eclipse/core/runtime/ListenerList
instanceKlass org/eclipse/core/internal/registry/ReadWriteMonitor
instanceKlass org/eclipse/core/internal/registry/RegistryObjectFactory
instanceKlass org/eclipse/core/runtime/ISafeRunnable
instanceKlass org/eclipse/core/runtime/IExtensionDelta
instanceKlass org/eclipse/core/runtime/IExtensionPoint
instanceKlass org/eclipse/core/runtime/IExtension
instanceKlass org/eclipse/core/internal/registry/RegistryObject
instanceKlass org/eclipse/core/internal/registry/KeyedElement
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry
instanceKlass org/eclipse/core/runtime/spi/IDynamicExtensionRegistry
instanceKlass org/eclipse/core/runtime/IExtensionRegistry
instanceKlass org/eclipse/core/runtime/RegistryFactory
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$IEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap
instanceKlass org/eclipse/core/internal/registry/osgi/OSGIUtils
instanceKlass org/eclipse/core/internal/registry/osgi/EquinoxUtils
instanceKlass org/eclipse/core/internal/registry/RegistryProperties
instanceKlass org/eclipse/core/runtime/spi/IRegistryProvider
instanceKlass org/eclipse/core/runtime/spi/RegistryStrategy
instanceKlass org/eclipse/core/internal/registry/osgi/Activator
instanceKlass org/eclipse/core/runtime/IRegistryChangeListener
instanceKlass org/eclipse/core/internal/content/IContentTypeInfo
instanceKlass org/eclipse/core/runtime/content/IContentDescription
instanceKlass org/osgi/service/prefs/Preferences
instanceKlass org/eclipse/core/runtime/content/IContentType
instanceKlass org/eclipse/core/runtime/content/IContentTypeSettings
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentConstructorImpl
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/methods/BindMethods
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotApplicable
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$State
instanceKlass org/apache/felix/scr/impl/inject/BaseParameter
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod
instanceKlass org/eclipse/core/internal/content/ContentTypeMatcher
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager
instanceKlass org/eclipse/core/runtime/content/IContentTypeMatcher
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$1
instanceKlass org/apache/felix/scr/impl/helper/ComponentServiceObjectsHelper
instanceKlass org/apache/felix/scr/impl/manager/EdgeInfo
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl$ComponentInstanceImpl
instanceKlass org/osgi/service/component/ComponentInstance
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegStateWrapper
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator$ListenerInfo
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker$AbstractTracked
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListener
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker
instanceKlass org/apache/felix/scr/impl/helper/Coercions
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$AbstractCustomizer
instanceKlass org/apache/felix/scr/impl/inject/RefPair
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$Customizer
instanceKlass org/apache/felix/scr/impl/manager/ServiceTrackerCustomizer
instanceKlass org/apache/felix/scr/impl/inject/OpenStatus
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager
instanceKlass org/apache/felix/scr/impl/manager/ReferenceManager
instanceKlass org/osgi/util/promise/Deferred
instanceKlass org/apache/felix/scr/impl/inject/ScrComponentContext
instanceKlass org/apache/felix/scr/component/ExtComponentContext
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$SetImplementationObject
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker$1
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker
instanceKlass java/util/Timer$ThreadReaper
instanceKlass java/util/TaskQueue
instanceKlass java/util/Timer
instanceKlass org/apache/felix/scr/impl/inject/ComponentConstructor
instanceKlass org/apache/felix/scr/impl/inject/LifecycleMethod
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentMethodsImpl
instanceKlass org/apache/felix/scr/impl/metadata/TargetedPID
instanceKlass java/util/concurrent/CompletionStage
instanceKlass org/osgi/util/promise/PromiseImpl
instanceKlass org/osgi/util/promise/Promise
instanceKlass org/osgi/util/promise/PromiseFactory
instanceKlass org/osgi/util/promise/Promises
instanceKlass org/apache/felix/scr/impl/inject/ComponentMethods
instanceKlass org/osgi/service/component/ComponentFactory
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ConfigurableComponentHolder
instanceKlass org/apache/felix/scr/impl/manager/ComponentContainer
instanceKlass org/apache/felix/scr/impl/ComponentRegistryKey
instanceKlass org/apache/felix/scr/impl/metadata/PropertyMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ComponentMetadata
instanceKlass org/apache/felix/scr/impl/xml/XmlConstants
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants$ArrayEnumeration
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy
instanceKlass org/xml/sax/ext/Locator2
instanceKlass org/xml/sax/Locator
instanceKlass com/sun/org/apache/xerces/internal/util/XMLSymbols
instanceKlass com/sun/org/apache/xerces/internal/util/XMLChar
instanceKlass com/sun/xml/internal/stream/Entity
instanceKlass com/sun/xml/internal/stream/util/BufferAllocator
instanceKlass com/sun/xml/internal/stream/util/ThreadLocalBufferAllocator
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$EncodingInfo
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLLimitAnalyzer
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLInputSource
instanceKlass com/sun/org/apache/xerces/internal/util/ErrorHandlerWrapper
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLErrorHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/ExternalSubsetResolver
instanceKlass com/sun/org/apache/xerces/internal/util/EntityResolverWrapper
instanceKlass org/xml/sax/ext/EntityResolver2
instanceKlass org/xml/sax/InputSource
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$AttributesProxy
instanceKlass org/xml/sax/ext/Attributes2
instanceKlass org/xml/sax/Attributes
instanceKlass org/xml/sax/AttributeList
instanceKlass com/sun/org/apache/xerces/internal/util/FeatureState
instanceKlass com/sun/org/apache/xerces/internal/util/PropertyState
instanceKlass com/sun/org/apache/xerces/internal/impl/msg/XMLMessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/util/MessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLVersionDetector
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationManager
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NMTOKENDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NOTATIONDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ENTITYDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ListDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDREFDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/StringDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DTDDVFactory
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/DTDGrammarBucket
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLAttributeDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLSimpleType
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLElementDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationState
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/ValidationContext
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/RevalidationHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidatorFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentFilter
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLEntityDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDProcessor
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelSource
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDSource
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLDTDDescription
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarDescription
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$TrailingMiscDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$PrologDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$XMLDeclDriver
instanceKlass com/sun/org/apache/xerces/internal/util/NamespaceSupport
instanceKlass com/sun/org/apache/xerces/internal/xni/NamespaceContext
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl$Attribute
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLAttributes
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$FragmentContentDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$Driver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack2
instanceKlass com/sun/org/apache/xerces/internal/xni/QName
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLString
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner
instanceKlass com/sun/xml/internal/stream/XMLBufferListener
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentSource
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLErrorReporter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLLocator
instanceKlass com/sun/xml/internal/stream/XMLEntityStorage
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl$AugmentationsItemsContainer
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/Augmentations
instanceKlass com/sun/org/apache/xerces/internal/util/XMLResourceIdentifierImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLResourceIdentifier
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLEntityResolver
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponent
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable$Entry
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable
instanceKlass jdk/xml/internal/JdkConstants
instanceKlass jdk/xml/internal/JdkXmlUtils
instanceKlass com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings
instanceKlass com/sun/org/apache/xerces/internal/parsers/XML11Configurable
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager
instanceKlass com/sun/org/apache/xerces/internal/parsers/XMLParser
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDContentModelHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDocumentHandler
instanceKlass org/xml/sax/XMLReader
instanceKlass org/xml/sax/Parser
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
instanceKlass javax/xml/parsers/SAXParser
instanceKlass com/sun/org/apache/xerces/internal/xs/PSVIProvider
instanceKlass com/sun/org/apache/xerces/internal/jaxp/JAXPConstants
instanceKlass  @bci javax/xml/parsers/FactoryFinder newInstance (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/ClassLoader;ZZ)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x00000186100d6890
instanceKlass  @bci jdk/xml/internal/SecuritySupport getContextClassLoader ()Ljava/lang/ClassLoader; 0 <appendix> argL0 ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000186100d6408
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x00000186100d61e0
instanceKlass javax/xml/parsers/FactoryFinder$1
instanceKlass  @bci jdk/xml/internal/SecuritySupport getFileInputStream (Ljava/io/File;)Ljava/io/FileInputStream; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000186100d5d88
instanceKlass  @bci jdk/xml/internal/SecuritySupport doesFileExist (Ljava/io/File;)Z 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000186100d5b60
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 6 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x00000186100d5938
instanceKlass  @bci jdk/xml/internal/SecuritySupport getSystemProperty (Ljava/lang/String;)Ljava/lang/String; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x00000186100d5710
instanceKlass jdk/xml/internal/SecuritySupport
instanceKlass javax/xml/parsers/FactoryFinder
instanceKlass javax/xml/parsers/SAXParserFactory
instanceKlass  @bci org/eclipse/osgi/storage/Storage lambda$7 (Ljava/util/List;Ljava/lang/String;)Ljava/util/stream/Stream; 7 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000001861013aca0
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$1
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 101 <appendix> argL0 ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000001861013a800
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 91 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000001861012bc20
instanceKlass org/xml/sax/helpers/DefaultHandler
instanceKlass org/xml/sax/ErrorHandler
instanceKlass org/xml/sax/ContentHandler
instanceKlass org/xml/sax/DTDHandler
instanceKlass org/xml/sax/EntityResolver
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListenerContext
instanceKlass org/eclipse/core/internal/runtime/IAdapterFactoryExt
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge$LazyAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge
instanceKlass org/eclipse/core/runtime/IAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/TracingOptions$1
instanceKlass org/eclipse/core/internal/runtime/TracingOptions
instanceKlass java/util/AbstractMap$1$1
instanceKlass org/osgi/service/url/URLStreamHandlerService
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller current ()Ljava/util/Optional; 4 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000018610139078
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/GenericEndpoint lambda$recursiveFindRpcMethods$2 (Ljava/lang/Object;Ljava/util/Set;Ljava/util/Set;Ljava/lang/reflect/Method;)V 33 <appendix> argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861013a000
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller trackCurrent ()Ljava/util/Optional; 19 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000018610138e30
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller getCurrent ()Ljava/util/Optional; 12 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000018610138be8
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass org/eclipse/core/runtime/ServiceCaller$ReferenceAndService
instanceKlass org/eclipse/core/internal/runtime/AdapterManager
instanceKlass org/eclipse/core/runtime/IAdapterManager
instanceKlass org/eclipse/core/internal/runtime/PlatformURLConverter
instanceKlass org/eclipse/core/internal/runtime/RuntimeLog
instanceKlass org/eclipse/core/runtime/IStatus
instanceKlass org/eclipse/core/internal/runtime/PlatformLogWriter
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceImpl
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 39 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000018610133000
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 26 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861012b2b0
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 17 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861012b070
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 1 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861012ae48
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 27 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861012ac10
instanceKlass org/osgi/framework/connect/FrameworkUtilHelper
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 8 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000001861012a7f0
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 31 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x0000018610132ca8
instanceKlass org/osgi/framework/FrameworkUtil
instanceKlass org/eclipse/core/runtime/ServiceCaller
instanceKlass org/eclipse/core/internal/runtime/Activator
instanceKlass org/apache/felix/scr/impl/config/ScrMetaTypeProviderServiceFactory
instanceKlass org/apache/felix/scr/impl/config/ScrManagedServiceServiceFactory
instanceKlass org/apache/felix/scr/impl/ComponentCommands$2
instanceKlass org/apache/felix/scr/impl/ComponentCommands$1
instanceKlass org/apache/felix/scr/impl/ComponentCommands
instanceKlass org/apache/felix/scr/impl/Activator$ScrExtension
instanceKlass org/apache/felix/scr/impl/ComponentActorThread$1
instanceKlass org/apache/felix/scr/impl/ComponentActorThread
instanceKlass org/apache/felix/scr/impl/runtime/ServiceComponentRuntimeImpl
instanceKlass java/util/TimerTask
instanceKlass org/apache/felix/scr/impl/manager/RegionConfigurationSupport
instanceKlass org/apache/felix/scr/impl/manager/ComponentHolder
instanceKlass org/apache/felix/scr/impl/ComponentRegistry
instanceKlass org/osgi/util/tracker/BundleTracker
instanceKlass org/apache/felix/scr/impl/logger/ScrLogManager$1
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LogDomain
instanceKlass org/apache/felix/scr/impl/logger/LogManager$Lock
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LoggerFacade
instanceKlass org/apache/felix/scr/impl/logger/BundleLogger
instanceKlass org/apache/felix/scr/impl/logger/ComponentLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLogger
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLoggerFactory
instanceKlass org/osgi/service/component/ComponentContext
instanceKlass org/osgi/service/component/ComponentServiceObjects
instanceKlass org/apache/felix/scr/impl/inject/internal/ClassUtils
instanceKlass org/apache/felix/scr/impl/config/ScrConfigurationImpl
instanceKlass org/osgi/service/component/runtime/ServiceComponentRuntime
instanceKlass org/apache/felix/scr/impl/manager/ScrConfiguration
instanceKlass org/apache/felix/scr/impl/logger/LogConfiguration
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult
instanceKlass org/apache/felix/scr/impl/AbstractExtender
instanceKlass org/osgi/util/tracker/BundleTrackerCustomizer
instanceKlass org/eclipse/osgi/internal/weaving/WeavingHookConfigurator$WovenClassContext
instanceKlass org/eclipse/osgi/internal/weaving/WovenClassImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClass
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager$DefineContext
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$3
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel$2
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$1
instanceKlass org/eclipse/osgi/service/resolver/ExportPackageDescription
instanceKlass org/eclipse/osgi/service/resolver/ImportPackageSpecification
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeSpecification
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeDescription
instanceKlass org/eclipse/osgi/service/resolver/GenericSpecification
instanceKlass org/eclipse/osgi/service/resolver/GenericDescription
instanceKlass org/eclipse/osgi/service/resolver/BundleDescription
instanceKlass org/eclipse/osgi/service/resolver/BaseDescription
instanceKlass org/eclipse/osgi/internal/resolver/StateImpl
instanceKlass org/eclipse/osgi/service/resolver/HostSpecification
instanceKlass org/eclipse/osgi/service/resolver/BundleSpecification
instanceKlass org/eclipse/osgi/service/resolver/VersionConstraint
instanceKlass org/eclipse/osgi/internal/resolver/StateObjectFactoryImpl
instanceKlass org/eclipse/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/service/resolver/State
instanceKlass org/eclipse/osgi/service/resolver/StateObjectFactory
instanceKlass org/eclipse/osgi/compatibility/state/PlatformAdminImpl
instanceKlass org/eclipse/osgi/service/resolver/PlatformAdmin
instanceKlass org/eclipse/osgi/compatibility/state/Activator
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610121000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610120c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610120800
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$7
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removeSubstitutedCapabilities (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList;)Ljava/util/Collection; 60 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x0000018610126980
instanceKlass org/apache/felix/resolver/util/ArrayMap$1$1
instanceKlass org/apache/felix/resolver/UsedBlames
instanceKlass java/util/AbstractList$Itr
instanceKlass org/apache/felix/resolver/util/CopyOnWriteSet$1
instanceKlass org/apache/felix/resolver/WrappedCapability
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport$EntryImpl
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Entry
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 74 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x0000018610124940
instanceKlass java/util/function/BiPredicate
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 69 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x00000186101246f0
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 64 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x00000186101244a0
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 44 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x0000018610124250
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 39 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x0000018610124000
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removePayloadContent (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 10 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000001861011fd68
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$5
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removePayloadContent (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 1 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000001861011f8b0
instanceKlass org/eclipse/osgi/container/ModuleResolver$ResolveProcess$DynamicFragments
instanceKlass java/util/Collections$ReverseComparator2
instanceKlass java/util/Collections$ReverseComparator
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver$ResolveProcess resolveNonPayLoadFragments ()Ljava/util/Map; 84 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$ResolveProcess$$Lambda+0x000001861011f450
instanceKlass  @bci org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory getHookReferences (Lorg/eclipse/osgi/internal/serviceregistry/ServiceRegistry;Lorg/eclipse/osgi/internal/framework/BundleContextImpl;)[Lorg/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl; 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory$$Lambda+0x000001861011f228
instanceKlass org/eclipse/osgi/container/ModuleDatabase$2
instanceKlass org/eclipse/osgi/internal/container/ComputeNodeOrder$Digraph$Vertex
instanceKlass org/eclipse/osgi/internal/container/ComputeNodeOrder$Digraph
instanceKlass org/eclipse/osgi/internal/container/ComputeNodeOrder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610120400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610120000
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring$1
instanceKlass org/osgi/dto/DTO
instanceKlass org/eclipse/osgi/container/builders/OSGiManifestBuilderFactory$NativeClause
instanceKlass org/eclipse/osgi/storage/ManifestLocalization$BundleResourceBundle
instanceKlass org/eclipse/osgi/storage/ManifestLocalization
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread$Queued
instanceKlass  @bci org/eclipse/osgi/framework/eventmgr/EventManager getEventThread ()Lorg/eclipse/osgi/framework/eventmgr/EventManager$EventThread; 24 <appendix> member <vmtarget> ; # org/eclipse/osgi/framework/eventmgr/EventManager$$Lambda+0x000001861011b3f8
instanceKlass  @bci org/eclipse/osgi/internal/framework/EquinoxEventPublisher notifyEventHooksPrivileged (Lorg/osgi/framework/BundleEvent;Ljava/util/Collection;)V 98 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/EquinoxEventPublisher$$Lambda+0x000001861011ae90
instanceKlass org/osgi/framework/VersionRange
instanceKlass  @bci org/eclipse/osgi/internal/framework/BundleContextImpl notifyFindHooksPriviledged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/util/Collection;)V 73 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/BundleContextImpl$$Lambda+0x000001861011aa00
instanceKlass org/eclipse/osgi/framework/util/FilePath
instanceKlass org/eclipse/core/runtime/internal/adaptor/ConsoleManager
instanceKlass org/eclipse/core/runtime/internal/adaptor/DefaultStartupMonitor
instanceKlass org/eclipse/osgi/service/runnable/StartupMonitor
instanceKlass java/lang/Thread$Builder$OfVirtual
instanceKlass java/lang/Thread$Builder$OfPlatform
instanceKlass java/lang/Thread$Builder
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceFactoryUse$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$2
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer
instanceKlass org/eclipse/osgi/service/security/TrustEngine
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedContentConstants
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook$1
instanceKlass org/eclipse/osgi/internal/framework/XMLParsingServiceFactory
instanceKlass org/eclipse/osgi/storage/BundleLocalizationImpl
instanceKlass org/eclipse/osgi/service/localization/BundleLocalization
instanceKlass org/eclipse/osgi/storage/url/BundleURLConverter
instanceKlass org/eclipse/osgi/service/urlconversion/URLConverter
instanceKlass org/eclipse/osgi/internal/framework/legacy/StartLevelImpl
instanceKlass org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl
instanceKlass org/osgi/service/condition/ConditionImpl
instanceKlass org/osgi/service/condition/Condition
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl add (Ljava/net/ContentHandlerFactory;)V 13 <appendix> argL0 ; # org/eclipse/equinox/plurl/impl/PlurlImpl$$Lambda+0x0000018610116b98
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl$5 getContent ()Ljava/util/function/Consumer; 115 <appendix> member <vmtarget> ; # org/eclipse/equinox/plurl/impl/PlurlImpl$5$$Lambda+0x0000018610116700
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl add (Ljava/net/URLStreamHandlerFactory;)V 13 <appendix> argL0 ; # org/eclipse/equinox/plurl/impl/PlurlImpl$$Lambda+0x00000186101164b0
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlFactoryHolder
instanceKlass  @bci org/eclipse/equinox/plurl/impl/PlurlImpl$5 getContent ()Ljava/util/function/Consumer; 101 <appendix> member <vmtarget> ; # org/eclipse/equinox/plurl/impl/PlurlImpl$5$$Lambda+0x0000018610113c48
instanceKlass  @cpi java/util/logging/LogRecord 420 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610115800
instanceKlass org/eclipse/osgi/internal/url/ContentHandlerFactoryImpl
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$PlurlContentHandlerFactory
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl$LegacyFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610115400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610115000
instanceKlass org/eclipse/equinox/plurl/impl/StackWalkerCallStack
instanceKlass org/eclipse/equinox/plurl/impl/URLToHandler
instanceKlass java/net/UrlDeserializedState
instanceKlass java/net/InetAddress
instanceKlass org/eclipse/equinox/plurl/impl/CallStack
instanceKlass org/eclipse/equinox/plurl/PlurlStreamHandler$PlurlSetter
instanceKlass java/net/ContentHandler
instanceKlass org/eclipse/equinox/plurl/impl/PlurlImpl
instanceKlass org/eclipse/osgi/internal/log/ConfigAdminListener
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610114c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610114800
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610114400
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x00000186101110b8
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1105 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610114000
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableCollection
instanceKlass org/osgi/util/tracker/AbstractTracked
instanceKlass org/osgi/util/tracker/ServiceTracker
instanceKlass org/eclipse/osgi/internal/log/EventAdminAdapter
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$2
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot$SnapshotIterator
instanceKlass org/eclipse/osgi/framework/eventmgr/ListenerQueue
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventListenerHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Map;)V 77 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000001861010bcb8
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Collection;)V 77 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000001861010b6d0
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot
instanceKlass org/osgi/framework/PrototypeServiceFactory
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610109000
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000001861010f520
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1128 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610108c00
instanceKlass org/eclipse/osgi/internal/serviceregistry/HookContext
instanceKlass org/osgi/framework/UnfilteredServiceListener
instanceKlass org/eclipse/osgi/internal/serviceregistry/FilteredServiceListener
instanceKlass org/osgi/framework/hooks/service/ListenerHook$ListenerInfo
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Entry
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap
instanceKlass org/eclipse/osgi/internal/log/OrderedExecutor
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl$2
instanceKlass org/osgi/service/startlevel/StartLevel
instanceKlass org/osgi/service/packageadmin/PackageAdmin
instanceKlass org/eclipse/equinox/plurl/PlurlContentHandlerFactory
instanceKlass java/net/ContentHandlerFactory
instanceKlass org/eclipse/equinox/plurl/PlurlStreamHandlerFactory
instanceKlass org/eclipse/equinox/plurl/PlurlFactory
instanceKlass org/eclipse/equinox/plurl/Plurl
instanceKlass org/eclipse/osgi/internal/framework/SystemBundleActivator
instanceKlass org/eclipse/osgi/internal/loader/classpath/TitleVersionVendor
instanceKlass org/eclipse/osgi/internal/loader/classpath/ManifestPackageAttributes
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry$PDEData
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry
instanceKlass org/eclipse/osgi/internal/loader/classpath/FragmentClasspath
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock$1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore
instanceKlass org/eclipse/osgi/internal/loader/BundleLoaderSources
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$1
instanceKlass org/eclipse/osgi/internal/loader/sources/PackageSource
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver$StorageSaverTask
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 92 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x00000186100cda88
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 76 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100cd848
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 66 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100cd5f8
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 47 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x00000186100cd3c0
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 31 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100cd180
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 21 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100ccf30
instanceKlass  @cpi org/eclipse/core/internal/filesystem/local/LocalFile 759 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610108800
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 59 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100ccd00
instanceKlass  @cpi org/eclipse/core/internal/net/Policy 113 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610108400
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 49 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100ccac0
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 39 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100cc880
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 29 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x00000186100cc628
instanceKlass  @cpi java/lang/SecurityManager 518 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610108000
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 17 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x00000186100cc3e8
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$1
instanceKlass org/osgi/framework/ServiceObjects
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClassListener
instanceKlass org/osgi/framework/hooks/weaving/WeavingHook
instanceKlass org/osgi/framework/hooks/service/FindHook
instanceKlass org/osgi/framework/hooks/service/EventListenerHook
instanceKlass org/osgi/framework/hooks/service/EventHook
instanceKlass org/osgi/framework/hooks/bundle/FindHook
instanceKlass org/osgi/framework/hooks/bundle/EventHook
instanceKlass org/osgi/framework/hooks/bundle/CollisionHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$FrameworkHookHolder
instanceKlass org/osgi/framework/hooks/service/ListenerHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl
instanceKlass org/osgi/framework/ServiceRegistration
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager
instanceKlass org/eclipse/osgi/internal/framework/EquinoxEventPublisher
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleWiring$LoaderInitializer
instanceKlass org/eclipse/osgi/container/ModuleWiring
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$1
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removeNonEffectiveRequirements (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList;)V 57 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x0000018610101410
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$6
instanceKlass org/eclipse/osgi/container/ModuleWire
instanceKlass org/osgi/framework/wiring/BundleWire
instanceKlass org/apache/felix/resolver/WrappedRequirement
instanceKlass org/apache/felix/resolver/WireImpl
instanceKlass org/apache/felix/resolver/ResolverImpl$6
instanceKlass org/apache/felix/resolver/ResolverImpl$5
instanceKlass org/apache/felix/resolver/ResolverImpl$4
instanceKlass org/apache/felix/resolver/Blame
instanceKlass org/apache/felix/resolver/ResolverImpl$3
instanceKlass org/apache/felix/resolver/Packages
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass org/apache/felix/resolver/WireCandidate
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/ThreadPoolExecutor$CallerRunsPolicy
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$2
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Executors
instanceKlass org/apache/felix/resolver/EnhancedExecutor$1
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass org/apache/felix/resolver/ResolverImpl$1Computer
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/apache/felix/resolver/EnhancedExecutor
instanceKlass org/apache/felix/resolver/WrappedResource
instanceKlass java/util/ArrayList$SubList$1
instanceKlass org/apache/felix/resolver/util/OpenHashMap$1
instanceKlass org/apache/felix/resolver/util/CopyOnWriteSet
instanceKlass org/apache/felix/resolver/util/CandidateSelector
instanceKlass org/apache/felix/resolver/util/OpenHashMap$MapEntry
instanceKlass org/apache/felix/resolver/util/OpenHashMap$MapIterator
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver$ResolveProcess removeSubstituted (Ljava/util/List;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$ResolveProcess$$Lambda+0x00000186100ba1b8
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removeNonEffectiveCapabilities (Ljava/util/Collection;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x00000186100b9f60
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver$ResolveProcess filterDisabled (Ljava/util/List;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$ResolveProcess$$Lambda+0x00000186100b9d08
instanceKlass  @bci org/eclipse/osgi/internal/framework/FilterImpl$Equal compare_Version (Lorg/osgi/framework/Version;)Z 3 <appendix> argL0 ; # org/eclipse/osgi/internal/framework/FilterImpl$Equal$$Lambda+0x00000186100b9ac8
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$Parser
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl
instanceKlass org/apache/felix/resolver/Candidates$PopulateResult
instanceKlass org/osgi/service/resolver/HostedCapability
instanceKlass org/apache/felix/resolver/Candidates
instanceKlass org/apache/felix/resolver/Util
instanceKlass org/apache/felix/resolver/ResolveSession
instanceKlass org/osgi/resource/Wire
instanceKlass org/apache/felix/resolver/ResolutionError
instanceKlass org/apache/felix/resolver/util/OpenHashMap
instanceKlass org/apache/felix/resolver/ResolverImpl
instanceKlass org/osgi/service/resolver/Resolver
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/LinkedList$Node
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory$CoreResolverHook
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Listener
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport$Builder
instanceKlass org/apache/felix/resolver/Logger
instanceKlass org/osgi/service/resolver/ResolveContext
instanceKlass org/eclipse/osgi/container/ModuleDatabase$1
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock$Permits
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/eclipse/osgi/internal/container/Capabilities$NamespaceSet
instanceKlass org/eclipse/osgi/container/ModuleRevision$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$8
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass org/eclipse/osgi/container/ModuleCapability
instanceKlass org/osgi/framework/wiring/BundleCapability
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$1
instanceKlass org/eclipse/osgi/internal/container/NamespaceList
instanceKlass org/eclipse/osgi/container/ModuleRevision$1
instanceKlass org/osgi/framework/wiring/BundleWiring
instanceKlass org/osgi/resource/Wiring
instanceKlass org/eclipse/osgi/container/ModuleRevision
instanceKlass org/eclipse/osgi/container/ModuleRevisions
instanceKlass org/osgi/framework/wiring/BundleRevisions
instanceKlass org/lombokweb/asm/Opcodes
instanceKlass org/lombokweb/asm/Handler
instanceKlass lombok/patcher/MethodLogistics
instanceKlass org/lombokweb/asm/Label
instanceKlass org/lombokweb/asm/Type
instanceKlass org/lombokweb/asm/Frame
instanceKlass org/lombokweb/asm/Context
instanceKlass org/lombokweb/asm/Attribute
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1
instanceKlass org/lombokweb/asm/ByteVector
instanceKlass org/lombokweb/asm/Symbol
instanceKlass org/lombokweb/asm/SymbolTable
instanceKlass org/lombokweb/asm/FieldVisitor
instanceKlass org/lombokweb/asm/MethodVisitor
instanceKlass org/lombokweb/asm/AnnotationVisitor
instanceKlass org/lombokweb/asm/ModuleVisitor
instanceKlass org/lombokweb/asm/RecordComponentVisitor
instanceKlass org/lombokweb/asm/ClassReader
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle
instanceKlass org/osgi/resource/Capability
instanceKlass org/eclipse/osgi/container/ModuleRequirement
instanceKlass org/osgi/framework/wiring/BundleRequirement
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$2
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensitiveKey
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleEntry
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo$1
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder
instanceKlass org/eclipse/osgi/container/builders/OSGiManifestBuilderFactory
instanceKlass java/util/ComparableTimSort
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186100a9000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186100a8c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186100a8800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x00000186100a8400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x00000186100a8000
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/reflect/TypeVariable
instanceKlass org/eclipse/osgi/storage/BundleInfo$Generation
instanceKlass org/eclipse/osgi/internal/container/LockSet$LockHolder
instanceKlass org/eclipse/osgi/storage/BundleInfo
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring
instanceKlass org/eclipse/osgi/container/ModuleResolver
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock
instanceKlass org/eclipse/osgi/internal/container/LockSet
instanceKlass org/osgi/framework/startlevel/FrameworkStartLevel
instanceKlass org/osgi/framework/wiring/FrameworkWiring
instanceKlass org/osgi/resource/Requirement
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleContainer
instanceKlass org/eclipse/osgi/internal/container/Capabilities
instanceKlass org/eclipse/osgi/container/Module
instanceKlass org/osgi/framework/startlevel/BundleStartLevel
instanceKlass org/eclipse/osgi/container/ModuleDatabase
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1
instanceKlass java/util/concurrent/LinkedTransferQueue$DualNode
instanceKlass java/util/concurrent/TransferQueue
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/eclipse/osgi/internal/container/AtomicLazyInitializer
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$BundleCollisionHook
instanceKlass org/osgi/framework/ServiceReference
instanceKlass org/osgi/framework/hooks/resolver/ResolverHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory
instanceKlass org/osgi/framework/hooks/resolver/ResolverHookFactory
instanceKlass org/eclipse/osgi/container/ModuleCollisionHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$1
instanceKlass org/eclipse/osgi/container/ModuleLoader
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityRow
instanceKlass org/eclipse/osgi/internal/permadmin/PermissionAdminTable
instanceKlass org/osgi/service/permissionadmin/PermissionInfo
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionUpdate
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionInfo
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityAdmin
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionAdmin
instanceKlass org/osgi/service/permissionadmin/PermissionAdmin
instanceKlass org/eclipse/osgi/storage/PermissionData
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/io/DeleteOnExitHook$1
instanceKlass java/io/DeleteOnExitHook
instanceKlass  @bci org/eclipse/osgi/framework/internal/reliablefile/ReliableFile createTempFile (Ljava/lang/String;Ljava/lang/String;Ljava/io/File;)Ljava/io/File; 61 <appendix> argL0 ; # org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$$Lambda+0x0000018610099270
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass java/nio/channels/FileLock
instanceKlass org/eclipse/osgi/internal/location/Locker_JavaNio
instanceKlass org/eclipse/osgi/storagemanager/StorageManager
instanceKlass org/eclipse/osgi/storage/FrameworkExtensionInstaller
instanceKlass org/eclipse/osgi/storage/bundlefile/MRUBundleFileList
instanceKlass org/eclipse/osgi/framework/eventmgr/EventDispatcher
instanceKlass org/eclipse/osgi/storage/ContentProvider
instanceKlass org/osgi/framework/Filter
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleFile
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor
instanceKlass org/eclipse/osgi/storage/Storage
instanceKlass  @bci org/eclipse/osgi/internal/cds/CDSHookConfigurator addHooks (Lorg/eclipse/osgi/internal/hookregistry/HookRegistry;)V 77 <appendix> argL0 ; # org/eclipse/osgi/internal/cds/CDSHookConfigurator$$Lambda+0x000001861009def8
instanceKlass org/eclipse/osgi/signedcontent/SignedContent
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610098400
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory$StorageHook
instanceKlass org/osgi/framework/BundleActivator
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory
instanceKlass org/eclipse/osgi/internal/cds/CDSHookConfigurator
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook
instanceKlass org/eclipse/osgi/signedcontent/SignedContentFactory
instanceKlass org/eclipse/osgi/internal/hookregistry/ActivatorHookFactory
instanceKlass org/osgi/framework/wiring/BundleRevision
instanceKlass org/osgi/resource/Resource
instanceKlass org/eclipse/osgi/internal/connect/ConnectHookConfigurator
instanceKlass org/eclipse/osgi/internal/hookregistry/HookConfigurator
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$ConnectModules
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 20 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x0000018610097500
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 5 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x00000186100972c8
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints 160 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610098000
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory$1
instanceKlass org/eclipse/osgi/framework/log/FrameworkLog
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory
instanceKlass org/osgi/service/log/FormatterLogger
instanceKlass org/eclipse/osgi/internal/log/LoggerImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerAdmin
instanceKlass org/osgi/service/log/admin/LoggerContext
instanceKlass org/eclipse/osgi/internal/log/LoggerContextTargetMap
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager$MockSystemBundle
instanceKlass org/osgi/service/log/admin/LoggerAdmin
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory
instanceKlass org/eclipse/osgi/framework/util/ArrayMap
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory$1
instanceKlass org/osgi/service/log/LogEntry
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory
instanceKlass org/osgi/framework/ServiceFactory
instanceKlass org/eclipse/equinox/log/ExtendedLogReaderService
instanceKlass org/osgi/service/log/LogReaderService
instanceKlass org/eclipse/equinox/log/ExtendedLogService
instanceKlass org/eclipse/equinox/log/Logger
instanceKlass org/osgi/service/log/Logger
instanceKlass org/osgi/service/log/LogService
instanceKlass org/osgi/service/log/LoggerFactory
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager
instanceKlass org/osgi/framework/AllServiceListener
instanceKlass org/osgi/framework/ServiceListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogWriter
instanceKlass org/eclipse/equinox/log/LogFilter
instanceKlass org/eclipse/equinox/log/SynchronousLogListener
instanceKlass org/osgi/service/log/LogListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogServices
instanceKlass org/eclipse/osgi/util/ManifestElement
instanceKlass org/eclipse/osgi/internal/debug/Debug
instanceKlass org/eclipse/osgi/service/debug/DebugOptionsListener
instanceKlass org/eclipse/osgi/service/debug/DebugTrace
instanceKlass org/eclipse/osgi/internal/debug/FrameworkDebugOptions
instanceKlass org/osgi/util/tracker/ServiceTrackerCustomizer
instanceKlass java/nio/file/FileVisitor
instanceKlass org/eclipse/osgi/storage/StorageUtil
instanceKlass org/eclipse/osgi/internal/location/BasicLocation
instanceKlass org/eclipse/osgi/internal/location/EquinoxLocations
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/UUID
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/eclipse/osgi/internal/container/InternalUtils
instanceKlass org/osgi/framework/Version
instanceKlass org/eclipse/osgi/internal/location/Locker
instanceKlass org/eclipse/osgi/internal/location/LocationHelper
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration$ConfigValues
instanceKlass org/eclipse/osgi/internal/util/Tokenizer
instanceKlass sun/util/logging/PlatformLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerAccessor
instanceKlass jdk/internal/logger/LazyLoggers$LoggerAccessor
instanceKlass jdk/internal/logger/AbstractLoggerWrapper
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/lang/System$LoggerFinder
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass jdk/internal/event/EventHelper$ThreadTrackHolder
instanceKlass java/net/URLClassLoader$2
instanceKlass org/eclipse/osgi/internal/framework/AliasMapper
instanceKlass org/eclipse/osgi/framework/util/KeyedElement
instanceKlass org/eclipse/osgi/internal/hookregistry/ClassLoaderHook
instanceKlass org/eclipse/osgi/internal/hookregistry/HookRegistry
instanceKlass org/eclipse/osgi/service/datalocation/Location
instanceKlass org/eclipse/osgi/service/debug/DebugOptions
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration
instanceKlass org/eclipse/osgi/service/environment/EnvironmentInfo
# instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$$InjectedInvoker+0x000001861008c400
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$InjectedInvokerHolder
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861008c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861008ac00
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x00000186100398c0
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x0000018610039680
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x0000018610038f28
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x0000018610038ae8
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x0000018610038250
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/eclipse/osgi/framework/util/SecureAction$1
instanceKlass org/eclipse/osgi/framework/util/SecureAction
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer
instanceKlass org/eclipse/osgi/launch/Equinox
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861008a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861008a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861008a000
instanceKlass java/util/EventObject
instanceKlass org/osgi/framework/BundleContext
instanceKlass org/osgi/framework/BundleReference
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$InitialBundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$StartupEventListener
instanceKlass org/osgi/framework/FrameworkListener
instanceKlass org/osgi/framework/SynchronousBundleListener
instanceKlass java/util/concurrent/Semaphore
instanceKlass org/osgi/framework/launch/Framework
instanceKlass org/osgi/framework/Bundle
instanceKlass org/osgi/framework/BundleListener
instanceKlass java/util/EventListener
instanceKlass org/eclipse/equinox/plurl/PlurlStreamHandler
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x0000018610032258
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass java/net/URLClassLoader$1
instanceKlass  @bci org/eclipse/equinox/launcher/Main basicRun ([Ljava/lang/String;)V 162 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x0000018610084930
instanceKlass java/nio/file/FileStore
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass org/eclipse/equinox/launcher/JNIBridge
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/NetworkChannel
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/Streams
instanceKlass java/nio/channels/Channels
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass sun/nio/fs/WindowsChannelFactory$2
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsPath$1
instanceKlass java/nio/file/Path$1
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryFromFragment (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 96 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x0000018610084490
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryFromFragment (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 86 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x0000018610084250
instanceKlass  @bci java/util/zip/ZipFile stream ()Ljava/util/stream/Stream; 25 <appendix> member <vmtarget> ; # java/util/zip/ZipFile$$Lambda+0x0000018610028bb8
instanceKlass java/util/Spliterators$AbstractSpliterator
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 37 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x0000018610028990
instanceKlass java/util/function/BooleanSupplier
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 24 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x00000186100279b0
instanceKlass java/util/stream/StreamSpliterators
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 190 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x0000018610084000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 180 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001bcc0
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass  @bci java/util/stream/AbstractPipeline spliterator ()Ljava/util/Spliterator; 103 <appendix> member <vmtarget> ; # java/util/stream/AbstractPipeline$$Lambda+0x0000018610026e20
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 143 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001ba80
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 133 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001b858
instanceKlass  @cpi org/eclipse/equinox/launcher/Main 2098 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610082c00
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 117 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001b610
instanceKlass java/util/function/IntUnaryOperator
instanceKlass java/util/stream/Streams$RangeIntSpliterator
instanceKlass java/util/stream/IntStream
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences absolutePath ()Ljava/lang/String; 66 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610082800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610082400
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences absolutePath ()Ljava/lang/String; 66 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610082000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610081c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610081800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610081400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610081000
instanceKlass  @bci org/eclipse/core/internal/preferences/EclipsePreferences absolutePath ()Ljava/lang/String; 66 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610080c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610080800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610080400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610080000
instanceKlass java/io/RandomAccessFile$1
instanceKlass  @bci java/util/logging/SimpleFormatter format (Ljava/util/logging/LogRecord;)Ljava/lang/String; 35 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861001fc00
instanceKlass java/lang/invoke/LambdaFormEditor$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001f800
instanceKlass  @bci java/util/logging/SimpleFormatter format (Ljava/util/logging/LogRecord;)Ljava/lang/String; 35 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861001f400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001f000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001ec00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001e800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001e400
instanceKlass  @bci java/util/logging/SimpleFormatter format (Ljava/util/logging/LogRecord;)Ljava/lang/String; 35 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001861001e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861001d400
instanceKlass  @bci org/eclipse/equinox/launcher/Main searchFor (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 95 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001b3c8
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 34 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001b188
instanceKlass java/util/stream/ReduceOps$2ReducingSink
instanceKlass  @bci java/util/function/BinaryOperator maxBy (Ljava/util/Comparator;)Ljava/util/function/BinaryOperator; 6 <appendix> member <vmtarget> ; # java/util/function/BinaryOperator$$Lambda+0x00000186100240c8
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;Ljava/util/Comparator;)Ljava/util/Comparator; 12 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x0000018610023e28
instanceKlass  @cpi java/util/Comparator 256 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861001d000
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x0000018610023b88
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 34 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000001861001af48
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 18 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000001861001ad28
instanceKlass  @bci java/util/Comparator thenComparing (Ljava/util/Comparator;)Ljava/util/Comparator; 7 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x00000186100238e8
instanceKlass  @cpi java/util/Comparator 251 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861001cc00
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 8 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000001861001ab08
instanceKlass  @bci java/util/Comparator comparingInt (Ljava/util/function/ToIntFunction;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x0000018610023648
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 462 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001861001c800
instanceKlass  @cpi org/eclipse/osgi/container/ModuleResolver 741 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861001c400
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000001861001a8e8
instanceKlass java/util/function/ToIntFunction
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 18 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001a6a8
instanceKlass  @cpi org/eclipse/jdt/internal/launching/StandardVMType 1163 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001861001c000
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 8 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001a250
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass  @bci org/eclipse/lsp4j/jsonrpc/services/AnnotationUtil getSegment (Ljava/lang/Class;)Ljava/lang/String; 25 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610019c00
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci java/util/stream/ReferencePipeline toArray ()[Ljava/lang/Object; 1 <appendix> argL0 ; # java/util/stream/ReferencePipeline$$Lambda+0x00000186100227c8
instanceKlass java/util/ImmutableCollections$Access$1
instanceKlass jdk/internal/access/JavaUtilCollectionAccess
instanceKlass java/util/ImmutableCollections$Access
instanceKlass  @bci org/eclipse/equinox/launcher/Main getArrayFromList (Ljava/lang/String;)Ljava/util/List; 32 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000001861001a000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getArrayFromList (Ljava/lang/String;)Ljava/util/List; 22 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x0000018610015d00
instanceKlass java/util/regex/Pattern$1MatcherIterator
instanceKlass sun/net/www/MimeEntry
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder$1
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder
instanceKlass sun/net/www/MimeTable$2
instanceKlass sun/net/www/MimeTable$1
instanceKlass sun/net/www/MimeTable
instanceKlass java/net/URLConnection$1
instanceKlass java/net/FileNameMap
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610019800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610019400
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610019000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610018c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610018800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610018400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610018000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610017c00
instanceKlass  @bci java/util/logging/SimpleFormatter format (Ljava/util/logging/LogRecord;)Ljava/lang/String; 35 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610017800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610017400
instanceKlass  @bci org/eclipse/core/internal/resources/LocalMetaArea getSafeTableLocationFor (Ljava/lang/String;)Lorg/eclipse/core/runtime/IPath; 44 <appendix> argL1 argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610017000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610016c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610016800
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610016400
instanceKlass java/util/Collections$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass  @bci org/eclipse/equinox/launcher/Main basicRun ([Ljava/lang/String;)V 29 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x0000018610015ae0
instanceKlass java/util/function/IntFunction
instanceKlass  @bci org/eclipse/equinox/launcher/Main processCommandLine (Ljava/util/List;)Ljava/util/List; 832 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x00000186100158b0
instanceKlass  @cpi org/eclipse/core/internal/events/NotificationManager$NotifyJob 101 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610016000
instanceKlass  @bci java/util/ArrayDeque copyElements (Ljava/util/Collection;)V 2 <appendix> member <vmtarget> ; # java/util/ArrayDeque$$Lambda+0x0000018610020000
instanceKlass java/lang/Thread$ThreadNumbering
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/security/Policy
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/eclipse/equinox/launcher/Main
instanceKlass sun/security/util/ManifestEntryVerifier$SunProviderHolder
instanceKlass java/util/Base64$Encoder
instanceKlass java/util/Base64$Decoder
instanceKlass java/util/Base64
instanceKlass javax/crypto/SecretKey
instanceKlass sun/security/util/Length
instanceKlass sun/security/util/KeyUtil
instanceKlass java/security/interfaces/XECKey
instanceKlass java/security/interfaces/ECKey
instanceKlass sun/security/util/JarConstraintsParameters
instanceKlass sun/security/util/ConstraintsParameters
instanceKlass java/security/CodeSigner
instanceKlass java/security/Timestamp
instanceKlass sun/security/timestamp/TimestampToken
instanceKlass java/security/cert/CertPath
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610014c00
instanceKlass java/math/MutableBigInteger
instanceKlass sun/security/rsa/RSAPadding
instanceKlass sun/security/rsa/RSACore
instanceKlass java/security/interfaces/RSAPrivateCrtKey
instanceKlass sun/security/pkcs/PKCS8Key
instanceKlass sun/security/util/InternalPrivateKey
instanceKlass java/security/interfaces/RSAPrivateKey
instanceKlass java/security/PrivateKey
instanceKlass javax/security/auth/Destroyable
instanceKlass jdk/internal/icu/util/CodePointTrie$Data
instanceKlass jdk/internal/icu/util/CodePointMap
instanceKlass jdk/internal/icu/util/VersionInfo
instanceKlass  @bci jdk/internal/module/SystemModuleFinders$SystemModuleReader open (Ljava/lang/String;)Ljava/util/Optional; 6 <appendix> member <vmtarget> ; # jdk/internal/module/SystemModuleFinders$SystemModuleReader$$Lambda+0x0000018610077e38
instanceKlass jdk/internal/jimage/decompressor/ZipDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorFactory
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorRepository
instanceKlass jdk/internal/jimage/decompressor/CompressedResourceHeader
instanceKlass  @bci jdk/internal/jimage/BasicImageReader getResourceBuffer (Ljdk/internal/jimage/ImageLocation;)Ljava/nio/ByteBuffer; 168 <appendix> member <vmtarget> ; # jdk/internal/jimage/BasicImageReader$$Lambda+0x0000018610076d78
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor$StringsProvider
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass jdk/internal/module/Checks
instanceKlass jdk/internal/icu/impl/ICUBinary$1
instanceKlass jdk/internal/icu/impl/ICUBinary
instanceKlass jdk/internal/icu/impl/NormalizerImpl$IsAcceptable
instanceKlass jdk/internal/icu/impl/ICUBinary$Authenticate
instanceKlass jdk/internal/icu/impl/NormalizerImpl
instanceKlass jdk/internal/icu/impl/Norm2AllModes$Norm2AllModesSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes
instanceKlass jdk/internal/icu/text/Normalizer2
instanceKlass jdk/internal/icu/text/NormalizerBase$ModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$NFKDModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$1
instanceKlass jdk/internal/icu/text/NormalizerBase$Mode
instanceKlass jdk/internal/icu/text/NormalizerBase
instanceKlass java/text/Normalizer
instanceKlass sun/security/x509/AVAKeyword
instanceKlass java/util/StringJoiner
instanceKlass sun/security/jca/ServiceId
instanceKlass java/security/Signature$1
instanceKlass jdk/internal/access/JavaSecuritySignatureAccess
instanceKlass java/security/SignatureSpi
instanceKlass sun/security/util/SignatureUtil
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/pkcs/SigningCertificateInfo$ESSCertId
instanceKlass sun/security/pkcs/SigningCertificateInfo
instanceKlass sun/security/pkcs/PKCS9Attribute
instanceKlass sun/security/pkcs/PKCS9Attributes
instanceKlass java/time/Instant
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000c
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/ZoneId
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000025
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass java/util/regex/CharPredicates
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints$Holder
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass java/util/StringTokenizer
instanceKlass java/security/spec/ECFieldF2m
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/DisabledAlgorithmConstraints$JarHolder
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x0000018610069470
instanceKlass java/util/regex/ASCII
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/pkcs/SignerInfo
instanceKlass java/security/cert/PolicyQualifierInfo
instanceKlass sun/security/x509/CertificatePolicyId
instanceKlass sun/security/x509/PolicyInformation
instanceKlass sun/security/x509/DistributionPoint
instanceKlass sun/security/x509/DNSName
instanceKlass sun/security/x509/URIName
instanceKlass sun/security/x509/GeneralName
instanceKlass sun/security/x509/AccessDescription
instanceKlass sun/security/x509/GeneralNames
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/lang/System$Logger
instanceKlass jdk/internal/event/EventHelper
instanceKlass sun/security/jca/JCAUtil
instanceKlass sun/security/util/MemoryCache$CacheEntry
instanceKlass sun/security/x509/KeyIdentifier
instanceKlass java/util/TreeMap$Entry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610014800
instanceKlass sun/security/x509/OIDMap$OIDInfo
instanceKlass sun/security/x509/PKIXExtensions
instanceKlass sun/security/x509/OIDMap
instanceKlass sun/security/x509/Extension
instanceKlass java/security/cert/Extension
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass sun/security/x509/CertificateExtensions
instanceKlass sun/security/rsa/RSAUtil
instanceKlass java/security/interfaces/RSAPublicKey
instanceKlass java/security/interfaces/RSAKey
instanceKlass java/security/spec/PSSParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/RSAPrivateKeySpec
instanceKlass java/security/spec/RSAPublicKeySpec
instanceKlass java/security/KeyFactorySpi
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/security/jca/ProviderList$ServiceList$1
instanceKlass java/security/KeyFactory
instanceKlass  @bci java/security/spec/EncodedKeySpec <clinit> ()V 0 <appendix> argL0 ; # java/security/spec/EncodedKeySpec$$Lambda+0x000001861005fa70
instanceKlass  @cpi org/eclipse/jdt/internal/core/search/processing/JobManager 551 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610014400
instanceKlass jdk/internal/access/JavaSecuritySpecAccess
instanceKlass java/security/spec/EncodedKeySpec
instanceKlass java/security/spec/KeySpec
instanceKlass sun/security/util/BitArray
instanceKlass sun/security/x509/X509Key
instanceKlass java/security/PublicKey
instanceKlass java/security/Key
instanceKlass sun/security/x509/CertificateX509Key
instanceKlass java/util/Date
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass sun/security/x509/CertificateValidity
instanceKlass sun/security/x509/AVA
instanceKlass sun/security/x509/RDN
instanceKlass javax/security/auth/x500/X500Principal
instanceKlass  @bci sun/security/x509/X500Name <clinit> ()V 153 <appendix> argL0 ; # sun/security/x509/X500Name$$Lambda+0x000001861005d568
instanceKlass sun/security/x509/X500Name
instanceKlass sun/security/x509/GeneralNameInterface
instanceKlass sun/security/x509/CertificateAlgorithmId
instanceKlass sun/security/x509/SerialNumber
instanceKlass sun/security/x509/CertificateSerialNumber
instanceKlass sun/security/x509/CertificateVersion
instanceKlass sun/security/x509/X509CertInfo
instanceKlass sun/security/util/Cache$EqualByteArray
instanceKlass java/security/cert/X509Extension
instanceKlass java/lang/Byte$ByteCache
instanceKlass  @bci sun/security/util/DerInputStream seeOptionalContextSpecific (I)Z 2 <appendix> member <vmtarget> ; # sun/security/util/DerInputStream$$Lambda+0x000001861005ae98
instanceKlass  @cpi sun/security/util/DerInputStream 295 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610014000
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/util/Cache
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/cert/CertificateFactorySpi
instanceKlass java/security/cert/CertificateFactory
instanceKlass sun/security/x509/AlgorithmId
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerValue
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/pkcs/ContentInfo
instanceKlass sun/security/util/DerEncoder
instanceKlass sun/security/util/DerInputStream
instanceKlass sun/security/pkcs/PKCS7
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass javax/security/auth/login/Configuration$Parameters
instanceKlass java/security/Policy$Parameters
instanceKlass java/security/cert/CertStoreParameters
instanceKlass java/security/SecureRandomParameters
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass  @bci sun/security/util/ManifestDigester <init> ([B)V 350 <appendix> argL0 ; # sun/security/util/ManifestDigester$$Lambda+0x000001861004e8b8
instanceKlass sun/security/util/ManifestDigester$Section
instanceKlass sun/security/util/ManifestDigester$Entry
instanceKlass sun/security/util/ManifestDigester$Position
instanceKlass sun/security/util/ManifestDigester
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/launcher/LauncherHelper
instanceKlass lombok/patcher/scripts/ScriptBuilder$SetSymbolDuringMethodCallBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder$ReplaceMethodCallBuilder
instanceKlass lombok/eclipse/agent/EclipsePatcher$4
instanceKlass lombok/eclipse/agent/EclipsePatcher$3
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapMethodCallBuilder
instanceKlass lombok/patcher/ScriptManager$WitnessAction
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapReturnValueBuilder
instanceKlass lombok/patcher/ClassRootFinder
instanceKlass lombok/patcher/scripts/ScriptBuilder$AddFieldBuilder
instanceKlass java/util/Collections$1
instanceKlass lombok/patcher/PatchScript$MethodPatcherFactory
instanceKlass org/lombokweb/asm/ClassVisitor
instanceKlass lombok/patcher/Hook
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000031
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass lombok/patcher/MethodTarget
instanceKlass lombok/patcher/scripts/ScriptBuilder$ExitEarlyBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder
instanceKlass lombok/eclipse/agent/EclipseLoaderPatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher$2
instanceKlass lombok/eclipse/agent/EclipsePatcher$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861000d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861000d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861000d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001861000cc00
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000001861000c800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001861000c400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001861000c000
instanceKlass java/lang/instrument/ClassDefinition
instanceKlass lombok/patcher/ScriptManager$OurClassFileTransformer
instanceKlass lombok/patcher/Filter$1
instanceKlass lombok/patcher/TransplantMapper$1
instanceKlass java/lang/instrument/ClassFileTransformer
instanceKlass lombok/patcher/ScriptManager
instanceKlass lombok/patcher/TransplantMapper
instanceKlass lombok/patcher/Filter
instanceKlass lombok/patcher/PatchScript
instanceKlass lombok/patcher/TargetMatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher
instanceKlass lombok/core/AgentLauncher$AgentLaunchable
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610008c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610008800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610008400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610008000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610007c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610007800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610007400
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610007000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610006c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610006800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610006400
instanceKlass  @cpi org/eclipse/osgi/container/ModuleResolver$ResolveProcess 1261 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610006000
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x0000018610005c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610005800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610005400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610005000
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610004c00
instanceKlass sun/invoke/util/ValueConversions$1
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x0000018610004800
instanceKlass lombok/core/AgentLauncher$AgentInfo
instanceKlass lombok/core/AgentLauncher
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass sun/net/www/MessageHeader
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLConnection
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/net/URLDecoder
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000029
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/util/Arrays$ArrayItr
instanceKlass lombok/launch/PackageShader
instanceKlass java/io/Reader
instanceKlass lombok/launch/Main
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 136 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x0000018610002c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610002800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610002400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610002000
instanceKlass  @bci org/eclipse/jdt/ls/core/internal/handlers/BundleUtils startBundle (Ljava/lang/String;)V 5 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610001c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610001800
instanceKlass sun/instrument/InstrumentationImpl$1
instanceKlass lombok/launch/Agent
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass  @bci sun/instrument/InstrumentationImpl <clinit> ()V 16 <appendix> argL0 ; # sun/instrument/InstrumentationImpl$$Lambda+0x0000018610044270
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x0000018610043af8
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x00000186100431a0
instanceKlass  @bci jdk/internal/module/ModuleBootstrap decode (Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/Map; 193 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000018610042f60
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/SequencedMap
instanceKlass java/util/SequencedSet
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 791 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000018610042530
instanceKlass  @cpi org/eclipse/lsp4j/jsonrpc/services/ServiceEndpoints 155 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x0000018610000c00
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 779 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x00000186100422d8
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 767 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000018610041ea0
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 757 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x0000018610041c60
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000048
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004b
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass  @bci jdk/internal/module/DefaultRoots exportsAPI (Ljava/lang/module/ModuleDescriptor;)Z 9 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000051
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000040
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000039
instanceKlass java/util/function/BiConsumer
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000045
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 42 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x80000004e
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 32 <appendix> member <vmtarget> ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000052
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 21 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x80000004f
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass  @bci java/util/logging/LogRecord$CallerFinder <clinit> ()V 0 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x0000018610000800
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 11 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000050
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/LambdaMetafactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x0000018610000400
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/Void
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass jdk/internal/util/StrongReferenceKey
instanceKlass jdk/internal/util/ReferenceKey
instanceKlass jdk/internal/util/ReferencedKeyMap
instanceKlass java/lang/invoke/MethodType$1
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass sun/nio/cs/GBK$EncodeHolder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass java/lang/StringCoding
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/DoubleByte
instanceKlass sun/nio/cs/GBK$DecodeHolder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/DelegatableDecoder
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciMethod java/lang/Object equals (Ljava/lang/Object;)Z 1024 0 6548 0 -1
ciMethod java/lang/Object toString ()Ljava/lang/String; 156 0 5217 0 -1
ciMethod java/lang/Object hashCode ()I 256 0 128 0 -1
ciMethod java/lang/Object clone ()Ljava/lang/Object; 256 0 128 0 -1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 7 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/ByteArrayInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass com/sun/jna/Native$6
instanceKlass org/eclipse/osgi/internal/permadmin/EquinoxSecurityManager
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$ClassContext
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$Finder
ciInstanceKlass java/lang/SecurityManager 1 1 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 11 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 10 12 1 10 12 1 18 12 1 18 10 7 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 7 12 1 1 10 7 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 7 12 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager nonExportedPkgs Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$GenerationProtectionDomain
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 7 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 7 12 1 9 7 12 1 1 1 7 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 7 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 7 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 14 0 64419 0 1304
ciMethod java/lang/StringBuilder length ()I 550 0 29967 0 80
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Flushable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
instanceKlass java/text/ParseException
instanceKlass org/eclipse/equinox/security/storage/StorageException
instanceKlass javax/xml/transform/TransformerException
instanceKlass org/eclipse/jface/text/templates/TemplateException
instanceKlass lombok/eclipse/agent/PatchDelegate$CantMakeDelegates
instanceKlass org/eclipse/jface/text/BadLocationException
instanceKlass org/eclipse/jdt/core/compiler/InvalidInputException
instanceKlass org/eclipse/jdt/internal/compiler/classfmt/ClassFormatException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass org/osgi/service/application/ApplicationException
instanceKlass org/eclipse/core/runtime/CoreException
instanceKlass org/osgi/service/prefs/BackingStoreException
instanceKlass org/apache/felix/scr/impl/inject/methods/SuitableMethodNotAccessibleException
instanceKlass org/xml/sax/SAXException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/osgi/service/resolver/ResolutionException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLockException
instanceKlass java/security/PrivilegedActionException
instanceKlass org/osgi/framework/InvalidSyntaxException
instanceKlass org/osgi/framework/BundleException
instanceKlass java/lang/InterruptedException
instanceKlass sun/nio/fs/WindowsException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/instrument/UnmodifiableClassException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass lombok/eclipse/agent/PatchDelegate$DelegateRecursion
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 7 12 1 1 1 7 1 10 12 10 12 1 10 12 1 7 1 10 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 7 1 10 12 1 9 12 1 1 10 12 1 10 7 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
ciMethod java/lang/Throwable toString ()Ljava/lang/String; 512 0 2685 0 0
ciMethod java/lang/Throwable fillInStackTrace ()Ljava/lang/Throwable; 512 0 2333 0 0
ciMethod java/lang/Throwable getCause ()Ljava/lang/Throwable; 0 0 1528 0 0
ciMethod java/lang/Throwable getMessage ()Ljava/lang/String; 256 0 128 0 -1
ciMethod java/lang/Throwable getLocalizedMessage ()Ljava/lang/String; 512 0 2685 0 0
instanceKlass org/eclipse/core/internal/resources/SaveManager$MasterTable
instanceKlass org/eclipse/core/internal/preferences/SortedProperties
instanceKlass org/eclipse/osgi/util/NLS$MessagesProperties
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 10 12 1 7 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass org/apache/felix/scr/impl/helper/ReadOnlyDictionary
instanceKlass org/osgi/framework/FrameworkUtil$MapAsDictionary
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$SystemBundle$SystemBundleHeaders
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$UnmodifiableDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap
instanceKlass org/eclipse/osgi/storage/BundleInfo$CachedManifest
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1443 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 100 1 10 10 100 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 982 0 13805 0 400
ciMethod java/lang/String toString ()Ljava/lang/String; 516 0 9655 0 80
ciMethod java/lang/String length ()I 576 0 531394 0 112
ciMethod java/lang/String isEmpty ()Z 516 0 41593 0 -1
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/VM 1 1 320 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 9 12 1 1 9 12 1 9 12 1 3 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 11 7 12 1 1 1 7 1 10 100 12 1 1 1 10 12 1 8 1 8 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 5 0 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 100 1 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 5 0 10 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 7 1 1 1 1
staticfield jdk/internal/misc/VM lock Ljava/lang/Object; java/lang/Object
ciInstanceKlass java/lang/InternalError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass javax/xml/parsers/FactoryConfigurationError
instanceKlass java/lang/ThreadDeath
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Set 1 1 144 100 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 7 1 7 1 11 7 12 1 1 1 11 12 1 1 7 1 10 12 1 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map$Entry 1 0 178 18 12 1 1 7 1 100 1 18 10 100 12 1 1 1 18 12 1 18 100 1 11 7 12 1 1 1 11 12 1 11 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 8 10 7 1 10 12 1 8 10 12 1 8 1 10 12 1 8 10 12 1 8 1 10 12 1 1 8 1 100 1 8 1 10 12 1 1 11 12 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 16 3 3 15 11 12 15 11 12 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1
instanceKlass org/eclipse/jdt/ls/core/internal/ConnectionStreamFactory$NamedPipeInputStream
instanceKlass sun/nio/ch/ChannelInputStream
instanceKlass org/eclipse/core/internal/resources/ContentDescriptionManager$LazyFileInputStream
instanceKlass java/io/ObjectInputStream
instanceKlass org/eclipse/core/internal/localstore/SafeChunkyInputStream
instanceKlass org/eclipse/core/internal/registry/BufferedRandomInputStream
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$RewindableInputStream
instanceKlass org/eclipse/osgi/storage/url/reference/ReferenceInputStream
instanceKlass java/util/jar/JarVerifier$VerifierStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 7 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$1
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 7 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 7 12 1 1 8 1 10 7 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 7 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/sun/jna/internal/Cleaner$CleanerThread
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/eclipse/core/internal/jobs/InternalWorker
instanceKlass org/eclipse/core/internal/jobs/Worker
instanceKlass java/util/TimerThread
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread
instanceKlass org/eclipse/equinox/launcher/Main$SplashHandler
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 7 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciMethod java/lang/Thread interrupt ()V 0 0 1 0 0
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 7 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciMethod java/lang/System identityHashCode (Ljava/lang/Object;)I 256 0 128 0 -1
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 256 0 128 0 -1
ciMethod java/lang/System lineSeparator ()Ljava/lang/String; 208 0 6667 0 -1
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/StringLatin1 1 1 392 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 7 1 8 1 10 12 1 8 1 10 12 1 1 100 1 10 10 12 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 460 7 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 5 0 5 0 7 1 3 5 0 3 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 100 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 10 12 1 4 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 9 12 1 1 10 12 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/ArraysSupport 1 1 378 7 1 7 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 9 12 1 10 12 1 1 10 12 7 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 100 1 10 12 1 100 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 9 12 1 1 11 100 12 1 1 1 9 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 12 1 7 1 8 1 8 1 8 1 10 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield jdk/internal/util/ArraysSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/util/ArraysSupport BIG_ENDIAN Z 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BOOLEAN_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BYTE_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_CHAR_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_SHORT_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_INT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_LONG_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_FLOAT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_DOUBLE_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_BYTE_BIT_SIZE I 3
staticfield jdk/internal/util/ArraysSupport JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/util/Arrays 1 1 1029 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 100 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 10 12 1 10 12 1 10 12 10 12 1 11 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 7 1 10 100 1 10 100 1 10 100 1 10 100 1 10 100 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 7 1 10 12 1 9 10 12 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 100 1 10 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 12 1 8 1 10 11 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 18 12 1 1 11 12 1 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 11 100 12 1 1 1 18 12 1 100 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 100 1 100 1 1
staticfield java/util/Arrays $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringLatin1 equals ([B[B)Z 534 462 7508 0 -1
ciMethod java/lang/StringLatin1 inflate ([BI[BII)V 0 0 4006 0 -1
ciInstanceKlass java/lang/StringUTF16 1 1 604 7 1 7 1 10 7 12 1 1 1 100 1 10 7 1 3 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 7 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/math/BigDecimal
instanceKlass com/google/gson/internal/LazilyParsedNumber
instanceKlass com/sun/jna/IntegerType
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StringUTF16 compress ([BII)[B 0 0 7499 0 0
ciMethod java/lang/StringUTF16 compress ([BI[BII)I 766 1902 7499 0 -1
ciMethod java/lang/StringUTF16 newBytesFor (I)[B 0 0 2082 0 -1
ciMethod java/lang/StringUTF16 putCharsAt ([BICCCC)I 0 0 1 0 -1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 907 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 18 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 8 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 7 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
ciMethod java/lang/ref/Reference get ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/ref/Reference refersToImpl (Ljava/lang/Object;)Z 518 0 9393 0 -1
ciMethod java/lang/ref/Reference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 512 0 17636 0 144
ciMethod java/lang/ref/Reference refersTo (Ljava/lang/Object;)Z 514 0 15363 0 -1
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$SoftRef
instanceKlass sun/security/util/MemoryCache$SoftCacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/ResourceBundle$KeyElementReference
instanceKlass com/sun/jna/CallbackReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore$LockWeakRef
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass jdk/internal/util/WeakReferenceKey
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/ref/WeakReference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 602 0 6551 0 0
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass com/sun/jna/internal/Cleaner$CleanerRef
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass java/lang/StackStreamFactory$WalkerState
instanceKlass java/lang/StackWalker$ExtendedOption
instanceKlass com/google/gson/Strictness
instanceKlass com/google/gson/ToNumberPolicy
instanceKlass com/google/gson/FieldNamingPolicy
instanceKlass com/google/gson/LongSerializationPolicy
instanceKlass org/eclipse/jdt/ls/core/internal/ServiceStatus
instanceKlass org/eclipse/jdt/ls/core/internal/DiagnosticsState$ErrorLevel
instanceKlass javax/xml/catalog/CatalogFeatures$State
instanceKlass jdk/xml/internal/XMLSecurityManager$NameMap
instanceKlass jdk/xml/internal/XMLSecurityManager$Processor
instanceKlass jdk/xml/internal/XMLSecurityManager$Limit
instanceKlass com/sun/org/apache/xalan/internal/utils/XMLSecurityPropertyManager$Property
instanceKlass com/sun/org/apache/xalan/internal/utils/FeaturePropertyBase$State
instanceKlass jdk/xml/internal/JdkProperty$ImplPropMap
instanceKlass jdk/xml/internal/JdkXmlFeatures$XmlFeature
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/CodeGenerationTemplate
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$SearchScope
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/ProjectEncodingMode
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/InlayHintsParameterMode
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionGuessMethodArgumentsMode
instanceKlass org/eclipse/jdt/ls/core/internal/handlers/CompletionMatchCaseMode
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$FeatureStatus
instanceKlass org/eclipse/jdt/ls/core/internal/preferences/Preferences$Severity
instanceKlass lombok/eclipse/agent/PatchDelegate$DelegateReceiver
instanceKlass org/apache/felix/scr/impl/inject/field/FieldHandler$METHOD_TYPE
instanceKlass java/security/DrbgParameters$Capability
instanceKlass java/math/RoundingMode
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass java/time/format/TextStyle
instanceKlass java/time/format/DateTimeFormatterBuilder$SettingsParser
instanceKlass java/util/Locale$Category
instanceKlass java/time/format/ResolverStyle
instanceKlass java/time/format/SignStyle
instanceKlass java/time/temporal/JulianFields$Field
instanceKlass java/time/temporal/IsoFields$Unit
instanceKlass java/time/temporal/IsoFields$Field
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils$ValueType
instanceKlass java/nio/file/StandardCopyOption
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegState
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager$State
instanceKlass org/osgi/util/promise/PromiseFactory$Option
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata$ReferenceScope
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata$Scope
instanceKlass org/apache/felix/scr/impl/metadata/DSVersion
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner$NameType
instanceKlass com/sun/org/apache/xerces/internal/util/Status
instanceKlass javax/xml/catalog/CatalogFeatures$Feature
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$NameMap
instanceKlass jdk/xml/internal/JdkProperty$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$Limit
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger$Level
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ContainerEvent
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Entry$Type
instanceKlass java/util/Comparators$NaturalOrderComparator
instanceKlass org/eclipse/osgi/container/Module$StartOptions
instanceKlass java/lang/StackWalker$Option
instanceKlass org/apache/felix/resolver/PermutationType
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Sort
instanceKlass org/eclipse/osgi/container/Module$Settings
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent
instanceKlass org/eclipse/osgi/storage/ContentProvider$Type
instanceKlass org/eclipse/osgi/container/Module$State
instanceKlass org/osgi/service/log/LogLevel
instanceKlass sun/util/logging/PlatformLogger$Level
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass java/nio/file/AccessMode
instanceKlass java/nio/file/attribute/PosixFilePermission
instanceKlass jdk/internal/icu/util/CodePointTrie$ValueWidth
instanceKlass jdk/internal/icu/util/CodePointTrie$Type
instanceKlass java/text/Normalizer$Form
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint$Operator
instanceKlass java/lang/System$Logger$Level
instanceKlass sun/security/rsa/RSAUtil$KeyType
instanceKlass sun/security/util/KnownOIDs
instanceKlass lombok/patcher/StackRequest
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/util/stream/StreamShape
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
instanceKlass java/lang/ref/ReferenceQueue$Null
instanceKlass java/lang/ref/NativeReferenceQueue
ciInstanceKlass java/lang/ref/ReferenceQueue 1 1 183 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 9 7 12 1 1 1 11 12 1 10 7 12 1 1 9 12 1 1 7 1 10 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 7 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 5 0 10 10 12 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 11 100 12 1 1 1 10 7 12 1 1 7 1 10 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/ReferenceQueue NULL Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue$Null
staticfield java/lang/ref/ReferenceQueue ENQUEUED Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue$Null
staticfield java/lang/ref/ReferenceQueue $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 1 1 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
ciMethod java/lang/StringBuffer append (Ljava/lang/CharSequence;II)Ljava/lang/StringBuffer; 768 0 5560 0 0
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/eclipse/equinox/launcher/Main$StartupClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/DoubleBuffer
instanceKlass java/nio/FloatBuffer
instanceKlass java/nio/ShortBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass java/util/Objects 1 1 184 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 11 100 12 1 1 1 100 1 10 10 12 1 8 1 10 12 1 8 1 100 1 11 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/util/Preconditions 1 1 194 10 7 12 1 1 1 11 7 12 1 1 1 11 100 12 1 1 1 7 1 100 1 10 7 12 1 1 1 10 12 1 8 1 7 1 10 7 12 1 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 7 1 10 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 11 12 1 8 1 8 1 11 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 7 1 10 10 12 1 1 9 12 1 1 7 1 10 9 12 1 7 1 10 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/util/Preconditions SIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions AIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
ciMethod java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 626 0 47984 0 136
instanceKlass org/eclipse/osgi/internal/weaving/DynamicImportList
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$CopyOnFirstWriteList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Vector
instanceKlass sun/security/jca/ProviderList$ServiceList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/Collection 1 1 115 11 7 12 1 1 1 7 1 11 7 12 1 1 1 10 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/List toArray ([Ljava/lang/Object;)[Ljava/lang/Object; 0 0 1 0 -1
instanceKlass com/sun/jna/Structure$StructureSet
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/AbstractMap$2
instanceKlass java/util/TreeMap$Values
instanceKlass org/apache/felix/resolver/util/ArrayMap$1
instanceKlass org/apache/felix/resolver/util/OpenHashMap$AbstractObjectCollection
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/lsp4j/jsonrpc/ResponseErrorException
instanceKlass org/eclipse/lsp4j/jsonrpc/JsonRpcException
instanceKlass org/eclipse/lsp4j/jsonrpc/MessageIssueException
instanceKlass com/google/gson/JsonParseException
instanceKlass org/eclipse/jdt/internal/core/search/matching/MatchLocator$WrappedCoreException
instanceKlass java/io/UncheckedIOException
instanceKlass com/sun/jna/LastErrorException
instanceKlass org/eclipse/text/edits/MalformedTreeException
instanceKlass org/w3c/dom/DOMException
instanceKlass org/eclipse/jdt/internal/codeassist/select/SelectionNodeFound
instanceKlass org/eclipse/jdt/internal/core/DeltaProcessor$1FoundRelevantDeltaException
instanceKlass org/eclipse/jdt/internal/compiler/lookup/ReferenceBinding$DysfunctionalInterfaceException
instanceKlass org/eclipse/jdt/internal/codeassist/complete/InvalidCursorLocation
instanceKlass org/eclipse/jdt/internal/codeassist/complete/CompletionNodeFound
instanceKlass org/eclipse/jdt/internal/compiler/problem/AbortCompilation
instanceKlass org/eclipse/jdt/internal/compiler/lookup/SourceTypeCollisionException
instanceKlass org/eclipse/jdt/internal/core/builder/MissingSourceFileException
instanceKlass org/eclipse/jdt/internal/core/builder/ImageBuilderInternalException
instanceKlass java/lang/NegativeArraySizeException
instanceKlass org/eclipse/jdt/internal/core/ClasspathEntry$AssertionFailedException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/eclipse/core/internal/events/BuildManager$JobManagerSuspendedException
instanceKlass org/eclipse/core/internal/localstore/IsSynchronizedVisitor$ResourceChangedException
instanceKlass org/eclipse/core/internal/dtree/ObjectNotFoundException
instanceKlass org/eclipse/core/internal/utils/WrappedRuntimeException
instanceKlass org/eclipse/core/runtime/OperationCanceledException
instanceKlass org/osgi/util/promise/FailedPromisesException
instanceKlass java/lang/IllegalCallerException
instanceKlass org/eclipse/core/runtime/InvalidRegistryObjectException
instanceKlass org/eclipse/core/runtime/AssertionFailedException
instanceKlass org/osgi/service/component/ComponentException
instanceKlass org/osgi/framework/hooks/weaving/WeavingException
instanceKlass java/util/MissingResourceException
instanceKlass java/lang/reflect/InaccessibleObjectException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/osgi/framework/ServiceException
instanceKlass java/util/concurrent/RejectedExecutionException
instanceKlass java/lang/TypeNotPresentException
instanceKlass org/eclipse/osgi/framework/util/ThreadInfoReport
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/eclipse/osgi/storage/Storage$StorageException
instanceKlass java/util/NoSuchElementException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/invoke/WrongMethodTypeException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable$ParameterData
instanceKlass org/eclipse/jdt/internal/core/JavaModelManager$SecondaryTypesCache
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass org/eclipse/equinox/launcher/Main$Identifier
instanceKlass sun/security/pkcs/SignerInfo$AlgorithmInfo
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/module/ModuleReferenceImpl$CachedHash
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 7 1 10 10 12 1 1 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljdk/internal/util/ReferencedKeySet; jdk/internal/util/ReferencedKeySet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 733 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 6
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$ServiceReferenceMap
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableValueCollectionMap
instanceKlass org/apache/felix/resolver/util/ArrayMap
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass java/util/HashMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 7 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass java/nio/charset/UnsupportedCharsetException
instanceKlass java/nio/charset/IllegalCharsetNameException
instanceKlass java/lang/NumberFormatException
ciInstanceKlass java/lang/IllegalArgumentException 1 1 35 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethod java/lang/IllegalMonitorStateException <init> ()V 0 0 1 0 -1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 1 1 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 7 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
ciMethod java/lang/StackTraceElement equals (Ljava/lang/Object;)Z 512 0 7020 0 896
ciMethod java/lang/StackTraceElement toString ()Ljava/lang/String; 558 0 10664 0 18448
ciMethod java/lang/StackTraceElement <init> ()V 512 0 31759 0 0
ciMethod java/lang/StackTraceElement length (Ljava/lang/String;)I 696 0 42656 0 -1
ciMethod java/lang/StackTraceElement of (Ljava/lang/Object;I)[Ljava/lang/StackTraceElement; 256 5558 1445 0 1272
ciMethod java/lang/StackTraceElement of ([Ljava/lang/StackTraceElement;)[Ljava/lang/StackTraceElement; 256 5558 1445 0 224
ciMethod java/lang/StackTraceElement dropClassLoaderName ()Z 558 0 10664 0 -1
ciMethod java/lang/StackTraceElement dropModuleVersion ()Z 288 0 5504 0 -1
ciMethod java/lang/StackTraceElement isNativeMethod ()Z 558 0 10664 0 -1
ciMethod java/lang/StackTraceElement isHashedInJavaBase (Ljava/lang/Module;)Z 510 0 6855 0 -1
ciMethod java/lang/StackTraceElement initStackTraceElements ([Ljava/lang/StackTraceElement;Ljava/lang/Object;I)V 256 0 128 0 -1
ciMethod java/lang/StackTraceElement computeFormat ()V 512 0 6855 0 2904
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/concurrent/locks/AbstractOwnableSynchronizer setExclusiveOwnerThread (Ljava/lang/Thread;)V 610 0 305 0 0
ciMethod java/util/concurrent/locks/AbstractOwnableSynchronizer getExclusiveOwnerThread ()Ljava/lang/Thread; 510 0 255 0 0
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass java/lang/Void 1 1 31 10 7 12 1 1 1 8 1 10 7 12 1 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Void TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; 
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; 
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; 
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 7 1 100 1 7 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 1 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 1 1 271 9 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 7 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
ciInstanceKlass java/lang/StackWalker$StackFrame 1 1 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
staticfield java/lang/StackFrameInfo JLIA Ljdk/internal/access/JavaLangInvokeAccess; java/lang/invoke/MethodHandleImpl$1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
instanceKlass java/lang/StackStreamFactory$StackFrameTraverser
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 1 375 7 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 7 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
instanceKlass java/util/Hashtable$EntrySet
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$KeySet
instanceKlass java/util/concurrent/ConcurrentSkipListSet
instanceKlass java/util/AbstractMap$1
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot$EntrySet
instanceKlass java/util/TreeMap$EntrySet
instanceKlass java/util/LinkedHashMap$LinkedEntrySet
instanceKlass java/util/TreeMap$KeySet
instanceKlass java/util/TreeSet
instanceKlass java/util/LinkedHashMap$LinkedKeySet
instanceKlass java/util/Collections$SingletonSet
instanceKlass java/util/WeakHashMap$EntrySet
instanceKlass java/util/IdentityHashMap$KeySet
instanceKlass java/util/EnumSet
instanceKlass jdk/internal/util/ReferencedKeySet
instanceKlass java/util/HashMap$KeySet
instanceKlass java/util/WeakHashMap$KeySet
instanceKlass java/util/Collections$SetFromMap
instanceKlass java/util/HashSet
instanceKlass java/util/ImmutableCollections$MapN$1
instanceKlass java/util/Collections$EmptySet
instanceKlass java/util/HashMap$EntrySet
ciInstanceKlass java/util/AbstractSet 1 1 96 10 7 12 1 1 1 7 1 7 1 11 12 1 1 10 7 1 10 12 1 1 100 1 100 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 7 12 1 1 10 7 12 1 1 1 11 10 12 1 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/module/ModuleDescriptor 1 1 516 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 9 12 1 1 9 12 1 1 9 12 1 11 7 12 1 1 1 9 12 1 1 9 7 12 1 1 1 11 12 1 1 9 12 1 9 12 1 9 12 1 11 12 1 1 18 12 1 1 11 100 12 1 1 1 11 12 1 11 12 1 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 10 10 12 10 12 1 1 8 1 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 11 10 100 12 1 1 10 12 1 10 12 1 1 11 10 12 1 10 12 1 8 1 8 1 10 12 1 11 12 1 8 1 8 1 8 1 8 1 8 1 8 1 7 1 10 12 1 100 1 8 1 10 12 1 7 1 10 12 1 11 12 11 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 18 11 12 1 11 12 1 1 8 1 10 100 12 1 1 1 11 12 1 1 11 7 1 7 1 10 7 1 11 12 11 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 16 15 10 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/module/ModuleDescriptor $assertionsDisabled Z 1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ClassValue$ClassValueMap
ciInstanceKlass java/util/WeakHashMap 1 1 399 7 1 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 3 10 7 12 1 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 4 10 12 1 11 7 12 1 1 1 6 0 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 9 12 1 9 12 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 7 1 3 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 100 1 11 12 1 11 12 1 10 12 1 1 10 10 100 12 1 1 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 9 12 7 1 10 10 100 12 1 1 10 12 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 8 1 10 12 1 10 12 10 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/WeakHashMap NULL_KEY Ljava/lang/Object; java/lang/Object
ciMethod java/util/WeakHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 512 4 2769 0 0
ciMethod java/util/WeakHashMap resize (I)V 0 0 1 0 0
ciMethod java/util/WeakHashMap hash (Ljava/lang/Object;)I 512 0 5383 0 240
ciMethod java/util/WeakHashMap transfer ([Ljava/util/WeakHashMap$Entry;[Ljava/util/WeakHashMap$Entry;)V 2 58 1 0 -1
ciMethod java/util/WeakHashMap newTable (I)[Ljava/util/WeakHashMap$Entry; 236 0 118 0 -1
ciMethod java/util/WeakHashMap indexFor (II)I 768 0 9834 0 0
ciMethod java/util/WeakHashMap expungeStaleEntries ()V 768 0 5552 0 800
ciMethod java/util/WeakHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 512 0 9821 0 0
ciMethod java/util/WeakHashMap getTable ()[Ljava/util/WeakHashMap$Entry; 768 0 9822 0 0
ciMethod java/util/WeakHashMap matchesKey (Ljava/util/WeakHashMap$Entry;Ljava/lang/Object;)Z 458 0 3182 0 0
ciInstanceKlass jdk/internal/misc/InternalLock 1 1 69 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 9 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/InternalLock CAN_USE_INTERNAL_LOCK Z 1
ciMethod jdk/internal/misc/InternalLock lock ()V 1024 0 28627 0 -1
ciMethod jdk/internal/misc/InternalLock unlock ()V 1024 0 28627 0 -1
ciInstanceKlass java/util/IdentityHashMap 1 1 403 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 3 3 10 7 12 1 1 7 1 9 12 1 1 11 7 12 1 1 1 6 0 10 12 10 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 3 3 100 1 8 1 10 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 11 12 1 1 10 12 1 10 12 1 9 12 1 10 100 100 1 7 1 10 12 1 9 12 1 7 1 10 12 1 9 12 1 1 7 1 10 100 1 10 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 100 1 8 1 10 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 10 10 100 12 1 1 11 100 12 1 1 100 1 10 11 100 12 1 1 10 12 1 10 7 1 7 1 1 1 3 1 3 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1
staticfield java/util/IdentityHashMap NULL_KEY Ljava/lang/Object; java/lang/Object
ciMethod java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 514 18 1559 0 0
ciMethod java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 512 0 2709 0 0
ciMethod java/util/IdentityHashMap resize (I)Z 0 0 1 0 -1
ciMethod java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 512 0 2709 0 0
ciMethod java/util/IdentityHashMap nextKeyIndex (II)I 36 0 180 0 0
instanceKlass com/sun/org/apache/xml/internal/serializer/WriterToASCI
instanceKlass com/sun/org/apache/xml/internal/serializer/WriterToUTF8Buffered
instanceKlass org/apache/commons/lang3/text/StrBuilder$StrBuilderWriter
instanceKlass java/io/PrintWriter
instanceKlass java/io/StringWriter
instanceKlass java/io/BufferedWriter
instanceKlass sun/nio/cs/StreamEncoder
instanceKlass java/io/OutputStreamWriter
ciInstanceKlass java/io/Writer 1 1 151 100 1 10 12 1 1 10 7 1 9 7 12 1 1 1 10 12 1 1 7 1 7 1 7 1 10 7 12 1 1 1 100 1 10 10 12 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 8 1 11 7 12 1 1 1 10 12 1 1 10 12 10 12 1 10 12 1 7 1 7 1 7 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 7 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/io/Writer flush ()V 0 0 1 0 -1
ciMethod java/io/Writer write (Ljava/lang/String;)V 512 0 564 0 -1
ciMethod java/io/Writer write (Ljava/lang/String;II)V 0 0 1 0 -1
ciInstanceKlass java/util/function/BiFunction 1 1 65 10 100 12 1 1 1 18 12 1 1 11 7 12 1 1 11 100 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/ref/CleanerImpl$PhantomCleanableRef 1 1 51 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 11 7 12 1 1 100 1 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/concurrent/locks/Lock 1 0 19 100 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse$ServiceUseLock
instanceKlass org/eclipse/osgi/internal/container/EquinoxReentrantLock
instanceKlass jdk/internal/loader/NativeLibraries$CountedLock
instanceKlass java/util/concurrent/ConcurrentHashMap$Segment
ciInstanceKlass java/util/concurrent/locks/ReentrantLock 1 1 177 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 8 1 10 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 100 1 1
ciMethod java/util/concurrent/locks/ReentrantLock lock ()V 14 0 39225 0 376
ciMethod java/util/concurrent/locks/ReentrantLock unlock ()V 12 0 40373 0 256
ciInstanceKlass java/util/concurrent/locks/LockSupport 1 1 105 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/locks/LockSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/locks/LockSupport PARKBLOCKER J 92
ciMethod java/util/concurrent/locks/LockSupport unpark (Ljava/lang/Thread;)V 2 0 125 0 0
ciInstanceKlass java/lang/ref/ReferenceQueue$Null 1 1 29 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/lang/ref/ReferenceQueue poll ()Ljava/lang/ref/Reference; 514 0 5466 0 704
ciInstanceKlass java/util/concurrent/locks/ReentrantLock$NonfairSync 1 1 69 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass java/util/concurrent/CountDownLatch$Sync
instanceKlass java/util/concurrent/ThreadPoolExecutor$Worker
instanceKlass java/util/concurrent/Semaphore$Sync
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$Sync
instanceKlass java/util/concurrent/locks/ReentrantLock$Sync
ciInstanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer 1 1 367 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 7 1 10 7 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 7 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 5 0 10 12 1 10 12 1 10 12 1 3 100 1 10 10 12 1 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 10 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 10 12 1 1 8 8 7 1 1 1 5 0 1 3 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer STATE J 16
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer HEAD J 20
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer TAIL J 24
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer tryRelease (I)Z 0 0 1 0 -1
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer getState ()I 400 0 200 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer compareAndSetState (II)Z 538 0 1965 0 104
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer setState (I)V 410 0 205 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer release (I)Z 14 0 40801 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer acquire (I)V 0 0 397 0 -1
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer signalNext (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;)V 14 0 10548 0 152
instanceKlass java/util/concurrent/locks/ReentrantLock$FairSync
instanceKlass java/util/concurrent/locks/ReentrantLock$NonfairSync
ciInstanceKlass java/util/concurrent/locks/ReentrantLock$Sync 1 1 127 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 1 7 1 10 7 1 10 12 1 10 12 1 10 100 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 100 1 1 1 1
ciMethod java/util/concurrent/locks/ReentrantLock$Sync tryRelease (I)Z 14 0 10122 0 152
ciMethod java/util/concurrent/locks/ReentrantLock$Sync initialTryLock ()Z 0 0 1 0 -1
ciMethod java/util/concurrent/locks/ReentrantLock$Sync lock ()V 14 0 39228 0 0
ciMethod java/util/concurrent/locks/ReentrantLock$NonfairSync initialTryLock ()Z 1024 0 16011 0 320
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$SharedNode
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionNode
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ExclusiveNode
ciInstanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node 1 1 84 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer$Node STATUS J 12
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer$Node NEXT J 20
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer$Node PREV J 16
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer$Node getAndUnsetStatus (I)I 4 0 28 0 0
ciInstanceKlass java/util/Collections$SetFromMap 1 1 182 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 12 1 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 9 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 100 1 8 1 10 12 1 7 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 100 1 1 1
ciMethod java/util/Collections$SetFromMap add (Ljava/lang/Object;)Z 512 0 4240 0 0
ciMethod java/util/Collections$SetFromMap contains (Ljava/lang/Object;)Z 514 0 1199 0 0
ciInstanceKlass java/util/Collections$EmptyList 1 1 150 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 7 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 11 10 7 12 1 1 1 10 100 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/WeakHashMap$Entry 1 1 112 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 11 10 7 12 1 1 1 10 12 1 11 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
ciMethod java/util/WeakHashMap$Entry <init> (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;ILjava/util/WeakHashMap$Entry;)V 508 0 2758 0 0
ciInstanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry 1 1 37 10 7 12 1 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciInstanceKlass java/util/zip/ZipFile$ZipFileInputStream 1 1 180 100 1 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 9 12 1 5 0 10 12 1 1 9 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 5 0 8 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 7 1 5 0 3 9 12 1 1 9 12 1 1 11 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/zip/ZipFile$ZipFileInflaterInputStream 1 1 155 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 9 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 11 7 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 5 0 3 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/util/WeakReferenceKey 1 1 92 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 7 1 11 12 1 1 10 10 12 1 1 7 1 10 12 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/Set add (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod jdk/internal/util/Preconditions checkFromToIndex (IIILjava/util/function/BiFunction;)I 532 0 54434 0 -1
ciMethod java/lang/ref/ReferenceQueue headIsNull ()Z 540 0 10982 0 0
ciMethod java/lang/ref/ReferenceQueue poll0 ()Ljava/lang/ref/Reference; 4 0 2309 0 0
ciMethod java/lang/OutOfMemoryError <init> (Ljava/lang/String;)V 0 0 1 0 -1
ciMethod java/util/Arrays checkLength (II)V 582 0 25468 0 -1
ciMethod java/util/Arrays copyOfRangeByte ([BII)[B 582 0 25304 0 -1
ciMethod java/util/Arrays copyOfRange ([BII)[B 768 0 27586 0 1000
ciMethod java/util/Arrays copyOf ([BI)[B 206 0 4375 0 0
ciMethod jdk/internal/util/ArraysSupport newLength (III)I 454 0 7484 0 -1
ciMethod java/lang/Math max (II)I 514 0 71785 0 -1
ciMethod java/lang/Math min (II)I 514 0 88471 0 -1
ciMethod java/lang/Thread interrupt0 ()V 0 0 1 0 -1
ciMethod java/lang/Thread checkAccess ()V 108 0 54 0 -1
ciMethod java/lang/Thread isVirtual ()Z 78 0 1032 0 -1
ciMethod java/lang/Thread currentThread ()Ljava/lang/Thread; 0 0 1 0 -1
ciMethod jdk/internal/misc/Unsafe getAndBitwiseAndInt (Ljava/lang/Object;JI)I 56 0 28 0 -1
ciMethod jdk/internal/misc/Unsafe unpark (Ljava/lang/Object;)V 218 0 109 0 -1
ciMethod jdk/internal/misc/Unsafe compareAndSetInt (Ljava/lang/Object;JII)Z 256 0 128 0 -1
ciMethod java/util/Set contains (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/lang/Error <init> (Ljava/lang/String;)V 42 0 75 0 0
ciMethod jdk/internal/misc/VM addFinalRefCount (I)V 0 0 1 0 -1
ciMethod java/lang/String getBytes ([BIB)V 514 0 7470 0 488
ciMethod java/lang/String coder ()B 512 0 634729 0 88
ciMethod java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 102 0 31481 0 0
ciMethod java/lang/String <init> (Ljava/lang/AbstractStringBuilder;Ljava/lang/Void;)V 14 0 6291 0 1256
ciMethod java/lang/String <init> (Ljava/lang/StringBuilder;)V 14 0 64475 0 0
ciMethod java/lang/Throwable <init> (Ljava/lang/String;)V 512 0 611 0 0
ciMethod java/lang/Throwable fillInStackTrace (I)Ljava/lang/Throwable; 256 0 128 0 -1
ciMethod java/lang/Throwable getSuppressed ()[Ljava/lang/Throwable; 320 0 1528 0 0
ciMethod java/lang/Throwable getOurStackTrace ()[Ljava/lang/StackTraceElement; 330 0 1534 0 0
ciMethod java/lang/Throwable printEnclosedStackTrace (Ljava/lang/Throwable$PrintStreamOrWriter;[Ljava/lang/StackTraceElement;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)V 240 6160 1146 0 -1
ciMethod java/lang/CharSequence length ()I 0 0 1 0 -1
ciMethod java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 10 0 9709 0 0
ciMethod java/lang/StringBuilder append (C)Ljava/lang/StringBuilder; 772 0 296030 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 14 0 266123 0 1696
ciMethod java/lang/StringBuilder append (Ljava/lang/Object;)Ljava/lang/StringBuilder; 4 0 16313 0 1752
ciMethod java/lang/StringBuilder <init> ()V 12 0 33259 0 232
ciMethod java/lang/StringBuilder <init> (I)V 410 0 29410 0 -1
ciMethod java/lang/AbstractStringBuilder length ()I 266 0 133 0 0
ciMethod java/lang/AbstractStringBuilder <init> (I)V 14 0 6157 0 384
ciMethod java/lang/AbstractStringBuilder append (Ljava/lang/CharSequence;II)Ljava/lang/AbstractStringBuilder; 98 0 5627 0 3240
ciMethod java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 14 0 12551 0 1672
ciMethod java/lang/AbstractStringBuilder append (I)Ljava/lang/AbstractStringBuilder; 10 0 6355 0 -1
ciMethod java/lang/AbstractStringBuilder inflate ()V 0 0 5172 0 760
ciMethod java/lang/AbstractStringBuilder isLatin1 ()Z 778 0 59993 0 88
ciMethod java/lang/AbstractStringBuilder getValue ()[B 262 0 131 0 0
ciMethod java/lang/AbstractStringBuilder newCapacity (I)I 196 0 5221 0 200
ciMethod java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 776 0 6560 0 1024
ciMethod java/lang/AbstractStringBuilder appendNull ()Ljava/lang/AbstractStringBuilder; 0 0 17 0 0
ciMethod java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 526 0 13202 0 0
ciMethod java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 534 0 13203 0 0
ciMethod java/lang/AbstractStringBuilder appendChars (Ljava/lang/String;II)V 522 0 5627 0 -1
ciMethod java/lang/AbstractStringBuilder appendChars (Ljava/lang/CharSequence;II)V 0 0 1 0 -1
ciMethod java/lang/Class initClassName ()Ljava/lang/String; 258 0 129 0 -1
ciMethod java/lang/Class getClassLoader0 ()Ljava/lang/ClassLoader; 268 0 134 0 -1
ciMethod java/lang/Class getModule ()Ljava/lang/Module; 256 0 128 0 -1
ciMethod java/lang/Class getName ()Ljava/lang/String; 444 0 34781 0 112
ciMethod java/util/Map containsKey (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/Map put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 256 0 128 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 565238 0 136
ciInstanceKlass java/io/StringWriter 1 1 119 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 11 100 12 1 1 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync 1 1 33 10 7 12 1 1 1 10 7 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass java/nio/channels/ClosedChannelException
instanceKlass com/google/gson/stream/MalformedJsonException
instanceKlass org/eclipse/core/internal/content/LowLevelIOException
instanceKlass java/net/ProtocolException
instanceKlass java/net/UnknownHostException
instanceKlass java/net/SocketException
instanceKlass java/io/InterruptedIOException
instanceKlass java/io/CharConversionException
instanceKlass java/io/EOFException
instanceKlass java/net/UnknownServiceException
instanceKlass java/io/SyncFailedException
instanceKlass java/util/zip/ZipException
instanceKlass java/nio/file/FileSystemException
instanceKlass java/io/UnsupportedEncodingException
instanceKlass java/net/MalformedURLException
instanceKlass java/io/FileNotFoundException
ciInstanceKlass java/io/IOException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionError 0 0 79 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/security/Provider$UString 1 1 56 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/concurrent/ExecutionException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/osgi/internal/loader/SystemBundleLoader$SystemModuleClassLoader
ciInstanceKlass org/eclipse/osgi/internal/loader/EquinoxClassLoader 1 1 75 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 7 1 1 1 1 1 1 10 12 1 9 12 10 7 1 12 1 1 9 12 9 12 9 12 7 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/io/PrintWriter 1 1 377 8 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 10 12 1 8 100 1 8 1 10 9 12 1 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 10 12 1 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 10 7 12 1 1 1 10 10 12 1 10 7 12 1 1 1 10 12 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 11 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 10 7 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/SocketTimeoutException
ciInstanceKlass java/io/InterruptedIOException 0 0 30 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/Throwable$WrappedPrintWriter
ciInstanceKlass java/lang/Throwable$PrintStreamOrWriter 1 1 44 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass java/lang/Throwable$WrappedPrintWriter 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1
ciInstanceKlass com/google/gson/JsonSyntaxException 1 1 31 10 10 10 100 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 1 1
ciMethodData java/lang/Object <init> ()V 2 564726 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder <init> ()V 2 33819 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x30002 0x841b 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder <init> (I)V 2 6150 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0x10002 0x1806 0x70007 0x0 0x38 0x1806 0x160003 0x1806 0x28 0x1b0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 266261 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x41016 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 2 12544 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x10007 0x30f1 0x58 0xf 0x50005 0xf 0x0 0x0 0x0 0x0 0x0 0xa0005 0x30f1 0x0 0x0 0x0 0x0 0x0 0x150005 0x30f1 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x30f1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder toString ()Ljava/lang/String; 2 64816 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x50002 0xfd30 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/String <init> (Ljava/lang/StringBuilder;)V 2 64816 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x30002 0xfd30 0x0 0x0 0x9 0x2 0xc 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/String <init> (Ljava/lang/AbstractStringBuilder;Ljava/lang/Void;)V 2 6284 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 59 0x10002 0x188c 0x50005 0x188c 0x0 0x0 0x0 0x0 0x0 0xa0005 0x164a 0x0 0x186767cb1a8 0x240 0x186767cb068 0x2 0x100005 0x188c 0x0 0x0 0x0 0x0 0x0 0x130007 0xc 0x48 0x1880 0x200002 0x1880 0x260003 0x1880 0x98 0x2c0007 0x0 0x70 0xc 0x330007 0xc 0x50 0x0 0x3a0002 0x0 0x410007 0x0 0x20 0x0 0x5c0002 0xc 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xc 0xffffffffffffffff 0x0 oops 2 12 java/lang/StringBuilder 14 java/lang/StringBuffer methods 0
ciMethodData java/lang/String equals (Ljava/lang/Object;)Z 2 13314 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x20007 0x1bef 0x20 0x1813 0x80104 0x0 0x0 0x186767c8118 0x1b84 0x0 0x0 0xb0007 0x6b 0xe0 0x1b84 0xf0004 0x0 0x0 0x186767c8118 0x1b84 0x0 0x0 0x160007 0x0 0x40 0x1b84 0x8000000600210007 0x1 0x68 0x1b84 0x2c0002 0x1b84 0x2f0007 0x1a93 0x38 0xf1 0x330003 0xf1 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/String 18 java/lang/String methods 0
ciMethodData java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 2 47671 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 32 0x20007 0xa636 0x98 0x1401 0x60007 0x1 0x90 0x1400 0xb0005 0x11f8 0x0 0x186767c8118 0x181 0x1867ee997a8 0x87 0xe0007 0x11e 0x38 0x12e2 0x120003 0xb918 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 11 java/lang/String 13 java/lang/module/ModuleDescriptor methods 0
ciMethodData java/lang/String coder ()B 2 634473 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x30007 0x0 0x38 0x9ae69 0xa0003 0x9ae69 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/String length ()I 2 531106 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x60005 0x81aa4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/ref/ReferenceQueue headIsNull ()Z 2 10712 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40007 0x184 0x38 0x2854 0x80003 0x2854 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/ref/ReferenceQueue poll0 ()Ljava/lang/ref/Reference; 2 2307 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x60007 0xd 0xc0 0x8f6 0x180007 0x8a6 0x38 0x50 0x1c0003 0x50 0x18 0x330004 0xfffffffffffff70a 0x0 0x18677a0a8a0 0xb3 0x18677a0a950 0xef 0x360007 0x8f6 0x30 0x0 0x3a0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 2 14 jdk/internal/ref/CleanerImpl$PhantomCleanableRef 16 jdk/internal/util/WeakReferenceKey methods 0
ciMethodData java/lang/String getBytes ([BIB)V 2 7213 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x10005 0x1c2d 0x0 0x0 0x0 0x0 0x0 0x50007 0x5 0x48 0x1c28 0x160002 0x1c28 0x190003 0x1c28 0x28 0x280002 0x5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData java/util/Arrays copyOfRange ([BII)[B 2 27202 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 35 0x10007 0x3fce 0x40 0x2a74 0x70007 0x88c 0x30 0x21e8 0xd0002 0x61b6 0x120005 0x0 0x0 0x186767dea48 0x88c 0x0 0x0 0x150004 0x0 0x0 0x186767dea48 0x88c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 2 13 [B 20 [B methods 0
ciMethodData java/util/Arrays copyOfRangeByte ([BII)[B 2 25013 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20002 0x61b5 0x180002 0x61b5 0x1b0002 0x61b5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xffffffffffffffff 0x0 0x0 oops 0 methods 0
ciMethodData java/util/Arrays checkLength (II)V 2 25177 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 46 0x20007 0x6259 0x120 0x0 0xd0002 0x0 0x110005 0x0 0x0 0x0 0x0 0x0 0x0 0x170005 0x0 0x0 0x0 0x0 0x0 0x0 0x1b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x210002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 2 6172 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0xe0007 0x1636 0x68 0x1e6 0x180005 0x1e6 0x0 0x0 0x0 0x0 0x0 0x200002 0x1e6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder newCapacity (I)I 2 5123 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x1d0002 0x1403 0x260007 0x1403 0x30 0x0 0x2f0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/Arrays copyOf ([BI)[B 2 4272 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 32 0x30007 0x105b 0x90 0x55 0x70005 0x0 0x0 0x186767dea48 0x55 0x0 0x0 0xa0004 0x0 0x0 0x186767dea48 0x55 0x0 0x0 0x190002 0x105b 0x1c0002 0x105b 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 [B 14 [B methods 0
ciMethodData java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 2 12940 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x20005 0x328c 0x0 0x0 0x0 0x0 0x0 0xf0005 0x328e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0xc 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 2 12936 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 32 0x30007 0x0 0xb0 0x3288 0xb0005 0x3288 0x0 0x0 0x0 0x0 0x0 0x80000006000e0007 0x3278 0x58 0x12 0x120005 0x12 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xc 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder inflate ()V 2 5172 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x10005 0x1434 0x0 0x0 0x0 0x0 0x0 0x40007 0x821 0x20 0xc13 0xd0002 0x821 0x1c0002 0x821 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder isLatin1 ()Z 2 59604 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0xe8d4 0xa0007 0x4f80 0x38 0x9952 0xe0003 0x9953 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder appendNull ()Ljava/lang/AbstractStringBuilder; 1 17 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 32 0x70005 0x11 0x0 0x0 0x0 0x0 0x0 0x150005 0x11 0x0 0x0 0x0 0x0 0x0 0x180007 0x0 0x38 0x11 0x3b0003 0x11 0x28 0x480002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xe oops 0 methods 0
ciMethodData java/lang/ref/Reference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 2 17380 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x10002 0x43e4 0xb0007 0x32d1 0x38 0x1113 0x110003 0x1113 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder length ()I 2 29736 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x10002 0x7428 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/io/IOException <init> (Ljava/lang/String;)V 24 0 436 0 -1
ciMethodData java/lang/Class getName ()Ljava/lang/String; 2 34559 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x60007 0x957 0x38 0x7da8 0xa0003 0x7da8 0x50 0xe0005 0x957 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/StringUTF16 compress ([BII)[B 2 7499 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x90002 0x1d4b 0xd0007 0x847 0x20 0x1504 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/String toString ()Ljava/lang/String; 2 9397 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder append (Ljava/lang/Object;)Ljava/lang/StringBuilder; 2 16356 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x20002 0x3fe4 0x50005 0x3fe4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xe 0xffffffffffffffff oops 0 methods 0
ciMethodData java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 2 31430 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x10007 0x7ac0 0x38 0x5 0x70003 0x5 0x50 0xb0005 0x302d 0x0 0x186767c8118 0x35f1 0x1867ee9c8c8 0x92 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 2 10 java/lang/String 12 java/security/Provider$UString methods 0
ciMethodData java/lang/ref/WeakReference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 2 6250 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x30002 0x186a 0x0 0x0 0x9 0x3 0x6 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/ref/ReferenceQueue poll ()Ljava/lang/ref/Reference; 2 5209 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 48 0x10005 0x1459 0x0 0x0 0x0 0x0 0x0 0x40007 0x95 0x20 0x13c4 0xd0005 0x0 0x0 0x186768ff070 0x95 0x0 0x0 0x110005 0x95 0x0 0x0 0x0 0x0 0x0 0x190005 0x0 0x0 0x186768ff070 0x95 0x0 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 14 java/util/concurrent/locks/ReentrantLock 28 java/util/concurrent/locks/ReentrantLock methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock lock ()V 2 39221 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x9935 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock$Sync lock ()V 2 39221 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x10005 0x0 0x0 0x1867cdf3d28 0x9933 0x0 0x0 0x40007 0x9935 0x58 0x0 0x90005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/concurrent/locks/ReentrantLock$NonfairSync methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock unlock ()V 2 40373 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x50005 0x9db5 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer release (I)Z 2 40794 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x20005 0x77 0x0 0x1867cdf3d28 0x9dc5 0x1867ea32750 0x11e 0x50007 0x6c2 0x30 0x9897 0xc0002 0x9897 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/concurrent/locks/ReentrantLock$NonfairSync 5 java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock$Sync tryRelease (I)Z 2 10115 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 57 0x10005 0x2783 0x0 0x0 0x0 0x0 0x0 0x80005 0x2783 0x0 0x0 0x0 0x0 0x0 0xb0002 0x2783 0xe0007 0x2783 0x30 0x0 0x150002 0x0 0x1a0007 0x16 0x38 0x276d 0x1e0003 0x276d 0x18 0x240007 0x16 0x58 0x276d 0x290005 0x276d 0x0 0x0 0x0 0x0 0x0 0x2e0005 0x2783 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer signalNext (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;)V 2 10541 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x10007 0x287a 0xa8 0xb3 0xa0007 0x60 0x88 0x53 0x110007 0x4f 0x68 0x4 0x160005 0x4 0x0 0x0 0x0 0x0 0x0 0x1e0002 0x4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 2 9565 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10007 0x255d 0x38 0x0 0x70003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap hash (Ljava/lang/Object;)I 2 5127 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x10005 0x2e0 0x0 0x1867ca5c560 0xb6e 0x1867ca5c610 0x5b9 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/zip/ZipFile$ZipFileInflaterInputStream 5 java/util/zip/ZipFile$ZipFileInputStream methods 0
ciMethodData java/lang/AbstractStringBuilder append (Ljava/lang/CharSequence;II)Ljava/lang/AbstractStringBuilder; 2 5578 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x10007 0x15ca 0x20 0x0 0xa0005 0x0 0x0 0x186767c8118 0x15ca 0x0 0x0 0x120002 0x15ca 0x230005 0x15ca 0x0 0x0 0x0 0x0 0x0 0x270004 0x0 0x0 0x186767c8118 0x15ca 0x0 0x0 0x2a0007 0x0 0xa8 0x15ca 0x2f0004 0x0 0x0 0x186767c8118 0x15ca 0x0 0x0 0x340005 0x15ca 0x0 0x0 0x0 0x0 0x0 0x370003 0x15ca 0x50 0x3e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0xffffffffffffffff 0xffffffffffffffff 0x0 0x0 oops 3 7 java/lang/String 23 java/lang/String 34 java/lang/String methods 0
ciMethodData java/lang/ref/Reference refersTo (Ljava/lang/Object;)Z 2 15106 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x704 0x0 0x18677a0c4b8 0x2827 0x1867df0a4b0 0xbd7 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0xffffffffffffffff oops 2 3 java/lang/ThreadLocal$ThreadLocalMap$Entry 5 java/util/WeakHashMap$Entry methods 0
ciMethodData java/util/WeakHashMap getTable ()[Ljava/util/WeakHashMap$Entry; 2 9438 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x24de 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 0 methods 0
ciMethodData java/util/WeakHashMap expungeStaleEntries ()V 2 5168 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 64 0x40005 0x0 0x0 0x1867df0ae58 0x1430 0x0 0x0 0x90007 0x1430 0x178 0x0 0x140004 0x0 0x0 0x0 0x0 0x0 0x0 0x210002 0x0 0x350007 0x0 0xe0 0x0 0x420007 0x0 0xa8 0x0 0x480007 0x0 0x70 0x0 0x530004 0x0 0x0 0x0 0x0 0x0 0x0 0x540003 0x0 0x18 0x6d0003 0x0 0x30 0x780003 0x0 0xffffffffffffff38 0x7d0003 0x0 0x18 0x870003 0x0 0xfffffffffffffe68 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 1 3 java/lang/ref/ReferenceQueue methods 0
ciMethodData java/util/WeakHashMap indexFor (II)I 2 9450 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer compareAndSetState (II)Z 2 1696 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x90005 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap matchesKey (Ljava/util/WeakHashMap$Entry;Ljava/lang/Object;)Z 2 2953 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 47 0x20005 0xb89 0x0 0x0 0x0 0x0 0x0 0x50007 0x0 0x20 0xb89 0xb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x100007 0x0 0x90 0x0 0x150005 0x0 0x0 0x0 0x0 0x0 0x0 0x180007 0x0 0x38 0x0 0x1c0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0xffffffffffffffff 0xffffffffffffffff oops 0 methods 0
ciMethod java/io/StringWriter write (Ljava/lang/String;II)V 794 0 5544 0 0
ciMethodData java/lang/Throwable getCause ()Ljava/lang/Throwable; 2 1552 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x50007 0x48c 0x38 0x184 0x90003 0x184 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 2513 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 76 0x10002 0x9d1 0x70005 0x9d1 0x0 0x0 0x0 0x0 0x0 0xd0005 0x9d1 0x0 0x0 0x0 0x0 0x0 0x170002 0x9d1 0x250007 0x9c8 0xd0 0x17 0x2f0007 0xe 0x98 0x9 0x360005 0x9 0x0 0x0 0x0 0x0 0x0 0x390007 0x0 0x40 0x9 0x460007 0x2 0x20 0x7 0x590003 0xe 0xffffffffffffff48 0x7f0002 0x9c8 0x820004 0x0 0x0 0x1867df0a4b0 0x9c8 0x0 0x0 0x920007 0x9c7 0x58 0x1 0x9b0005 0x0 0x0 0x1867f173850 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 2 49 java/util/WeakHashMap$Entry 60 java/util/WeakHashMap methods 0
ciMethodData java/util/WeakHashMap$Entry <init> (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;ILjava/util/WeakHashMap$Entry;)V 2 2504 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x30002 0x9c8 0x0 0x0 0x0 0x0 0x9 0x6 0x3e 0x0 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap resize (I)V 1 1 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 57 0x10005 0x1 0x0 0x0 0x0 0x0 0x0 0xb0007 0x1 0x20 0x0 0x170005 0x1 0x0 0x0 0x0 0x0 0x0 0x200005 0x1 0x0 0x0 0x0 0x0 0x0 0x330007 0x0 0x38 0x1 0x420003 0x1 0x88 0x460005 0x0 0x0 0x0 0x0 0x0 0x0 0x4d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x3c 0x0 oops 0 methods 0
ciMethodData java/util/Collections$SetFromMap add (Ljava/lang/Object;)Z 2 3984 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x80005 0x0 0x0 0x1867f173850 0x98d 0x1867f1722c0 0x603 0xd0007 0x3 0x38 0xf8d 0x110003 0xf8d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/WeakHashMap 5 java/util/IdentityHashMap methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer$Node getAndUnsetStatus (I)I 1 26 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0xa000b 0x1a 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/LockSupport unpark (Ljava/lang/Thread;)V 1 124 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x10007 0x10 0xd8 0x6c 0x50005 0x6c 0x0 0x0 0x0 0x0 0x0 0x80007 0x6c 0x48 0x0 0xc0002 0x0 0xf0003 0x0 0x50 0x160005 0x6c 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock$NonfairSync initialTryLock ()Z 2 15499 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x2 0x3c8c 0x70005 0x3c8c 0x0 0x0 0x0 0x0 0x0 0x80000006000a0007 0x3 0x58 0x3c8a 0xf0005 0x3c8a 0x0 0x0 0x0 0x0 0x0 0x150005 0x3 0x0 0x0 0x0 0x0 0x0 0x190007 0x0 0xc0 0x3 0x1d0005 0x3 0x0 0x0 0x0 0x0 0x0 0x240007 0x3 0x30 0x0 0x2d0002 0x0 0x330005 0x3 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Error <init> (Ljava/lang/String;)V 1 54 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x20002 0x36 0x0 0x0 0x9 0x2 0x1c 0x0 oops 0 methods 0
ciMethodData java/lang/Throwable fillInStackTrace ()Ljava/lang/Throwable; 2 2077 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x40007 0x81d 0x40 0x0 0xb0007 0x0 0x58 0x0 0x100005 0x81d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Thread interrupt ()V 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 49 0x10002 0x0 0x40007 0x0 0x58 0x0 0x80005 0x0 0x0 0x0 0x0 0x0 0x0 0x110005 0x0 0x0 0x0 0x0 0x0 0x0 0x150002 0x0 0x180007 0x0 0x90 0x0 0x280007 0x0 0x58 0x0 0x2d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x340003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethod java/io/PrintWriter ensureOpen ()V 784 0 11393 0 0
ciMethod java/io/PrintWriter write (Ljava/lang/String;II)V 768 0 5544 0 1520
ciMethod java/io/PrintWriter implWrite (Ljava/lang/String;II)V 768 0 5544 0 0
ciMethod java/io/PrintWriter write (Ljava/lang/String;)V 766 0 7608 0 0
ciMethod java/io/PrintWriter newLine ()V 510 0 5849 0 3520
ciMethod java/io/PrintWriter implNewLine ()V 512 0 5849 0 -1
ciMethod java/io/PrintWriter print (Ljava/lang/String;)V 768 0 7608 0 0
ciMethod java/io/PrintWriter println ()V 512 0 7089 0 0
ciMethod java/io/PrintWriter println (Ljava/lang/Object;)V 510 0 6510 0 2432
ciMethodData java/io/PrintWriter ensureOpen ()V 2 11001 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x40007 0x2af9 0x30 0x0 0xd0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuffer append (Ljava/lang/CharSequence;II)Ljava/lang/StringBuffer; 2 5176 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x90002 0x1438 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/io/PrintWriter print (Ljava/lang/String;)V 2 7224 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x20002 0x1c38 0x50005 0x0 0x0 0x1867f7314a0 0x1c38 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 5 java/io/PrintWriter methods 0
ciMethodData java/io/PrintWriter write (Ljava/lang/String;)V 2 7225 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x40005 0x1c39 0x0 0x0 0x0 0x0 0x0 0x70005 0x0 0x0 0x1867f7314a0 0x1c39 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 10 java/io/PrintWriter methods 0
ciMethodData java/io/PrintWriter write (Ljava/lang/String;II)V 2 5160 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 74 0x80004 0xffffffffffffebd8 0x0 0x1867c76fd18 0xe 0x0 0x0 0xb0007 0x1428 0x168 0x0 0x100004 0x0 0x0 0x0 0x0 0x0 0x0 0x170005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x260003 0x0 0x50 0x2d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x330003 0x0 0x68 0x400005 0x1428 0x0 0x0 0x0 0x0 0x0 0x460003 0x1428 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 3 java/io/StringWriter methods 0
ciMethodData java/io/PrintWriter implWrite (Ljava/lang/String;II)V 2 5160 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 41 0x10005 0x1428 0x0 0x0 0x0 0x0 0x0 0xb0005 0x0 0x0 0x1867c76fd18 0x1428 0x0 0x0 0xe0003 0x1428 0x78 0x130002 0x0 0x160005 0x0 0x0 0x0 0x0 0x0 0x0 0x190003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 10 java/io/StringWriter methods 0
ciMethodData java/io/StringWriter write (Ljava/lang/String;II)V 2 5147 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x90005 0x141b 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethod java/lang/Throwable$WrappedPrintWriter println (Ljava/lang/Object;)V 512 0 6726 0 2440
ciMethod java/lang/Throwable$PrintStreamOrWriter isLockedByCurrentThread ()Z 0 0 1 0 -1
ciMethod java/lang/Throwable$PrintStreamOrWriter println (Ljava/lang/Object;)V 0 0 1 0 -1
ciMethodData java/io/PrintWriter println ()V 2 6835 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x1ab3 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/io/PrintWriter newLine ()V 2 5594 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 71 0x60004 0xffffffffffffea26 0x0 0x1867c76fd18 0x87 0x0 0x0 0x90007 0x15da 0x168 0x0 0xd0004 0x0 0x0 0x0 0x0 0x0 0x0 0x120005 0x0 0x0 0x0 0x0 0x0 0x0 0x160005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1d0003 0x0 0x50 0x220005 0x0 0x0 0x0 0x0 0x0 0x0 0x270003 0x0 0x68 0x2f0005 0x15da 0x0 0x0 0x0 0x0 0x0 0x340003 0x15da 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 1 3 java/io/StringWriter methods 0
ciMethodData java/io/PrintWriter implNewLine ()V 2 5593 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 51 0x10005 0x15d9 0x0 0x0 0x0 0x0 0x0 0x80002 0x15d9 0xb0005 0x0 0x0 0x1867c76fd18 0x15d9 0x0 0x0 0x120007 0x15d9 0x58 0x0 0x190005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c0003 0x15d9 0x78 0x200002 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x260003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 1 12 java/io/StringWriter methods 0
ciMethodData java/lang/StackTraceElement <init> ()V 2 31503 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x7b0f 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StackTraceElement computeFormat ()V 2 6599 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x60005 0x19c7 0x0 0x0 0x0 0x0 0x0 0xb0005 0x19c7 0x0 0x0 0x0 0x0 0x0 0x130104 0xfffffffffffff2d2 0x0 0x18676aba5e0 0x12 0x0 0x0 0x160007 0x19c7 0x20 0x0 0x210002 0x19c7 0x240007 0xd2e 0x20 0xc99 0x390003 0x19c7 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xc oops 1 17 org/eclipse/osgi/internal/loader/EquinoxClassLoader methods 0
ciMethodData java/lang/StackTraceElement equals (Ljava/lang/Object;)Z 2 6764 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 81 0x20007 0x1a6c 0x20 0x0 0x80004 0x0 0x0 0x186767cbd48 0x1a6c 0x0 0x0 0xb0007 0x0 0x1d8 0x1a6c 0xf0004 0x0 0x0 0x186767cbd48 0x1a6c 0x0 0x0 0x1b0007 0xe1 0x180 0x198b 0x260005 0x198b 0x0 0x0 0x0 0x0 0x0 0x290007 0x71 0x128 0x191a 0x340002 0x191a 0x370007 0x0 0xf8 0x191a 0x420002 0x191a 0x450007 0x0 0xc8 0x191a 0x500002 0x191a 0x530007 0x0 0x98 0x191a 0x5e0002 0x191a 0x610007 0x0 0x68 0x191a 0x6c0002 0x191a 0x6f0007 0x0 0x38 0x191a 0x730003 0x191a 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/lang/StackTraceElement 18 java/lang/StackTraceElement methods 0
ciMethodData java/lang/Throwable$WrappedPrintWriter println (Ljava/lang/Object;)V 2 6470 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x50005 0x0 0x0 0x1867f7314a0 0x1946 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/io/PrintWriter methods 0
ciMethodData java/io/PrintWriter println (Ljava/lang/Object;)V 2 6255 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 88 0x10002 0x186f 0xb0004 0xffffffffffffe791 0x0 0x1867c76fd18 0x81 0x0 0x0 0xe0007 0x186f 0x1a0 0x0 0x120004 0x0 0x0 0x0 0x0 0x0 0x0 0x190005 0x0 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x220005 0x0 0x0 0x0 0x0 0x0 0x0 0x270005 0x0 0x0 0x0 0x0 0x0 0x0 0x2a0003 0x0 0x50 0x310005 0x0 0x0 0x0 0x0 0x0 0x0 0x370003 0x0 0xa0 0x410005 0x0 0x0 0x1867f7314a0 0x186f 0x0 0x0 0x450005 0x0 0x0 0x1867f7314a0 0x186f 0x0 0x0 0x4b0003 0x186f 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 3 5 java/io/StringWriter 64 java/io/PrintWriter 71 java/io/PrintWriter methods 0
ciMethodData java/lang/StackTraceElement toString ()Ljava/lang/String; 2 10385 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 273 0x40002 0x2891 0xd0002 0x2891 0x170002 0x2891 0x210005 0x2891 0x0 0x0 0x0 0x0 0x0 0x2b0005 0x2891 0x0 0x0 0x0 0x0 0x0 0x330005 0x2891 0x0 0x0 0x0 0x0 0x0 0x3a0002 0x2891 0x3d0002 0x2891 0x4c0002 0x2891 0x510005 0x2891 0x0 0x0 0x0 0x0 0x0 0x540007 0x0 0x108 0x2891 0x5b0007 0x2891 0xe8 0x0 0x620005 0x0 0x0 0x0 0x0 0x0 0x0 0x650007 0x0 0x90 0x0 0x6d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x720005 0x0 0x0 0x0 0x0 0x0 0x0 0x7a0007 0x13a1 0x1f0 0x14f0 0x810005 0x14f0 0x0 0x0 0x0 0x0 0x0 0x840007 0x0 0x198 0x14f0 0x8c0005 0x14f0 0x0 0x0 0x0 0x0 0x0 0x910005 0x14f0 0x0 0x0 0x0 0x0 0x0 0x940007 0x14f0 0x108 0x0 0x9b0007 0x0 0xe8 0x0 0xa20005 0x0 0x0 0x0 0x0 0x0 0x0 0xa50007 0x0 0x90 0x0 0xab0005 0x0 0x0 0x0 0x0 0x0 0x0 0xb20005 0x0 0x0 0x0 0x0 0x0 0x0 0xb70005 0x2891 0x0 0x0 0x0 0x0 0x0 0xba0007 0x13a1 0x58 0x14f0 0xc00005 0x14f0 0x0 0x0 0x0 0x0 0x0 0xc90005 0x2891 0x0 0x0 0x0 0x0 0x0 0xce0005 0x2891 0x0 0x0 0x0 0x0 0x0 0xd50005 0x2891 0x0 0x0 0x0 0x0 0x0 0xda0005 0x2891 0x0 0x0 0x0 0x0 0x0 0xdf0005 0x2891 0x0 0x0 0x0 0x0 0x0 0xe20007 0x2742 0x70 0x14f 0xe80005 0x14f 0x0 0x0 0x0 0x0 0x0 0xec0003 0x14f 0x150 0xf30007 0x13a1 0x70 0x13a1 0xf90005 0x13a1 0x0 0x0 0x0 0x0 0x0 0xfd0003 0x13a1 0xe0 0x1050005 0x13a1 0x0 0x0 0x0 0x0 0x0 0x10d0007 0x0 0x90 0x13a1 0x1130005 0x13a1 0x0 0x0 0x0 0x0 0x0 0x11a0005 0x13a1 0x0 0x0 0x0 0x0 0x0 0x1210005 0x2891 0x0 0x0 0x0 0x0 0x0 0x1260005 0x2891 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Throwable <init> (Ljava/lang/String;)V 1 355 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x10002 0x163 0x180005 0x0 0x0 0x1867daed010 0x163 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 5 java/io/IOException methods 0
ciMethodData java/lang/StackTraceElement of (Ljava/lang/Object;I)[Ljava/lang/StackTraceElement; 2 1317 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x90007 0x525 0x80 0x7134 0x120002 0x7134 0x150004 0x0 0x0 0x186767cbd48 0x7134 0x0 0x0 0x190003 0x7134 0xffffffffffffff98 0x1f0002 0x525 0x230002 0x525 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 9 java/lang/StackTraceElement methods 0
ciMethodData java/lang/StackTraceElement of ([Ljava/lang/StackTraceElement;)[Ljava/lang/StackTraceElement; 2 1317 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x90007 0x525 0x70 0x7134 0x130005 0x7134 0x0 0x0 0x0 0x0 0x0 0x190003 0x7134 0xffffffffffffffa8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 2 2453 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10007 0x995 0x38 0x0 0x70003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 2 2453 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x10002 0x995 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/Throwable toString ()Ljava/lang/String; 2 2429 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x10005 0x97d 0x0 0x0 0x0 0x0 0x0 0x40005 0x97d 0x0 0x0 0x0 0x0 0x0 0x90005 0x15b 0x0 0x1867daed010 0x56c 0x1867daed0a0 0x2b6 0xe0007 0x0 0x128 0x97d 0x150002 0x97d 0x190005 0x97d 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x97d 0x0 0x0 0x0 0x0 0x0 0x220005 0x97d 0x0 0x0 0x0 0x0 0x0 0x250005 0x97d 0x0 0x0 0x0 0x0 0x0 0x280003 0x97d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 2 17 java/io/IOException 19 java/util/concurrent/ExecutionException methods 0
ciMethodData java/lang/Throwable getLocalizedMessage ()Ljava/lang/String; 2 2429 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x15b 0x0 0x1867daed010 0x56c 0x1867daed0a0 0x2b6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 2 3 java/io/IOException 5 java/util/concurrent/ExecutionException methods 0
ciMethodData java/lang/Throwable printEnclosedStackTrace (Ljava/lang/Throwable$PrintStreamOrWriter;[Ljava/lang/StackTraceElement;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)V 2 1026 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 326 0x30007 0x402 0x88 0x0 0x70005 0x0 0x0 0x0 0x0 0x0 0x0 0xa0007 0x0 0x30 0x0 0x110002 0x0 0x180005 0x0 0x0 0x1867daeceb0 0x402 0x0 0x0 0x1d0007 0x402 0x1d0 0x0 0x250002 0x0 0x2a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x330005 0x0 0x0 0x0 0x0 0x0 0x0 0x370005 0x0 0x0 0x0 0x0 0x0 0x0 0x3c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x420005 0x0 0x0 0x0 0x0 0x0 0x0 0x450003 0x0 0x730 0x4b0005 0x0 0x0 0x1867daeceb0 0x402 0x0 0x0 0x520005 0x402 0x0 0x0 0x0 0x0 0x0 0x660007 0x0 0xb0 0x5028 0x6b0007 0x0 0x90 0x5028 0x770005 0x5028 0x0 0x0 0x0 0x0 0x0 0x7a0007 0x402 0x38 0x4c26 0x830003 0x4c26 0xffffffffffffff68 0x950002 0x402 0x9a0005 0x402 0x0 0x0 0x0 0x0 0x0 0x9e0005 0x402 0x0 0x0 0x0 0x0 0x0 0xa20005 0x402 0x0 0x0 0x0 0x0 0x0 0xa50005 0x402 0x0 0x0 0x0 0x0 0x0 0xa80005 0x0 0x0 0x1867daecf60 0x402 0x0 0x0 0xb20007 0x402 0x160 0x1ab8 0xba0002 0x1ab8 0xbf0005 0x1ab8 0x0 0x0 0x0 0x0 0x0 0xc40005 0x1ab8 0x0 0x0 0x0 0x0 0x0 0xcc0005 0x1ab8 0x0 0x0 0x0 0x0 0x0 0xcf0005 0x1ab8 0x0 0x0 0x0 0x0 0x0 0xd20005 0x0 0x0 0x1867daecf60 0x1ab8 0x0 0x0 0xd80003 0x1ab8 0xfffffffffffffeb8 0xdd0007 0x0 0x180 0x402 0xe50002 0x402 0xea0005 0x402 0x0 0x0 0x0 0x0 0x0 0xef0005 0x402 0x0 0x0 0x0 0x0 0x0 0xf40005 0x402 0x0 0x0 0x0 0x0 0x0 0xf90005 0x402 0x0 0x0 0x0 0x0 0x0 0xfc0005 0x402 0x0 0x0 0x0 0x0 0x0 0xff0005 0x0 0x0 0x1867daecf60 0x402 0x0 0x0 0x1030005 0x402 0x0 0x0 0x0 0x0 0x0 0x1140007 0x402 0x128 0x0 0x1290002 0x0 0x12e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1330005 0x0 0x0 0x0 0x0 0x0 0x0 0x1360005 0x0 0x0 0x0 0x0 0x0 0x0 0x13b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1410003 0x0 0xfffffffffffffef0 0x1450005 0x0 0x0 0x1867daed010 0x2ac 0x1867daed0a0 0x156 0x14c0007 0x156 0x58 0x2ac 0x15a0005 0x2ac 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 7 20 java/util/Collections$SetFromMap 85 java/util/Collections$SetFromMap 151 java/lang/Throwable$WrappedPrintWriter 192 java/lang/Throwable$WrappedPrintWriter 243 java/lang/Throwable$WrappedPrintWriter 294 java/io/IOException 296 java/util/concurrent/ExecutionException methods 0
ciMethodData java/lang/Throwable getOurStackTrace ()[Ljava/lang/StackTraceElement; 2 1374 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 26 0x70007 0x55e 0x40 0x0 0xe0007 0x0 0x68 0x0 0x150007 0x0 0x48 0x55e 0x210002 0x55e 0x270003 0x55d 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Throwable getSuppressed ()[Ljava/lang/Throwable; 2 1392 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x70007 0x570 0x40 0x0 0xe0007 0x0 0x20 0x0 0x1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x210004 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 1302 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x10002 0x516 0x130002 0x516 0x200007 0x516 0xa0 0x51 0x260007 0x51 0x58 0x0 0x390004 0x0 0x0 0x0 0x0 0x0 0x0 0x410002 0x51 0x460003 0x51 0xffffffffffffff78 0x5a0007 0x516 0x90 0x0 0x600005 0x0 0x0 0x0 0x0 0x0 0x0 0x630007 0x0 0x38 0x0 0x660003 0x0 0xfffffffffffffed8 0x780004 0x0 0x0 0x1867df10460 0x146 0x1867daed010 0x28b 0x800004 0x0 0x0 0x186767cc138 0x516 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 3 45 com/google/gson/JsonSyntaxException 47 java/io/IOException 52 java/lang/Boolean methods 0
ciMethodData java/util/IdentityHashMap nextKeyIndex (II)I 1 162 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 12 0x40007 0x4 0x38 0x9e 0xa0003 0x9e 0x18 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/Collections$SetFromMap contains (Ljava/lang/Object;)Z 1 942 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 134 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x50005 0x0 0x0 0x1867f1722c0 0x3ae 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/util/IdentityHashMap methods 0
compile java/lang/Throwable printEnclosedStackTrace (Ljava/lang/Throwable$PrintStreamOrWriter;[Ljava/lang/StackTraceElement;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)V -1 4 inline 213 0 -1 0 java/lang/Throwable printEnclosedStackTrace (Ljava/lang/Throwable$PrintStreamOrWriter;[Ljava/lang/StackTraceElement;Ljava/lang/String;Ljava/lang/String;Ljava/util/Set;)V 1 24 0 java/util/Collections$SetFromMap contains (Ljava/lang/Object;)Z 1 75 0 java/util/Collections$SetFromMap add (Ljava/lang/Object;)Z 2 8 0 java/util/WeakHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 3 1 0 java/util/WeakHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 3 7 0 java/util/WeakHashMap hash (Ljava/lang/Object;)I 3 13 0 java/util/WeakHashMap getTable ()[Ljava/util/WeakHashMap$Entry; 4 1 0 java/util/WeakHashMap expungeStaleEntries ()V 5 4 0 java/lang/ref/ReferenceQueue poll ()Ljava/lang/ref/Reference; 6 1 0 java/lang/ref/ReferenceQueue headIsNull ()Z 6 13 0 java/util/concurrent/locks/ReentrantLock lock ()V 7 4 0 java/util/concurrent/locks/ReentrantLock$Sync lock ()V 8 1 0 java/util/concurrent/locks/ReentrantLock$NonfairSync initialTryLock ()Z 9 7 0 java/util/concurrent/locks/AbstractQueuedSynchronizer compareAndSetState (II)Z 9 15 0 java/util/concurrent/locks/AbstractOwnableSynchronizer setExclusiveOwnerThread (Ljava/lang/Thread;)V 9 21 0 java/util/concurrent/locks/AbstractOwnableSynchronizer getExclusiveOwnerThread ()Ljava/lang/Thread; 9 29 0 java/util/concurrent/locks/AbstractQueuedSynchronizer getState ()I 9 45 0 java/lang/Error <init> (Ljava/lang/String;)V 10 2 0 java/lang/Throwable <init> (Ljava/lang/String;)V 11 1 0 java/lang/Object <init> ()V 11 24 0 java/lang/Throwable fillInStackTrace ()Ljava/lang/Throwable; 9 51 0 java/util/concurrent/locks/AbstractQueuedSynchronizer setState (I)V 6 25 0 java/util/concurrent/locks/ReentrantLock unlock ()V 7 5 0 java/util/concurrent/locks/AbstractQueuedSynchronizer release (I)Z 8 2 0 java/util/concurrent/locks/ReentrantLock$Sync tryRelease (I)Z 9 1 0 java/util/concurrent/locks/AbstractQueuedSynchronizer getState ()I 9 8 0 java/util/concurrent/locks/AbstractOwnableSynchronizer getExclusiveOwnerThread ()Ljava/lang/Thread; 9 41 0 java/util/concurrent/locks/AbstractOwnableSynchronizer setExclusiveOwnerThread (Ljava/lang/Thread;)V 9 46 0 java/util/concurrent/locks/AbstractQueuedSynchronizer setState (I)V 8 12 0 java/util/concurrent/locks/AbstractQueuedSynchronizer signalNext (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;)V 3 23 0 java/util/WeakHashMap indexFor (II)I 3 127 0 java/util/WeakHashMap$Entry <init> (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;ILjava/util/WeakHashMap$Entry;)V 4 3 0 java/lang/ref/WeakReference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 5 3 0 java/lang/ref/Reference <init> (Ljava/lang/Object;Ljava/lang/ref/ReferenceQueue;)V 6 1 0 java/lang/Object <init> ()V 2 8 0 java/util/IdentityHashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 3 1 0 java/util/IdentityHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 3 19 0 java/util/IdentityHashMap hash (Ljava/lang/Object;I)I 3 65 0 java/util/IdentityHashMap nextKeyIndex (II)I 1 82 0 java/lang/Throwable getOurStackTrace ()[Ljava/lang/StackTraceElement; 2 33 0 java/lang/StackTraceElement of (Ljava/lang/Object;I)[Ljava/lang/StackTraceElement; 3 18 0 java/lang/StackTraceElement <init> ()V 4 1 0 java/lang/Object <init> ()V 3 35 0 java/lang/StackTraceElement of ([Ljava/lang/StackTraceElement;)[Ljava/lang/StackTraceElement; 1 119 0 java/lang/StackTraceElement equals (Ljava/lang/Object;)Z 2 38 0 java/lang/String equals (Ljava/lang/Object;)Z 2 52 0 java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 2 66 0 java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 2 80 0 java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 2 94 0 java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 2 108 0 java/util/Objects equals (Ljava/lang/Object;Ljava/lang/Object;)Z 1 149 0 java/lang/StringBuilder <init> ()V 2 3 0 java/lang/AbstractStringBuilder <init> (I)V 3 1 0 java/lang/Object <init> ()V 1 154 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 2 0 java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 3 10 0 java/lang/String length ()I 4 6 0 java/lang/String coder ()B 3 21 0 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 4 32 0 java/util/Arrays copyOf ([BI)[B 3 30 0 java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 4 2 0 java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 5 11 0 java/lang/String coder ()B 4 15 0 java/lang/String getBytes ([BIB)V 5 1 0 java/lang/String coder ()B 1 158 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 2 0 java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 3 10 0 java/lang/String length ()I 4 6 0 java/lang/String coder ()B 3 21 0 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 4 32 0 java/util/Arrays copyOf ([BI)[B 3 30 0 java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 4 2 0 java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 5 11 0 java/lang/String coder ()B 4 15 0 java/lang/String getBytes ([BIB)V 5 1 0 java/lang/String coder ()B 1 162 0 java/lang/StringBuilder append (Ljava/lang/Object;)Ljava/lang/StringBuilder; 2 2 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 3 11 0 java/lang/Throwable toString ()Ljava/lang/String; 4 4 0 java/lang/Class getName ()Ljava/lang/String; 4 9 0 java/lang/Throwable getLocalizedMessage ()Ljava/lang/String; 4 21 0 java/lang/StringBuilder <init> ()V 4 25 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 30 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 34 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 4 37 0 java/lang/StringBuilder toString ()Ljava/lang/String; 2 5 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 3 2 0 java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 4 10 0 java/lang/String length ()I 5 6 0 java/lang/String coder ()B 4 21 0 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 5 32 0 java/util/Arrays copyOf ([BI)[B 4 30 0 java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 5 2 0 java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 6 11 0 java/lang/String coder ()B 5 15 0 java/lang/String getBytes ([BIB)V 6 1 0 java/lang/String coder ()B 1 165 0 java/lang/StringBuilder toString ()Ljava/lang/String; 2 5 0 java/lang/String <init> (Ljava/lang/StringBuilder;)V 3 3 0 java/lang/String <init> (Ljava/lang/AbstractStringBuilder;Ljava/lang/Void;)V 4 1 0 java/lang/Object <init> ()V 4 5 0 java/lang/AbstractStringBuilder getValue ()[B 4 10 0 java/lang/StringBuilder length ()I 5 1 0 java/lang/AbstractStringBuilder length ()I 4 16 0 java/lang/AbstractStringBuilder isLatin1 ()Z 4 32 0 java/util/Arrays copyOfRange ([BII)[B 5 13 0 java/util/Arrays copyOfRangeByte ([BII)[B 6 2 0 java/util/Arrays checkLength (II)V 1 168 0 java/lang/Throwable$WrappedPrintWriter println (Ljava/lang/Object;)V 2 5 0 java/io/PrintWriter println (Ljava/lang/Object;)V 3 1 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 4 11 0 java/lang/String toString ()Ljava/lang/String; 3 65 0 java/io/PrintWriter print (Ljava/lang/String;)V 4 2 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 5 11 0 java/lang/String toString ()Ljava/lang/String; 4 5 0 java/io/PrintWriter write (Ljava/lang/String;)V 5 4 0 java/lang/String length ()I 6 6 0 java/lang/String coder ()B 5 7 0 java/io/PrintWriter write (Ljava/lang/String;II)V 6 64 0 java/io/PrintWriter implWrite (Ljava/lang/String;II)V 7 1 0 java/io/PrintWriter ensureOpen ()V 7 11 0 java/io/StringWriter write (Ljava/lang/String;II)V 8 9 0 java/lang/StringBuffer append (Ljava/lang/CharSequence;II)Ljava/lang/StringBuffer; 3 69 0 java/io/PrintWriter println ()V 1 186 0 java/lang/StringBuilder <init> ()V 2 3 0 java/lang/AbstractStringBuilder <init> (I)V 3 1 0 java/lang/Object <init> ()V 1 191 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 2 0 java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 3 10 0 java/lang/String length ()I 4 6 0 java/lang/String coder ()B 3 21 0 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 4 32 0 java/util/Arrays copyOf ([BI)[B 3 30 0 java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 4 2 0 java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 5 11 0 java/lang/String coder ()B 4 15 0 java/lang/String getBytes ([BIB)V 5 1 0 java/lang/String coder ()B 1 196 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 2 0 java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 3 10 0 java/lang/String length ()I 4 6 0 java/lang/String coder ()B 3 21 0 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 4 32 0 java/util/Arrays copyOf ([BI)[B 3 30 0 java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 4 2 0 java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 5 11 0 java/lang/String coder ()B 4 15 0 java/lang/String getBytes ([BIB)V 5 1 0 java/lang/String coder ()B 1 204 0 java/lang/StringBuilder append (Ljava/lang/Object;)Ljava/lang/StringBuilder; 2 2 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 2 5 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 3 2 0 java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 4 10 0 java/lang/String length ()I 5 6 0 java/lang/String coder ()B 4 21 0 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 5 32 0 java/util/Arrays copyOf ([BI)[B 4 30 0 java/lang/AbstractStringBuilder putStringAt (ILjava/lang/String;)V 5 2 0 java/lang/AbstractStringBuilder inflateIfNeededFor (Ljava/lang/String;)V 6 11 0 java/lang/String coder ()B 5 15 0 java/lang/String getBytes ([BIB)V 6 1 0 java/lang/String coder ()B 1 207 0 java/lang/StringBuilder toString ()Ljava/lang/String; 2 5 0 java/lang/String <init> (Ljava/lang/StringBuilder;)V 3 3 0 java/lang/String <init> (Ljava/lang/AbstractStringBuilder;Ljava/lang/Void;)V 4 1 0 java/lang/Object <init> ()V 4 5 0 java/lang/AbstractStringBuilder getValue ()[B 4 10 0 java/lang/StringBuilder length ()I 5 1 0 java/lang/AbstractStringBuilder length ()I 4 16 0 java/lang/AbstractStringBuilder isLatin1 ()Z 4 32 0 java/util/Arrays copyOfRange ([BII)[B 5 13 0 java/util/Arrays copyOfRangeByte ([BII)[B 6 2 0 java/util/Arrays checkLength (II)V 1 210 0 java/lang/Throwable$WrappedPrintWriter println (Ljava/lang/Object;)V 2 5 0 java/io/PrintWriter println (Ljava/lang/Object;)V 3 1 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 4 11 0 java/lang/String toString ()Ljava/lang/String; 3 65 0 java/io/PrintWriter print (Ljava/lang/String;)V 4 2 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 5 11 0 java/lang/String toString ()Ljava/lang/String; 4 5 0 java/io/PrintWriter write (Ljava/lang/String;)V 5 4 0 java/lang/String length ()I 6 6 0 java/lang/String coder ()B 5 7 0 java/io/PrintWriter write (Ljava/lang/String;II)V 6 64 0 java/io/PrintWriter implWrite (Ljava/lang/String;II)V 7 1 0 java/io/PrintWriter ensureOpen ()V 7 11 0 java/io/StringWriter write (Ljava/lang/String;II)V 8 9 0 java/lang/StringBuffer append (Ljava/lang/CharSequence;II)Ljava/lang/StringBuffer; 3 69 0 java/io/PrintWriter println ()V 1 229 0 java/lang/StringBuilder <init> ()V 1 234 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 1 239 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 1 244 0 java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 1 249 0 java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 1 252 0 java/lang/StringBuilder toString ()Ljava/lang/String; 1 255 0 java/lang/Throwable$WrappedPrintWriter println (Ljava/lang/Object;)V 2 5 0 java/io/PrintWriter println (Ljava/lang/Object;)V 3 1 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 4 11 0 java/lang/String toString ()Ljava/lang/String; 3 65 0 java/io/PrintWriter print (Ljava/lang/String;)V 4 2 0 java/lang/String valueOf (Ljava/lang/Object;)Ljava/lang/String; 5 11 0 java/lang/String toString ()Ljava/lang/String; 4 5 0 java/io/PrintWriter write (Ljava/lang/String;)V 5 4 0 java/lang/String length ()I 6 6 0 java/lang/String coder ()B 5 7 0 java/io/PrintWriter write (Ljava/lang/String;II)V 6 64 0 java/io/PrintWriter implWrite (Ljava/lang/String;II)V 7 1 0 java/io/PrintWriter ensureOpen ()V 7 11 0 java/io/StringWriter write (Ljava/lang/String;II)V 8 9 0 java/lang/StringBuffer append (Ljava/lang/CharSequence;II)Ljava/lang/StringBuffer; 3 69 0 java/io/PrintWriter println ()V 1 259 0 java/lang/Throwable getSuppressed ()[Ljava/lang/Throwable; 1 325 0 java/lang/Throwable getCause ()Ljava/lang/Throwable;
