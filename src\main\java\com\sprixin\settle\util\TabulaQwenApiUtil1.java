package com.sprixin.settle.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import technology.tabula.ObjectExtractor;
import technology.tabula.Page;
import technology.tabula.RectangularTextContainer;
import technology.tabula.Table;
import technology.tabula.extractors.SpreadsheetExtractionAlgorithm;
import org.slf4j.Logger;
import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TabulaQwenApiUtil1 {
     private static final Logger logger = LoggerFactory.getLogger(TabulaQwenApiUtil1.class);
    private static String API_URL;
    private static String API_KEY;
    private static String MODEL;

    @Value("${ai.model.url}")
    private String apiUrlProp;
    @Value("${ai.model.key}")
    private String apiKeyProp;
    @Value("${ai.model.name}")
    private String modelProp;

    @PostConstruct
    public void init() {
        API_URL = apiUrlProp;
        API_KEY = apiKeyProp;
        MODEL = modelProp;
    }

    /**
     * 主解析流程，采用两阶段法：先识别实体，再提取数据。
     * @param pdfFile PDF文件
     * @return 包含所有结算实体数据的JSON数组字符串。
     */
    public static String parsePdf(File pdfFile) throws IOException {
        // FIX: 使用嵌套的 try-with-resources 来统一管理 document 和 extractor 的生命周期
        try (PDDocument document = WatermarkRemover.removeWatermark(pdfFile);
             ObjectExtractor extractor = new ObjectExtractor(document)) {

            // --- 第一阶段：识别所有交易单元 ---
            String fullText = extractFullText(document);
            JSONArray identifiedUnits = identifyTradingUnits(fullText);

            if (identifiedUnits == null || identifiedUnits.isEmpty()) {
                System.err.println("警告: 未能从文档中识别出任何交易单元。");
                return "[]";
            }

            // --- 第二阶段：基于识别的单元进行全面数据提取 ---
            // FIX: 传入已创建的 extractor 对象
            String fullTableData = extractFullTableData(document, extractor);
            return extractDataForUnits(fullText, fullTableData, identifiedUnits);
        }
    }

    /**
     * [第一阶段] 调用大模型识别文档中的所有公司和交易单元。
     * @param fullText 整个文档的纯文本内容。
     * @return 一个JSON数组，每个对象包含 'company' 和 'tradingUnitName'。
     */
    private static JSONArray identifyTradingUnits(String fullText) throws IOException {
        String prompt = buildUnitIdentificationPrompt(fullText);
        String apiResponse = callQwenApi(prompt);
        return JSONArray.parseArray(apiResponse);
    }

    /**
     * [第二阶段] 调用大模型为已识别的单元列表提取详细数据。
     * @param fullText 整个文档的文本。
     * @param fullTableData 整个文档的表格数据。
     * @param unitsToExtract 从第一阶段获取的单元列表。
     * @return 包含每个单元详细数据的最终JSON数组字符串。
     */
    private static String extractDataForUnits(String fullText, String fullTableData, JSONArray unitsToExtract) throws IOException {
        String prompt = buildComprehensiveExtractionPrompt(fullText, fullTableData, unitsToExtract);
        return callQwenApi(prompt);
    }


    /**
     * [提示词构建] 为第一阶段的“实体识别”任务构建Prompt。
     */
    private static String buildUnitIdentificationPrompt(String fullText) {
        // ... (此方法无需修改)
        return "你是一个文档结构分析专家。你的唯一任务是从下面的文本中识别出所有的【市场主体公司】以及它们下属的【交易单元】。\n\n" +
               "--- 文档纯文本 ---\n" + fullText + "\n\n" +
               "--- 分析规则 ---\n" +
               "1. **市场主体 (company)**: 通常位于文档的开头，例如 '交城县明科光电科技有限公司'。\n" +
               "2. **交易单元 (tradingUnitName)**: 通常是带有 '一期', '二期' 等字样的名称，如 '明科光伏一期'。它们是关键的分割点。\n" +
               "3. **处理三种情况**:\n" +
               "   - **情况1 (多单元)**: 如果找到多个交易单元 (如 '一期' 和 '二期')，请为每一个单元生成一个独立的对象。\n" +
               "   - **情况2 (单单元)**: 如果只找到一个明确的交易单元，也生成一个对象。\n" +
               "   - **情况3 (无单元)**: 如果通篇没有发现像 'xx期' 这样的明确单元划分，则认为整个文档属于一个默认单元。在这种情况下，请使用【市场主体公司名】作为`tradingUnitName`。\n\n" +
               "--- 输出格式 ---\n" +
               "请严格以JSON数组格式返回结果，不要添加任何解释。每个对象包含两个键：\n" +
               "```json\n" +
               "[\n" +
               "  {\n" +
               "    \"company\": \"公司全称\",\n" +
               "    \"tradingUnitName\": \"交易单元一名称\"\n" +
               "  },\n" +
               "  {\n" +
               "    \"company\": \"公司全称\",\n" +
               "    \"tradingUnitName\": \"交易单元二名称\"\n" +
               "  }\n" +
               "]\n" +
               "```";
    }

    /**
     * [提示词构建] 为第二阶段的“数据提取”任务构建Prompt。
     */
    private static String buildComprehensiveExtractionPrompt2(String fullText, String fullTableData, JSONArray unitsToExtract) {
        // ... (此方法无需修改)
        return "你是一位顶级的、精确的财务数据提取专家。下面提供了电力结算单的全部文本和表格内容，以及一个需要提取数据的实体列表。\n" +
               "你的任务是根据完整的文档上下文，为列表中的**每一个**实体，精确地提取其对应的摘要信息和所有明细条目。\n\n" +
               "--- 待提取的实体列表 ---\n" + unitsToExtract.toJSONString() + "\n\n" +
               "--- 完整文档文本 ---\n" + fullText + "\n\n" +
               "--- 完整文档表格数据 ---\n" + fullTableData + "\n\n" +
               "--- 提取指令 ---\n" +
               "1. **数据归属**: 这是最重要的规则！你必须准确判断每一条摘要数据和明细条目属于哪个交易单元。注意，一个单元的结束和另一个单元的开始可能发生在**同一页**。\n" +
               "2. **摘要信息**: 为每个实体提取 `header` 部分，包括 `province`, `settlementDate`, `period`, `onlineElectricity`, `settlementElectricity`, `contractElectricity`, `deviationElectricity`, `totalFee`。\n" +
               "3. **明细信息**: 为每个实体提取其所有的 `details` 列表。每个明细项包含 `code`, `name`, `plannedElectricity`, `settlementElectricity`, `settlementPrice`, `settlementFee`。\n" +
               "4. **空值处理**: 如果某个字段信息不存在，请在JSON中将该字段的值设为 `null`。\n" +
               "5. **数值转换**: 所有数值字段必须是数字格式。\n\n" +
               "6. **需要确保每列数据对应正确的表头。\n\n" +
               "--- 输出格式 ---\n" +
               "请严格以JSON数组格式返回结果，数组中的每个JSON对象对应“待提取的实体列表”中的一个实体。结构如下：\n" +
               "```json\n" +
               "[\n" +
               "  {\n" +
               "    \"company\": \"交城县明科光电科技有限公司\",\n" +
               "    \"tradingUnitName\": \"明科光伏一期\",\n" +
               "    \"header\": { ... },\n" +
               "    \"details\": [ { ... }, { ... } ]\n" +
               "  },\n" +
               "  {\n" +
               "    \"company\": \"交城县明科光电科技有限公司\",\n" +
               "    \"tradingUnitName\": \"明科光伏二期\",\n" +
               "    \"header\": { ... },\n" +
               "    \"details\": [ { ... }, { ... } ]\n" +
               "  }\n" +
               "]\n" +
               "```";
    }


     /**
     * [提示词构建] 为第二阶段的“数据提取”任务构建Prompt (V3 - 增强版)。
     * 此版本专门解决汇总行导致的“列错位”问题。
     */
    private static String buildComprehensiveExtractionPrompt(String fullText, String fullTableData, JSONArray unitsToExtract) {
        return "你是一位顶级的、极其精确的财务数据提取专家。下面提供了电力结算单的全部文本和表格内容，以及一个需要提取数据的实体列表。\n" +
               "你的任务是根据完整的文档上下文，为列表中的**每一个**实体，精确地提取其对应的摘要信息和所有明细条目。\n\n" +
               "--- 待提取的实体列表 ---\n" + unitsToExtract.toJSONString() + "\n\n" +
               "--- 完整文档文本 ---\n" + fullText + "\n\n" +
               "--- 完整文档表格数据 ---\n" + fullTableData + "\n\n" +
               "--- 提取指令 ---\n" +
               "1. **数据归属**: 这是最重要的规则！你必须准确判断每一条摘要数据和明细条目属于哪个交易单元。注意，一个单元的结束和另一个单元的开始可能发生在**同一页**。\n" +
               "2. **摘要信息**: 为每个实体提取 `header` 部分。\n" +
               "3. **明细信息**: 为每个实体提取其所有的 `details` 列表。\n\n" +
               "--- 关键规则：数据归属与列对齐 (必须严格遵守！) ---\n" +
               "1. **列的定义是固定的**：表格的列顺序是：`结算科目编码`, `结算科目`, `交易计划电量`, `结算电量/容量`, `结算电价/均价`, `结算电费`。\n" +
               "2. **禁止列数据漂移**：如果原始表格的某一行在某一列（例如 ‘结算电价/均价’）没有值，那么在JSON中对应的字段（`settlementPrice`）**必须为 `null`**。\n" +
               "3. **绝对禁止** 用右侧列（如 ‘结算电费’）的数据去填充左侧为空的列（如 ‘结算电价/均价’）。这是最常见的错误，请务必避免。\n" +
               "4. **数值特征提示**：通常情况下，`settlementPrice` (单价) 是一个几百左右的数字，而 `settlementFee` (总费用) 是一个大得多的数字。请利用这个特征来验证你的提取是否正确。\n\n" +
               "--- 输出格式 ---\n" +
               "请严格以JSON数组格式返回结果，数组中的每个JSON对象对应“待提取的实体列表”中的一个实体。结构如下，请注意 `header` 和 `details` 的字段要完整：\n" +
               "```json\n" +
               "[\n" +
               "  {\n" +
               "    \"company\": \"公司全称\",\n" +
               "    \"tradingUnitName\": \"交易单元名称\",\n" +
               "    \"header\": {\n" +
               "      \"province\": \"省份\",\n" +
               "      \"settlementDate\": \"YYYY-MM\",\n" +
               "      \"period\": \"期间\",\n" +
               "      \"onlineElectricity\": 数字或null,\n" +
               "      \"settlementElectricity\": 数字或null,\n" +
               "      \"contractElectricity\": 数字或null,\n" +
               "      \"deviationElectricity\": 数字或null,\n" +
               "      \"totalFee\": 数字或null\n" +
               "    },\n" +
               "    \"details\": [\n" +
               "      {\n" +
               "        \"code\": \"编码\",\n" +
               "        \"name\": \"名称\",\n" +
               "        \"plannedElectricity\": 数字或null,\n" +
               "        \"settlementElectricity\": 数字或null,\n" +
               "        \"settlementPrice\": 数字或null,\n" +
               "        \"settlementFee\": 数字或null\n" +
               "      }\n" +
               "    ]\n" +
               "  }\n" +
               "]\n" +
               "```";
    }

    // --- 以下为数据提取和API调用的辅助工具方法 ---

    private static String extractFullText(PDDocument document) throws IOException {
        PDFTextStripper stripper = new PDFTextStripper();
        stripper.setSortByPosition(true);
        return stripper.getText(document);
    }

    // FIX: 方法签名改变，接收一个 ObjectExtractor 实例
    private static String extractFullTableData(PDDocument document, ObjectExtractor extractor) {
        StringBuilder allTablesText = new StringBuilder();
        int pageCount = document.getNumberOfPages();
        for (int pageNum = 1; pageNum <= pageCount; pageNum++) {
            allTablesText.append("--- Page ").append(pageNum).append(" Tables ---\n");
            // FIX: 将 extractor 传递下去
            allTablesText.append(extractPageTableData(extractor, pageNum));
            allTablesText.append("\n");
        }
        return allTablesText.toString();
    }
    
    // FIX: 方法签名改变，不再需要 PDDocument，直接使用 extractor
    private static String extractPageTableData(ObjectExtractor extractor, int pageNum) {
        StringBuilder tableText = new StringBuilder();
        // FIX: 移除 try-with-resources，因为 extractor 的生命周期由外部管理
        try {
            Page page = extractor.extract(pageNum);
            SpreadsheetExtractionAlgorithm sea = new SpreadsheetExtractionAlgorithm();
            List<Table> tables = sea.extract(page);

            if (tables.isEmpty()) return "";

            for (Table table : tables) {
                // ... (内部逻辑不变)
                List<List<RectangularTextContainer>> rows = table.getRows();
                if (rows.isEmpty()) continue;

                int maxColumns = rows.stream().mapToInt(List::size).max().orElse(0);
                if (maxColumns < 2) continue;

                for (List<RectangularTextContainer> row : rows) {
                    String firstCellText = row.isEmpty() ? "" : row.get(0).getText();
                    if (firstCellText.contains("合计") || firstCellText.contains("总计") || firstCellText.contains("小计")) {
                        continue;
                    }
                    for (int i = 0; i < maxColumns; i++) {

                        String cellText = (i < row.size()) ? normalizeCellText(row.get(i).getText()) : "[NULL]";
                       
                        tableText.append(cellText).append(", ");
                    }
                    tableText.append("\n");
                }
                tableText.append("---TABLE_SEPARATOR---\n");
            }
        } catch (Exception e) {
             System.err.println("提取页面 " + pageNum + " 的表格数据时出错: " + e.getMessage());
             return "";
        }
        return tableText.toString();
    }

    private static String normalizeCellText(String text) {
        // ... (此方法无需修改)
        if (text == null || text.trim().isEmpty()) return "[NULL]";
        String cleaned = text.replaceAll("[\\s\\u00A0]+", " ").trim();
        return cleaned.matches("^-?[\\d,]+(\\.\\d+)?$") ? cleaned.replace(",", "") : cleaned;
    }

    private static String callQwenApi(String prompt) throws IOException {
        // ... (此方法无需修改)
        JSONArray messages = new JSONArray();
        JSONObject userMsg = new JSONObject();
        userMsg.put("role", "user");
        userMsg.put("content", prompt);
        messages.add(userMsg);

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL);
        requestBody.put("messages", messages);
        requestBody.put("temperature", 0.01);

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + API_KEY);
        headers.put("Content-Type", "application/json");

        try {
            logger.info("请求:" + requestBody.toJSONString());
            HttpResponse response = HttpRequest.post(API_URL + "/v1/chat/completions")
                    .headerMap(headers, true)
                    .body(requestBody.toJSONString())
                    .timeout(12000000)
                    .execute();

            if (!response.isOk()) {
                throw new IOException("API调用失败: " + response.getStatus() + " - " + response.body());
            }
            return parseApiResponse(response.body());
        } catch (Exception e) {
            throw new IOException("API调用异常: " + e.getMessage(), e);
        }
    }

    private static String parseApiResponse(String apiResponse) {
        // ... (此方法无需修改)
        JSONObject responseJson = JSONObject.parseObject(apiResponse);
        if (responseJson == null) return "{}";

        JSONArray choices = responseJson.getJSONArray("choices");
        if (choices != null && !choices.isEmpty()) {
            JSONObject message = choices.getJSONObject(0).getJSONObject("message");
            if (message != null) {
                return extractJsonFromMarkdown(message.getString("content"));
            }
        }
        return "{}";
    }

    private static String extractJsonFromMarkdown(String content) {
        // ... (此方法无需修改)
        if (content == null || content.trim().isEmpty()) return "{}";
        int firstBracket = content.indexOf('[');
        int lastBracket = content.lastIndexOf(']');
        if (firstBracket != -1 && lastBracket > firstBracket) {
            return content.substring(firstBracket, lastBracket + 1);
        }
        
        int firstBrace = content.indexOf('{');
        int lastBrace = content.lastIndexOf('}');
        if (firstBrace != -1 && lastBrace > firstBrace) {
             return content.substring(firstBrace, lastBrace + 1);
        }
        return content;
    }
}