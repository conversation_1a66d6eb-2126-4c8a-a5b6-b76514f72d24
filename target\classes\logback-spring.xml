<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scop="context" name="logging.file" source="logging.file.name" defaultValue="/Files/logs/"/>
    <property name="logPath" value="${logging.file}"/>
    <property name="pattern" value="%green(%d{yyyy-MM-dd HH:mm:ss.SSS}) [%-10.10t] %highlight(%5level) [%green(%50.50c{50}.%-3.5L)] - %m%n"/>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logPath}/spot.info.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/%d{yyyy-MM-dd}.info.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logPath}/spot.error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logPath}/%d{yyyy-MM-dd}.error.log</fileNamePattern>
        </rollingPolicy>
        <encoder>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>
    

<!--    <logger name="org.hibernate.SQL" level="debug"/>-->
    <logger name="cn.com.sgcc" level="debug"/>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="infoFile"/>
        <appender-ref ref="errorFile"/>
    </root>
</configuration>
