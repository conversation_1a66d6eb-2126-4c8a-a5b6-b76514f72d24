package com.sprixin.settle.controller;

import com.sprixin.settle.entity.TradeTjAllSettleBill;
import com.sprixin.settle.entity.TradeTjAllSettleBillDetail;
import com.sprixin.settle.service.TradeTjAllSettleBillDetailService;
import com.sprixin.settle.service.TradeTjAllSettleBillService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sprixin.settle.entity.TradeTjAllSettleItemLabel;
import com.sprixin.settle.entity.TradeTjAllSettleSubject;
import com.sprixin.settle.entity.TradeTjProvinceInfo;
import com.sprixin.settle.service.TradeTjAllSettleItemLabelService;
import com.sprixin.settle.service.TradeTjAllSettleSubjectService;
import com.sprixin.settle.service.TradeTjProvinceInfoService;
import com.sprixin.settle.util.TabulaQwenApiUtil1;
import com.sprixin.settle.util.TabulaQwenApiUtil2;
import com.sprixin.settle.util.TabulaQwenApiUtil3;

import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.HashSet;
import java.util.Set;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Callable;

/**
 * 结算单相关接口控制器
 * 提供结算单导入、查询、明细、分项标签等API
 */
@RestController
@RequestMapping("/api/settle/bill")
public class TradeTjAllSettleBillController {

    private static final Logger logger = LoggerFactory.getLogger(TradeTjAllSettleBillController.class);
    @Autowired
    private TradeTjAllSettleBillService billService;
    @Autowired
    private TradeTjAllSettleBillDetailService billDetailService;
    @Autowired
    private TradeTjAllSettleSubjectService subjectService;
    @Autowired
    private TradeTjAllSettleItemLabelService itemLabelService;
    @Autowired
    private TradeTjProvinceInfoService provinceInfoService;
    @Value("${settle.upload.tmpdir:./tmp}")
    private String tmpDir;


    TradeTjAllSettleBillController() {
        // 构造函数
    }

   
    private void unzipWithCommons(File zipFile, File destDir) throws IOException {
        if (!destDir.exists()) destDir.mkdirs();
        ZipFile zip = new ZipFile(zipFile, "GBK");
        try {
            Enumeration<ZipArchiveEntry> entries = zip.getEntries();
            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                File outFile = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    outFile.mkdirs();
                } else {
                    File parent = outFile.getParentFile();
                    if (!parent.exists()) parent.mkdirs();
                    try (java.io.InputStream in = zip.getInputStream(entry);
                         java.io.OutputStream out = new java.io.FileOutputStream(outFile)) {
                        byte[] buffer = new byte[8192]; // 增大缓冲区
                        int len;
                        while ((len = in.read(buffer)) != -1) {
                            out.write(buffer, 0, len);
                        }
                    }
                }
            }
        } finally {
            zip.close();
        }
    }

    /**
     * 结算单导入接口（支持压缩包上传）
     * @param file 压缩包文件
     * @param subjectName 市场主体名称（可选）
     * @return 导入结果
     */
    @PostMapping("/import")
    public ResponseEntity<?> importBill(@RequestParam("file") MultipartFile file,
                                            @RequestParam(value = "subjectName", required = false) String subjectName) {
        File tmpZip = null;
        File unzipDir = null;
        try {
            tmpZip = new File(tmpDir, System.currentTimeMillis() + "_" + file.getOriginalFilename());
            FileUtils.copyInputStreamToFile(file.getInputStream(), tmpZip);
            unzipDir = new File(tmpDir, FilenameUtils.getBaseName(tmpZip.getName()));
            if (!file.getOriginalFilename().toLowerCase().endsWith(".zip")) {
                return ResponseEntity.badRequest().body("只支持zip格式的压缩包！");
            }
            try {
                unzipWithCommons(tmpZip, unzipDir);
            } catch (IllegalArgumentException e) {
                logger.error("解压zip文件格式异常", e);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("上传的压缩包格式不正确或已损坏，请重新打包后再试！（代码检测到MALFORMED）");
            } catch (Exception e) {
                logger.error("解压zip文件失败", e);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("上传的压缩包格式不正确或已损坏，请重新打包后再试！（代码检测到其他异常）");
            }
            // 并发处理PDF
            List<TradeTjAllSettleBill> billList = new CopyOnWriteArrayList<TradeTjAllSettleBill>();
            List<TradeTjAllSettleBillDetail> detailList = new CopyOnWriteArrayList<TradeTjAllSettleBillDetail>();
            List<TradeTjAllSettleItemLabel> labelList = new CopyOnWriteArrayList<TradeTjAllSettleItemLabel>();
            Map<String, TradeTjAllSettleSubject> subjectCache = new ConcurrentHashMap<String, TradeTjAllSettleSubject>();
            List<Map<String, String>> fileResults = new CopyOnWriteArrayList<Map<String, String>>();
            int totalCount = 0;
            int successCount = 0;
            List<File> pdfFiles = new ArrayList<File>(FileUtils.listFiles(unzipDir, new String[]{"pdf"}, true));
            totalCount = pdfFiles.size();
            // 根据CPU核心数和文件数量动态调整线程池大小，避免过多线程消耗内存
            int threadCount = Math.min(Math.max(Runtime.getRuntime().availableProcessors(), 2), Math.min(pdfFiles.size(), 4));
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            List<Future<Boolean>> futures = new ArrayList<Future<Boolean>>();
            for (File pdf : pdfFiles) {
                futures.add(executor.submit(new Callable<Boolean>() {
                    @Override
                    public Boolean call() throws Exception {
                    Map<String, String> fileResult = new HashMap<String, String>();
                    fileResult.put("fileName", pdf.getName());
                    try {
                        String jsonStr = null;
                        try {
                            jsonStr = TabulaQwenApiUtil3.parsePdf(pdf);
                            logger.info(jsonStr);
                        } catch (Exception e) {
                            logger.error("PDF解析失败，文件：" + pdf.getName(), e);
                            fileResult.put("status", "fail");
                            fileResult.put("message", "解析失败: " + e.getMessage());
                            fileResults.add(fileResult);
                            return false;
                        }
                        if (jsonStr == null || jsonStr.trim().isEmpty()) {
                            logger.error("PDF解析结果为空，文件：" + pdf.getName());
                            fileResult.put("status", "fail");
                            fileResult.put("message", "解析结果为空");
                            fileResults.add(fileResult);
                            return false;
                        }
                        ObjectMapper mapper = new ObjectMapper();
                        JsonNode root;
                        try {
                            root = mapper.readTree(jsonStr);
                            if (!root.has("province") && root.has("choices")) {
                                String content = root.path("choices").get(0).path("message").path("content").asText();
                                root = mapper.readTree(content);
                            }
                        } catch (Exception e) {
                            logger.error("JSON解析失败，文件：" + pdf.getName() + "，JSON内容：" + jsonStr, e);
                            fileResult.put("status", "fail");
                            fileResult.put("message", "JSON解析失败: " + e.getMessage());
                            fileResults.add(fileResult);
                            return false;
                        }
                        // 处理新格式的数据结构，支持多个市场主体
                        // 检查是否为最新的数组格式
                        if (root.isArray() && root.size() > 0) {
                            // 最新格式：直接是市场主体数组
                            for (JsonNode companyNode : root) {
                                boolean success = processNewFormatCompanyData(companyNode, pdf,
                                    subjectName, subjectCache, billList, detailList, labelList, fileResult);
                                if (!success) {
                                    return false;
                                }
                            }
                        } else {
                            // 处理旧格式（包含province和settlementDate在根级别）
                            String province = root.path("province").asText();
                            String month = root.path("settlementDate").asText();

                            // 验证省份信息，如果获取不到则跳过该PDF
                            if (province == null || province.trim().isEmpty()) {
                                logger.error("PDF解析失败：省份信息为空，文件：" + pdf.getName());
                                fileResult.put("status", "fail");
                                fileResult.put("message", "省份信息为空");
                                fileResults.add(fileResult);
                                return false;
                            }
                            TradeTjProvinceInfo provinceInfo = provinceInfoService.selectByName(province.trim());
                            if (provinceInfo == null) {
                                logger.error("PDF解析失败：找不到省份信息，省份：" + province + "，文件：" + pdf.getName());
                                fileResult.put("status", "fail");
                                fileResult.put("message", "找不到省份信息：" + province);
                                fileResults.add(fileResult);
                                return false;
                            }
                            Long provinceId = provinceInfo.getId();

                            // 检查是否为中间格式（包含多个市场主体的数组）
                            JsonNode companiesNode = root.path("companies");
                            if (companiesNode.isArray() && companiesNode.size() > 0) {
                                // 中间格式：处理多个市场主体
                                for (JsonNode companyNode : companiesNode) {
                                    boolean success = processCompanyData(companyNode, province, month, provinceId, pdf,
                                        subjectName, subjectCache, billList, detailList, labelList, fileResult);
                                    if (!success) {
                                        return false;
                                    }
                                }
                            } else {
                                // 兼容最旧格式：单个市场主体
                                String company = (subjectName != null && !subjectName.trim().isEmpty()) ?
                                    subjectName.trim() : root.path("company").asText();
                                String tradingUnitName = root.path("tradingUnitName").asText();
                                Double onlineElectricity = root.path("onlineElectricity").asDouble();
                                Double settlementElectricity = root.path("settlementElectricity").asDouble();
                                Double contractElectricity = root.path("contractElectricity").asDouble();
                                Double deviationElectricity = root.path("deviationElectricity").asDouble();
                                Double totalFee = root.path("totalFee").asDouble();
                                JsonNode details = root.path("details");

                                boolean success = processCompanyDataOld(company, tradingUnitName, month, provinceId,
                                    onlineElectricity, settlementElectricity, contractElectricity,
                                    deviationElectricity, totalFee, details, pdf, subjectCache,
                                    billList, detailList, labelList, fileResult);
                                if (!success) {
                                    return false;
                                }
                            }
                        }

                        fileResult.put("status", "success");
                        fileResult.put("message", "");
                        fileResults.add(fileResult);
                        logger.info("PDF解析成功，文件：" + pdf.getName());
                        return true;
                    } catch (Exception e) {
                        logger.error("处理PDF时发生异常，文件：" + pdf.getName(), e);
                        fileResult.put("status", "fail");
                        fileResult.put("message", "处理异常: " + e.getMessage());
                        fileResults.add(fileResult);
                        return false;
                    }
                    }
                }));
            }
            executor.shutdown();
            try {
                // 等待所有任务完成，最多等待30分钟
                if (!executor.awaitTermination(60, java.util.concurrent.TimeUnit.MINUTES)) {
                    executor.shutdownNow();
                    logger.warn("强制关闭线程池，部分任务可能未完成");
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }

            for (Future<Boolean> f : futures) {
                try { if (f.get()) successCount++; } catch (Exception ignore) {}
            }
            // 批量入库
            if (!billList.isEmpty()) billService.replaceBatch(billList);
            if (!detailList.isEmpty()) billDetailService.replaceBatch(detailList);
            if (!labelList.isEmpty()) itemLabelService.replaceBatch(labelList);

            // 构建返回结果
            Map<String, Object> result = new HashMap<String, Object>();
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", totalCount - successCount);
            result.put("fileResults", fileResults);
            result.put("message", "导入完成");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("结算单导入接口异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("服务器异常：" + e.getMessage());
        } finally {
            // 清理临时文件，释放内存
            cleanupTempFiles(tmpZip, unzipDir);
            // 强制垃圾回收
            System.gc();
        }
    }

    /**
     * 修改市场主体名称
     * @param subjectId 市场主体ID
     * @param newName 新的市场主体名称
     * @return 修改结果
     */
    @PostMapping("/updateSubjectName")
    public ResponseEntity<?> updateSubjectName(@RequestParam("subjectId") Long subjectId,
                                               @RequestParam("newName") String newName) {
        try {
            if (subjectId == null) {
                return ResponseEntity.badRequest().body("市场主体ID不能为空");
            }

            if (newName == null || newName.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("市场主体名称不能为空");
            }

            // 目前用户ID写死为1，后续可以从session或token中获取
            Long userId = 1L;

            // 检查市场主体是否存在且属于当前用户
            TradeTjAllSettleSubject subject = subjectService.getSubjectById(subjectId);
            if (subject == null || !userId.equals(subject.getUserId())) {
                return ResponseEntity.badRequest().body("市场主体不存在或无权限修改");
            }

            // 检查同省份下是否已存在相同名称的市场主体（限制在当前用户范围内）
            TradeTjAllSettleSubject existingSubject = subjectService.getByNameProvinceAndUserId(newName.trim(), subject.getProvinceId(), userId);
            if (existingSubject != null && !existingSubject.getId().equals(subjectId)) {
                return ResponseEntity.badRequest().body("该省份下已存在同名的市场主体");
            }

            // 更新市场主体名称
            subject.setName(newName.trim());
            int result = subjectService.updateSubject(subject);

            if (result > 0) {
                Map<String, Object> response = new HashMap<String, Object>();
                response.put("success", true);
                response.put("message", "市场主体名称修改成功");
                response.put("subjectId", subjectId);
                response.put("newName", newName.trim());
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("修改失败");
            }
        } catch (Exception e) {
            logger.error("修改市场主体名称失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("修改失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除市场主体及相关数据
     * @param subjectIds 市场主体ID列表，逗号分隔
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ResponseEntity<?> deleteSubjects(@RequestParam("subjectIds") String subjectIds) {
        try {
            if (subjectIds == null || subjectIds.trim().isEmpty()) {
                return ResponseEntity.badRequest().body("请选择要删除的市场主体");
            }

            // 解析市场主体ID列表
            List<Long> subjectIdList = new ArrayList<Long>();
            for (String idStr : subjectIds.split(",")) {
                try {
                    subjectIdList.add(Long.valueOf(idStr.trim()));
                } catch (NumberFormatException e) {
                    return ResponseEntity.badRequest().body("市场主体ID格式错误: " + idStr);
                }
            }

            if (subjectIdList.isEmpty()) {
                return ResponseEntity.badRequest().body("请选择要删除的市场主体");
            }

            // 执行删除操作
            Map<String, Object> result = deleteSubjectData(subjectIdList);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("删除市场主体数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("删除失败: " + e.getMessage());
        }
    }

    /**
     * 执行删除市场主体及相关数据的业务逻辑
     */
    private Map<String, Object> deleteSubjectData(List<Long> subjectIds) {
        Map<String, Object> result = new HashMap<String, Object>();
        int deletedSubjects = 0;
        int deletedBills = 0;
        int deletedDetails = 0;

        try {
            // 1. 删除结算单明细数据
            deletedDetails = billDetailService.deleteBySubjectIds(subjectIds);
            logger.info("删除结算单明细数据: {} 条", deletedDetails);

            // 2. 删除结算单主表数据
            deletedBills = billService.deleteBySubjectIds(subjectIds);
            logger.info("删除结算单主表数据: {} 条", deletedBills);

            // 3. 删除市场主体数据（只删除当前用户的）
            // 目前用户ID写死为1，后续可以从session或token中获取
            Long userId = 1L;
            deletedSubjects = subjectService.deleteByIdsAndUserId(subjectIds, userId);
            logger.info("删除市场主体数据: {} 条", deletedSubjects);

            result.put("success", true);
            result.put("message", "删除成功");
            result.put("deletedSubjects", deletedSubjects);
            result.put("deletedBills", deletedBills);
            result.put("deletedDetails", deletedDetails);
            result.put("totalDeleted", deletedSubjects + deletedBills + deletedDetails);

        } catch (Exception e) {
            logger.error("删除数据时发生异常", e);
            result.put("success", false);
            result.put("message", "删除失败: " + e.getMessage());
            result.put("deletedSubjects", deletedSubjects);
            result.put("deletedBills", deletedBills);
            result.put("deletedDetails", deletedDetails);
        }

        return result;
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(File tmpZip, File unzipDir) {
        try {
            if (tmpZip != null && tmpZip.exists()) {
                FileUtils.forceDelete(tmpZip);
                logger.info("已删除临时压缩文件: " + tmpZip.getName());
            }
            if (unzipDir != null && unzipDir.exists()) {
                FileUtils.deleteDirectory(unzipDir);
                logger.info("已删除临时解压目录: " + unzipDir.getName());
            }
        } catch (IOException e) {
            logger.warn("清理临时文件失败", e);
        }
    }

    /**
     * 结算单总览查询接口（多条件筛选+分页）
     * @param provinceId 省份ID
     * @param subjectIds 市场主体ID，逗号分隔
     * @param months 结算月份，逗号分隔
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @GetMapping("/list")
    public ResponseEntity<?> listBills(
            @RequestParam(value = "provinceId", required = false) Long provinceId,
            @RequestParam(value = "subjectIds", required = false) String subjectIds,
            @RequestParam(value = "months", required = false) String months,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize
    ) {
        // 组装条件
        List<Long> subjectIdList = new ArrayList<Long>();
        if (subjectIds != null && !"".equals(subjectIds.trim())) {
            for (String s : subjectIds.split(",")) subjectIdList.add(Long.valueOf(s));
        }
        List<String> monthList = new ArrayList<String>();
        if (months != null && !"".equals(months.trim())) {
            for (String m : months.split(",")) monthList.add(m);
        }
        // 目前用户ID写死为1，后续可以从session或token中获取
        Long userId = 1L;

        // 先获取当前用户的所有市场主体，确保权限控制
        List<TradeTjAllSettleSubject> userSubjectList = subjectService.getSubjectsByUserId(userId);
        if (userSubjectList.isEmpty()) {
            // 如果当前用户没有任何市场主体，直接返回空结果
            return ResponseEntity.ok(new PageResult<>(0, new ArrayList<Map<String, Object>>()));
        }

        // 构建当前用户的市场主体ID集合和名称映射
        Set<Long> userSubjectIds = new HashSet<Long>();
        Map<Long, String> subjectNameMap = new HashMap<Long, String>();
        for (TradeTjAllSettleSubject s : userSubjectList) {
            userSubjectIds.add(s.getId());
            subjectNameMap.put(s.getId(), s.getName());
        }

        // 获取所有结算单数据
        List<TradeTjAllSettleBill> all = billService.getAllBills();

        List<Map<String, Object>> filtered = new ArrayList<Map<String, Object>>();
        for (TradeTjAllSettleBill b : all) {
            // 首先检查权限：只显示当前用户的市场主体的结算单
            if (b.getSubjectId() == null || !userSubjectIds.contains(b.getSubjectId())) continue;

            // 然后应用其他过滤条件
            if (provinceId != null && !provinceId.equals(b.getProvinceId())) continue;
            if (!subjectIdList.isEmpty() && !subjectIdList.contains(b.getSubjectId())) continue;
            if (!monthList.isEmpty() && !monthList.contains(b.getMonth())) continue;
            Map<String, Object> map = new java.util.HashMap<String, Object>();
            map.put("id", b.getId());
            map.put("provinceId", b.getProvinceId());
            map.put("subjectId", b.getSubjectId());
            map.put("month", b.getMonth());
            map.put("createTime", b.getCreateTime());
            map.put("updateTime", b.getUpdateTime());
            map.put("onlineElectricity", b.getOnlineElectricity());
            map.put("settlementElectricity", b.getSettlementElectricity());
            map.put("contractElectricity", b.getContractElectricity());
            map.put("deviationElectricity", b.getDeviationElectricity());
            map.put("totalFee", b.getTotalFee());
            // 获取市场主体名称（由于已经过滤了权限，这里一定能找到对应的名称）
            String subjectName = subjectNameMap.get(b.getSubjectId());
            map.put("subjectName", subjectName != null ? subjectName : "");
            filtered.add(map);
        }
        int total = filtered.size();
        int from = Math.max(0, (pageNum-1)*pageSize);
        int to = Math.min(filtered.size(), from+pageSize);

        // 修复分页边界问题：当from大于等于total时，返回空列表
        List<Map<String, Object>> page;
        if (from >= total) {
            page = new ArrayList<Map<String, Object>>();
        } else {
            page = filtered.subList(from, to);
        }
        return ResponseEntity.ok(new PageResult<>(total, page));
    }

    /**
     * 分项标签列表接口
     * @param provinceId 省份ID（可选）
     * @return 分项标签列表
     */
    @GetMapping("/labels")
    public ResponseEntity<?> listLabels(@RequestParam(value = "provinceId", required = false) Long provinceId) {
        if (provinceId != null) {
            List<TradeTjAllSettleItemLabel> all = itemLabelService.getAllItemLabels();
            List<TradeTjAllSettleItemLabel> filtered = new ArrayList<TradeTjAllSettleItemLabel>();
            for (TradeTjAllSettleItemLabel l : all) {
                if (provinceId.equals(l.getProvinceId())) filtered.add(l);
            }
            return ResponseEntity.ok(filtered);
        } else {
            return ResponseEntity.ok(itemLabelService.getAllItemLabels());
        }
    }

    /**
     * 结算单明细标签数据接口
     * @param provinceId 省份ID
     * @param subjectIds 市场主体ID，逗号分隔
     * @param months 结算月份，逗号分隔
     * @param itemCodes 分项标签编码，逗号分隔（可选）
     * @return 明细数据，结构为{subjectId-month: {code: {...}}}
     */
    @GetMapping("/detail/summary")
    public ResponseEntity<?> detailSummary(@RequestParam("provinceId") Long provinceId,
                                           @RequestParam("subjectIds") String subjectIds,
                                           @RequestParam("months") String months,
                                           @RequestParam(value = "itemCodes", required = false) String itemCodes) {
        // 目前用户ID写死为1，后续可以从session或token中获取
        Long userId = 1L;

        // 先获取当前用户的所有市场主体，确保权限控制
        List<TradeTjAllSettleSubject> userSubjectList = subjectService.getSubjectsByUserId(userId);
        if (userSubjectList.isEmpty()) {
            // 如果当前用户没有任何市场主体，直接返回空结果
            return ResponseEntity.ok(new LinkedHashMap<String, Map<String, Map<String, Object>>>());
        }

        // 构建当前用户的市场主体ID集合
        Set<Long> userSubjectIds = new HashSet<Long>();
        for (TradeTjAllSettleSubject s : userSubjectList) {
            userSubjectIds.add(s.getId());
        }

        List<Long> subjectIdList = new ArrayList<Long>();
        for (String s : subjectIds.split(",")) {
            Long subjectId = Long.valueOf(s);
            // 只允许查询当前用户的市场主体
            if (userSubjectIds.contains(subjectId)) {
                subjectIdList.add(subjectId);
            }
        }

        // 如果过滤后没有有效的市场主体ID，返回空结果
        if (subjectIdList.isEmpty()) {
            return ResponseEntity.ok(new LinkedHashMap<String, Map<String, Map<String, Object>>>());
        }

        List<String> monthList = new ArrayList<String>();
        for (String m : months.split(",")) monthList.add(m);
        List<String> codeList = new ArrayList<String>();
        if (itemCodes != null && !itemCodes.trim().isEmpty()) {
            for (String c : itemCodes.split(",")) codeList.add(c);
        }
        // 批量查明细（现在只查询当前用户有权限的市场主体）
        List<TradeTjAllSettleBillDetail> allDetails = billDetailService.getByProvinceSubjectMonthBatch(provinceId, subjectIdList, monthList);
        // 结果结构：{subjectId-month: {code: {...}}}
        Map<String, Map<String, Map<String, Object>>> result = new LinkedHashMap<String, Map<String, Map<String, Object>>>();
        for (TradeTjAllSettleBillDetail d : allDetails) {
            if (!codeList.isEmpty() && !codeList.contains(d.getCode())) continue;
            String key = d.getSubjectId() + "-" + d.getMonth();
            if (!result.containsKey(key)) {
                result.put(key, new LinkedHashMap<String, Map<String, Object>>());
            }
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("code", d.getCode());
            map.put("name", d.getName());
            map.put("settlementElectricity", d.getSettlementElectricity());
            map.put("settlementFee", d.getSettlementFee());
            map.put("avgPrice", d.getSettlementElectricity() != null && d.getSettlementElectricity() != 0 ? d.getSettlementFee() / d.getSettlementElectricity() : null);
            result.get(key).put(d.getCode(), map);
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 结算总览导出接口（Excel）
     * @param response HttpServletResponse
     * @param provinceId 省份ID
     * @param subjectIds 市场主体ID，逗号分隔
     * @param months 结算月份，逗号分隔
     * @param itemCodes 分项标签编码，逗号分隔
     */
    @GetMapping("/export")
    public void exportBillOverview(HttpServletResponse response,
                                   @RequestParam(value = "provinceId", required = false) Long provinceId,
                                   @RequestParam(value = "subjectIds", required = false) String subjectIds,
                                   @RequestParam(value = "months", required = false) String months,
                                   @RequestParam(value = "itemCodes", required = false) String itemCodes
    ) throws Exception {
        // 组装条件
        List<Long> subjectIdList = new ArrayList<Long>();
        if (subjectIds != null && !"".equals(subjectIds.trim())) {
            for (String s : subjectIds.split(",")) subjectIdList.add(Long.valueOf(s));
        }
        List<String> monthList = new ArrayList<String>();
        if (months != null && !"".equals(months.trim())) {
            for (String m : months.split(",")) monthList.add(m);
        }
        List<String> codeList = new ArrayList<String>();
        if (itemCodes != null && !itemCodes.trim().isEmpty()) {
            for (String c : itemCodes.split(",")) codeList.add(c);
        }
        // 目前用户ID写死为1，后续可以从session或token中获取
        Long userId = 1L;

        // 先获取当前用户的所有市场主体，确保权限控制
        List<TradeTjAllSettleSubject> userSubjectList = subjectService.getSubjectsByUserId(userId);
        if (userSubjectList.isEmpty()) {
            // 如果当前用户没有任何市场主体，直接返回空的Excel
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("结算单数据.xlsx", "UTF-8"));
            Workbook emptyWb = new XSSFWorkbook();
            Sheet emptySheet = emptyWb.createSheet("结算总览");
            Row emptyRow = emptySheet.createRow(0);
            emptyRow.createCell(0).setCellValue("当前用户暂无结算单数据");
            emptyWb.write(response.getOutputStream());
            emptyWb.close();
            return;
        }

        // 构建当前用户的市场主体ID集合和名称映射
        Set<Long> userSubjectIds = new HashSet<Long>();
        Map<Long, String> subjectNameMap = new HashMap<Long, String>();
        for (TradeTjAllSettleSubject s : userSubjectList) {
            userSubjectIds.add(s.getId());
            subjectNameMap.put(s.getId(), s.getName());
        }

        // 查询主表
        List<TradeTjAllSettleBill> all = billService.getAllBills();

        // 过滤主表（加入权限控制）
        List<TradeTjAllSettleBill> filtered = new ArrayList<TradeTjAllSettleBill>();
        for (TradeTjAllSettleBill b : all) {
            // 首先检查权限：只导出当前用户的市场主体的结算单
            if (b.getSubjectId() == null || !userSubjectIds.contains(b.getSubjectId())) continue;

            // 然后应用其他过滤条件
            if (provinceId != null && !provinceId.equals(b.getProvinceId())) continue;
            if (!subjectIdList.isEmpty() && !subjectIdList.contains(b.getSubjectId())) continue;
            if (!monthList.isEmpty() && !monthList.contains(b.getMonth())) continue;
            filtered.add(b);
        }
        // 批量查询所有明细（优化：避免在for循环中单条查询）
        Map<String, Map<String, Object>> detailMap = new HashMap<String, Map<String, Object>>(); // key: subjectId-month-code
        if (!filtered.isEmpty()) {
            // 收集所有需要查询的subjectId和month
            Set<Long> allSubjectIdsForDetail = new HashSet<Long>();
            Set<String> allMonthsForDetail = new HashSet<String>();
            for (TradeTjAllSettleBill b : filtered) {
                allSubjectIdsForDetail.add(b.getSubjectId());
                allMonthsForDetail.add(b.getMonth());
            }
            // 批量查询明细
            List<TradeTjAllSettleBillDetail> allDetails = billDetailService.getByProvinceSubjectMonthBatch(
                provinceId, new ArrayList<Long>(allSubjectIdsForDetail), new ArrayList<String>(allMonthsForDetail));
            // 构建明细Map
            for (TradeTjAllSettleBillDetail d : allDetails) {
                if (!codeList.isEmpty() && !codeList.contains(d.getCode())) continue;
                String key = d.getSubjectId() + "-" + d.getMonth() + "-" + d.getCode();
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("code", d.getCode());
                map.put("name", d.getName());
                map.put("settlementElectricity", d.getSettlementElectricity());
                map.put("settlementFee", d.getSettlementFee());
                map.put("avgPrice", d.getSettlementElectricity() != null && d.getSettlementElectricity() != 0 ? d.getSettlementFee() / d.getSettlementElectricity() : null);
                detailMap.put(key, map);
            }
        }
        // 查询分项标签
        List<TradeTjAllSettleItemLabel> labelList = new ArrayList<TradeTjAllSettleItemLabel>();
        List<TradeTjAllSettleItemLabel> allLabels = itemLabelService.getAllItemLabels();
        if (!codeList.isEmpty()) {
            for (TradeTjAllSettleItemLabel l : allLabels) {
                if (codeList.contains(l.getCode()) && (provinceId == null || provinceId.equals(l.getProvinceId()))) {
                    labelList.add(l);
                }
            }
        } else {
            for (TradeTjAllSettleItemLabel l : allLabels) {
                if (provinceId == null || provinceId.equals(l.getProvinceId())) {
                    labelList.add(l);
                }
            }
        }
        // 只导出用户选择的分项标签（itemCodes），未选时不导出分项明细
        if (!codeList.isEmpty()) {
            List<TradeTjAllSettleItemLabel> temp = new ArrayList<TradeTjAllSettleItemLabel>();
            for (TradeTjAllSettleItemLabel l : labelList) {
                if (codeList.contains(l.getCode())) temp.add(l);
            }
            labelList = temp;
        }
        // 保证labelList中同code只保留一条（同省份下）
        Map<String, TradeTjAllSettleItemLabel> uniqueLabelMap = new LinkedHashMap<String, TradeTjAllSettleItemLabel>();
        for (TradeTjAllSettleItemLabel l : labelList) {
            if (!uniqueLabelMap.containsKey(l.getCode())) {
                uniqueLabelMap.put(l.getCode(), l);
            }
        }
        labelList = new ArrayList<TradeTjAllSettleItemLabel>(uniqueLabelMap.values());
        // 创建Excel
        Workbook wb = new XSSFWorkbook();
        // Excel样式
        CellStyle headStyle = wb.createCellStyle();
        Font headFont = wb.createFont();
        headFont.setBold(true);
        headFont.setFontHeightInPoints((short)11);
        headStyle.setFont(headFont);
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        CellStyle numStyle = wb.createCellStyle();
        numStyle.setAlignment(HorizontalAlignment.RIGHT);
        numStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        numStyle.setDataFormat(wb.createDataFormat().getFormat("#,##0.######"));

        CellStyle textStyle = wb.createCellStyle();
        textStyle.setAlignment(HorizontalAlignment.LEFT);
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Sheet sheet = wb.createSheet("结算总览");
        int rowIdx = 0;
        List<String> baseHeaders = new ArrayList<String>();
        baseHeaders.add("期间");
        baseHeaders.add("市场主体名称");
        baseHeaders.add("实际上网电量");
        baseHeaders.add("结算电费");
        baseHeaders.add("结算均价");
        // 表头1
        Row head1 = sheet.createRow(rowIdx++);
        int colIdx = 0;
        for (String h : baseHeaders) {
            Cell cell = head1.createCell(colIdx++);
            cell.setCellValue(h);
            cell.setCellStyle(headStyle);
        }
        // 只有选择了结算编码才导出分项明细表头
        if (!codeList.isEmpty()) {
            for (TradeTjAllSettleItemLabel l : labelList) {
                Cell cell = head1.createCell(colIdx++);
                cell.setCellValue(l.getCode() + " 结算电量");
                cell.setCellStyle(headStyle);
                cell = head1.createCell(colIdx++);
                cell.setCellValue(l.getCode() + " 结算电费");
                cell.setCellStyle(headStyle);
                cell = head1.createCell(colIdx++);
                cell.setCellValue(l.getCode() + " 结算均价");
                cell.setCellStyle(headStyle);
            }
        }
        // 数据行
        for (TradeTjAllSettleBill b : filtered) {
            Row row = sheet.createRow(rowIdx++);
            int c = 0;
            Cell cell0 = row.createCell(c++); cell0.setCellValue(b.getMonth()); cell0.setCellStyle(textStyle);
            String subjectNameForExcel = subjectNameMap.get(b.getSubjectId());
            Cell cell1 = row.createCell(c++); cell1.setCellValue(subjectNameForExcel != null ? subjectNameForExcel : ""); cell1.setCellStyle(textStyle);
            Cell cell2 = row.createCell(c++); cell2.setCellValue(b.getOnlineElectricity() != null ? b.getOnlineElectricity() : 0); cell2.setCellStyle(numStyle);
            Cell cell3 = row.createCell(c++); cell3.setCellValue(b.getTotalFee() != null ? b.getTotalFee() : 0); cell3.setCellStyle(numStyle);
            double avgPrice = (b.getOnlineElectricity() != null && b.getOnlineElectricity() != 0 && b.getTotalFee() != null) ? b.getTotalFee() / b.getOnlineElectricity() : 0;
            Cell cell4 = row.createCell(c++); cell4.setCellValue(String.format("%.2f", avgPrice)); cell4.setCellStyle(numStyle);
            // 只有选择了结算编码才导出分项明细数据
            if (!codeList.isEmpty()) {
                for (TradeTjAllSettleItemLabel l : labelList) {
                    String code = l.getCode();
                    String key = b.getSubjectId() + "-" + b.getMonth() + "-" + code;
                    Map<String, Object> d = detailMap.get(key);
                    Cell cellA = row.createCell(c++); cellA.setCellValue(d != null && d.get("settlementElectricity") != null ? d.get("settlementElectricity").toString() : "-"); cellA.setCellStyle(numStyle);
                    Cell cellB = row.createCell(c++); cellB.setCellValue(d != null && d.get("settlementFee") != null ? d.get("settlementFee").toString() : "-"); cellB.setCellStyle(numStyle);
                    String avgStr = "-";
                    if (d != null && d.get("avgPrice") != null) {
                        try {
                            avgStr = String.format("%.2f", Double.parseDouble(d.get("avgPrice").toString()));
                        } catch (Exception ignore) {}
                    }
                    Cell cellC = row.createCell(c++); cellC.setCellValue(avgStr); cellC.setCellStyle(numStyle);
                }
            }
        }
        // 列宽自适应
        for (int i = 0; i < head1.getLastCellNum(); i++) {
            sheet.autoSizeColumn(i);
            int width = sheet.getColumnWidth(i);
            sheet.setColumnWidth(i, Math.min(width + 1024, 10000));
        }
        // 响应下载
        String fileName = URLEncoder.encode("结算单数据.xlsx", "UTF-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        wb.write(response.getOutputStream());
        wb.close();
    }

    /**
     * 处理最新格式的单个市场主体数据（数组格式，header包含省份和月份信息）
     */
    private boolean processNewFormatCompanyData(JsonNode companyNode, File pdf, String subjectName,
                                              Map<String, TradeTjAllSettleSubject> subjectCache,
                                              List<TradeTjAllSettleBill> billList, List<TradeTjAllSettleBillDetail> detailList,
                                              List<TradeTjAllSettleItemLabel> labelList, Map<String, String> fileResult) {
        try {
            // 从最新格式中提取信息
            String company = companyNode.path("company").asText();
            String tradingUnitName = companyNode.path("tradingUnitName").asText();
            JsonNode headerNode = companyNode.path("header");

            // 从header中提取省份和月份信息
            String province = headerNode.path("province").asText();
            String month = headerNode.path("settlementDate").asText();

            // 验证省份信息
            if (province == null || province.trim().isEmpty()) {
                logger.error("PDF解析失败：省份信息为空，文件：" + pdf.getName());
                fileResult.put("status", "fail");
                fileResult.put("message", "省份信息为空");
                return false;
            }

            TradeTjProvinceInfo provinceInfo = provinceInfoService.selectByName(province.trim());
            if (provinceInfo == null) {
                logger.error("PDF解析失败：找不到省份信息，省份：" + province + "，文件：" + pdf.getName());
                fileResult.put("status", "fail");
                fileResult.put("message", "找不到省份信息：" + province);
                return false;
            }
            Long provinceId = provinceInfo.getId();

            // 从header中提取结算数据
            Double onlineElectricity = headerNode.path("onlineElectricity").asDouble();
            Double settlementElectricity = headerNode.path("settlementElectricity").asDouble();
            Double contractElectricity = headerNode.path("contractElectricity").asDouble();
            Double deviationElectricity = headerNode.path("deviationElectricity").asDouble();
            Double totalFee = headerNode.path("totalFee").asDouble();
            JsonNode details = companyNode.path("details");

            return processCompanyDataOld(company, tradingUnitName, month, provinceId,
                onlineElectricity, settlementElectricity, contractElectricity,
                deviationElectricity, totalFee, details, pdf, subjectCache,
                billList, detailList, labelList, fileResult);
        } catch (Exception e) {
            logger.error("处理最新格式市场主体数据失败，文件：" + pdf.getName(), e);
            fileResult.put("status", "fail");
            fileResult.put("message", "处理最新格式市场主体数据失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 处理中间格式的单个市场主体数据
     */
    private boolean processCompanyData(JsonNode companyNode, String province, String month, Long provinceId,
                                     File pdf, String subjectName, Map<String, TradeTjAllSettleSubject> subjectCache,
                                     List<TradeTjAllSettleBill> billList, List<TradeTjAllSettleBillDetail> detailList,
                                     List<TradeTjAllSettleItemLabel> labelList, Map<String, String> fileResult) {
        try {
            // 从新格式中提取市场主体信息
            String company = companyNode.path("company").asText();
            String tradingUnitName = companyNode.path("tradingUnitName").asText();
            Double onlineElectricity = companyNode.path("onlineElectricity").asDouble();
            Double settlementElectricity = companyNode.path("settlementElectricity").asDouble();
            Double contractElectricity = companyNode.path("contractElectricity").asDouble();
            Double deviationElectricity = companyNode.path("deviationElectricity").asDouble();
            Double totalFee = companyNode.path("totalFee").asDouble();
            JsonNode details = companyNode.path("details");

            return processCompanyDataOld(company, tradingUnitName, month, provinceId,
                onlineElectricity, settlementElectricity, contractElectricity,
                deviationElectricity, totalFee, details, pdf, subjectCache,
                billList, detailList, labelList, fileResult);
        } catch (Exception e) {
            logger.error("处理市场主体数据失败，文件：" + pdf.getName(), e);
            fileResult.put("status", "fail");
            fileResult.put("message", "处理市场主体数据失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 处理市场主体数据的通用逻辑（兼容新旧格式）
     */
    private boolean processCompanyDataOld(String company, String tradingUnitName, String month, Long provinceId,
                                        Double onlineElectricity, Double settlementElectricity, Double contractElectricity,
                                        Double deviationElectricity, Double totalFee, JsonNode details, File pdf,
                                        Map<String, TradeTjAllSettleSubject> subjectCache, List<TradeTjAllSettleBill> billList,
                                        List<TradeTjAllSettleBillDetail> detailList, List<TradeTjAllSettleItemLabel> labelList,
                                        Map<String, String> fileResult) {
        try {
            // 构建市场主体名称：company + "-" + tradingUnitName
            String subjectName;
            if (tradingUnitName != null && !tradingUnitName.trim().isEmpty()) {
                subjectName = company.trim() + "-" + tradingUnitName.trim();
            } else {
                subjectName = company.trim();
            }

            // 验证市场主体名称
            if (subjectName.isEmpty()) {
                logger.error("PDF解析失败：市场主体名称为空，文件：" + pdf.getName());
                fileResult.put("status", "fail");
                fileResult.put("message", "市场主体名称为空");
                return false;
            }

            // 优化市场主体查询逻辑，避免重复查询
            String subjectKey = subjectName + "-" + provinceId;
            TradeTjAllSettleSubject subject = subjectCache.get(subjectKey);
            if (subject == null) {
                synchronized (subjectCache) {
                    subject = subjectCache.get(subjectKey);
                    if (subject == null) {
                        // 目前用户ID写死为1，后续可以从session或token中获取
                        Long userId = 1L;
                        subject = subjectService.getByNameProvinceAndUserId(subjectName, provinceId, userId);
                        if (subject == null) {
                            // 创建新的市场主体
                            TradeTjAllSettleSubject newSubject = new TradeTjAllSettleSubject();
                            newSubject.setName(subjectName);
                            newSubject.setProvinceId(provinceId);
                            newSubject.setUserId(1L);
                            subjectService.addSubject(newSubject);
                            // 重新查询获取带ID的对象
                            subject = subjectService.getByNameProvinceAndUserId(subjectName, provinceId, 1L);
                            if (subject == null) {
                                logger.error("PDF解析失败：创建市场主体后查询失败，市场主体：" + subjectName + "，文件：" + pdf.getName());
                                fileResult.put("status", "fail");
                                fileResult.put("message", "创建市场主体失败");
                                return false;
                            }
                        }
                        subjectCache.put(subjectKey, subject);
                    }
                }
            }

            // 创建结算单记录
            TradeTjAllSettleBill bill = new TradeTjAllSettleBill();
            bill.setProvinceId(provinceId);
            bill.setSubjectId(subject.getId());
            bill.setMonth(month);
            bill.setOnlineElectricity(onlineElectricity);
            bill.setSettlementElectricity(settlementElectricity);
            bill.setContractElectricity(contractElectricity);
            bill.setDeviationElectricity(deviationElectricity);
            bill.setTotalFee(totalFee);
            billList.add(bill);

            // 处理明细数据
            if (details != null && details.isArray()) {
                for (JsonNode d : details) {
                    String rawCode = d.path("code").asText();
                    // 去掉所有空格
                    String code = rawCode != null ? rawCode.replaceAll("\\s+", "") : "";
                    // 跳过无效的code
                    if (code.isEmpty() || "0".equals(code)) {
                        continue;
                    }

                    // 创建分项标签
                    TradeTjAllSettleItemLabel label = new TradeTjAllSettleItemLabel();
                    label.setProvinceId(provinceId);
                    label.setCode(code);
                    label.setName(d.path("name").asText());
                    labelList.add(label);

                    // 创建明细记录
                    TradeTjAllSettleBillDetail detail = new TradeTjAllSettleBillDetail();
                    detail.setProvinceId(provinceId);
                    detail.setSubjectId(subject.getId());
                    detail.setMonth(month);
                    detail.setCode(code);
                    detail.setName(d.path("name").asText());
                    detail.setPlannedElectricity(d.path("plannedElectricity").asDouble());
                    detail.setSettlementElectricity(d.path("settlementElectricity").asDouble());
                    detail.setSettlementPrice(d.path("settlementPrice").asDouble());
                    detail.setSettlementFee(d.path("settlementFee").asDouble());
                    detailList.add(detail);
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("处理市场主体数据失败，文件：" + pdf.getName(), e);
            fileResult.put("status", "fail");
            fileResult.put("message", "处理市场主体数据失败: " + e.getMessage());
            return false;
        }
    }

    // 分页结果包装类
    public static class PageResult<T> {
        public int total;
        public List<T> data;
        public PageResult(int total, List<T> data) {
            this.total = total;
            this.data = data;
        }
    }
}