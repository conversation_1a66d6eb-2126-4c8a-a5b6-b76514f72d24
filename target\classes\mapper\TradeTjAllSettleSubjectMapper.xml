<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.settle.dao.TradeTjAllSettleSubjectMapper">
    <resultMap id="BaseResultMap" type="com.sprixin.settle.entity.TradeTjAllSettleSubject">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="provinceId" column="province_id" />
        <result property="userId" column="user_id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <insert id="insert" parameterType="com.sprixin.settle.entity.TradeTjAllSettleSubject">
        INSERT INTO trade_tj_all_settle_subject (name, province_id, user_id, create_time, update_time)
        VALUES (#{name}, #{provinceId}, #{userId}, NOW(), NOW())
    </insert>



    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_all_settle_subject WHERE id=#{id}
    </select>

    <update id="update" parameterType="com.sprixin.settle.entity.TradeTjAllSettleSubject">
        UPDATE trade_tj_all_settle_subject
        SET name=#{name}, province_id=#{provinceId}, user_id=#{userId}, update_time=NOW()
        WHERE id=#{id}
    </update>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_all_settle_subject
    </select>

    <select id="selectListByUserId" parameterType="long" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_all_settle_subject WHERE user_id = #{userId}
    </select>

    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM trade_tj_all_settle_subject
        WHERE id IN
        <foreach collection="subjectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByIdsAndUserId">
        DELETE FROM trade_tj_all_settle_subject
        WHERE user_id = #{userId} AND id IN
        <foreach collection="subjectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>