<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.settle.dao.TradeTjAllSettleBillMapper">
    <resultMap id="BaseResultMap" type="com.sprixin.settle.entity.TradeTjAllSettleBill">
        <id property="id" column="id" />
        <result property="provinceId" column="province_id" />
        <result property="subjectId" column="subject_id" />
        <result property="month" column="month" />
        <result property="onlineElectricity" column="online_electricity" />
        <result property="settlementElectricity" column="settlement_electricity" />
        <result property="contractElectricity" column="contract_electricity" />
        <result property="deviationElectricity" column="deviation_electricity" />
        <result property="totalFee" column="total_fee" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_all_settle_bill
    </select>



    <insert id="replaceBatch" parameterType="java.util.List">
        REPLACE INTO trade_tj_all_settle_bill (province_id, subject_id, month, online_electricity, settlement_electricity, contract_electricity, deviation_electricity, total_fee, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.provinceId}, #{item.subjectId}, #{item.month}, #{item.onlineElectricity}, #{item.settlementElectricity}, #{item.contractElectricity}, #{item.deviationElectricity}, #{item.totalFee}, NOW(), NOW())
        </foreach>
    </insert>

    <delete id="deleteBySubjectIds" parameterType="java.util.List">
        DELETE FROM trade_tj_all_settle_bill
        WHERE subject_id IN
        <foreach collection="subjectIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>