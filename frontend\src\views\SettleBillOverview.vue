<template>
  <div style="height: 100vh; background: #f7f8fa;">
    <div style="max-width: 1600px; margin: 0 auto; height: 100vh; display: flex; flex-direction: column;">
      <!-- 顶部查询与导入操作区 -->
      <div style="padding: 16px 0 0 0; background: #fff; box-shadow: 0 2px 8px #f0f1f2; z-index: 10;">
        <!-- 第一行：查询条件 -->
        <div style="padding: 0 24px 12px 24px;">
          <el-form :inline="true" :model="filters" class="filter-form">
            <el-form-item label="省份">
              <el-select v-model="filters.provinceId" placeholder="请选择省份" clearable @change="onProvinceChange" style="width: 140px;">
                <el-option v-for="p in provinceList" :key="p.id" :label="p.name" :value="p.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="市场主体">
              <el-select v-model="filters.subjectIds" multiple collapse-tags collapse-tags-tooltip placeholder="请选择市场主体" clearable @change="onSubjectChange" style="width: 200px;">
                <el-option v-for="s in subjectList" :key="s.id" :label="s.name" :value="s.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="月份">
              <el-date-picker
                v-model="filters.monthRange"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                value-format="YYYY-MM"
                format="YYYY-MM"
                style="width: 280px;"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="fetchList">查询</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 第二行：操作按钮 -->
        <div style="padding: 0 24px 16px 24px; border-top: 1px solid #f0f0f0;">
          <el-form :inline="true" style="margin-top: 12px;">
            <el-form-item>
              <el-button type="primary" @click="importDialogVisible = true" size="small">导入结算单</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="handleExport" :loading="exporting" size="small">导出</el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="danger" @click="handleDelete" :disabled="!filters.subjectIds || filters.subjectIds.length === 0" size="small">删除选中</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 内容区：左右结构 -->
      <div style="flex: 1; min-height: 0; display: flex; flex-direction: row;">
        <!-- 左侧分项标签 -->
        <div style="width: 280px; min-width: 280px; max-width: 280px; display: flex; flex-direction: column; border-right: 1px solid #e4e7ed;">
          <!-- 固定标题区域 -->
          <div style="padding: 16px 16px 12px 16px; border-bottom: 1px solid #f0f0f0; background: #fafafa;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="display: flex; align-items: center; font-weight: bold; font-size: 14px; color: #303133;">
                <el-icon style="margin-right: 8px; color: #409eff;"><Grid /></el-icon>
                分项标签
              </div>
              <div style="display: flex; gap: 8px;">
                <el-button size="small" type="text" @click="selectAllLabels" style="padding: 2px 4px; font-size: 12px;">
                  全选
                </el-button>
                <el-button size="small" type="text" @click="clearAllLabels" style="padding: 2px 4px; font-size: 12px;">
                  清空
                </el-button>
              </div>
            </div>
            <div style="font-size: 12px; color: #909399; margin-top: 4px;">
              选择要显示的分项数据
            </div>
          </div>

          <!-- 可滚动的标签列表区域 -->
          <div class="label-list-container" style="flex: 1; overflow-y: auto; padding: 12px 16px;">
            <el-checkbox-group v-model="selectedLabels" style="display: flex; flex-direction: column;">
              <el-checkbox
                v-for="item in labelList"
                :key="item.code"
                :value="item.code"
                style="margin-bottom: 12px; margin-right: 0; width: 100%;"
                class="label-checkbox"
              >
                <div style="width: 100%; min-width: 0; line-height: 1.4;">
                  <div style="font-weight: 500; color: #606266; margin-bottom: 3px; font-size: 13px;">{{ item.code }}</div>
                  <div
                    style="color: #909399; font-size: 11px; line-height: 1.4; word-wrap: break-word; overflow-wrap: break-word;"
                    :title="item.name"
                  >
                    {{ item.name }}
                  </div>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <!-- 右侧表格和分页 -->
        <div style="flex: 1; min-width: 0; padding: 16px 24px 24px 16px; display: flex; flex-direction: column;">
          <!-- 表格容器，支持水平滚动 -->
          <div style="flex: 1; overflow-x: auto; overflow-y: auto;">
          <el-table
            :data="tableData"
            :key="selectedLabels.join(',')"
            :style="{ width: '100%', minWidth: getTableMinWidth() + 'px', marginTop: 0 }"
            @sort-change="handleSortChange"
          >
            <el-table-column prop="month" label="期间" sortable="custom" min-width="120" />
            <el-table-column prop="subjectName" label="市场主体名称" width="320" min-width="320">
              <template #default="scope">
                <div v-if="editingSubjectId === scope.row.subjectId && editingRowKey === getRowKey(scope.row)" style="display: flex; align-items: center; gap: 8px;">
                  <el-input
                    v-model="editingSubjectName"
                    size="small"
                    @keyup.enter="saveSubjectName(scope.row)"
                    @keyup.esc="cancelEdit"
                    @blur="handleInputBlur"
                    ref="editInput"
                    style="flex: 1; min-width: 200px;"
                    placeholder="请输入市场主体名称"
                  />
                  <el-button size="small" type="primary" @click="saveSubjectName(scope.row)" :loading="savingSubjectName">
                    <el-icon><Check /></el-icon>
                  </el-button>
                  <el-button size="small" @click="cancelEdit">
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
                <div v-else style="display: flex; align-items: center; justify-content: space-between;">
                  <span style="flex: 1; word-break: break-all;">{{ scope.row.subjectName }}</span>
                  <el-button
                    size="small"
                    type="text"
                    @click="startEdit(scope.row)"
                    style="margin-left: 8px; opacity: 0.6; flex-shrink: 0;"
                    title="点击编辑市场主体名称"
                  >
                    <el-icon><Edit /></el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="onlineElectricity" label="实际上网电量" min-width="140" />
            <el-table-column prop="totalFee" label="结算电费" min-width="120" />
            <el-table-column prop="avgPrice" label="结算均价" sortable="custom" min-width="120">
              <template #default="scope">
                <span>{{ scope.row.avgPrice !== undefined ? scope.row.avgPrice.toFixed(2) : '-' }}</span>
              </template>
            </el-table-column>
            <template v-for="code in selectedLabels" :key="`label-${code}`">
              <el-table-column :label="getLabelLabel(code)" align="center" min-width="300">
                <el-table-column :label="'结算电量'" align="center" min-width="100">
                  <template #default="scope">
                    <span>{{ scope.row.details && scope.row.details[code] ? scope.row.details[code].settlementElectricity : '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="'结算电费'" align="center" min-width="100">
                  <template #default="scope">
                    <span>{{ scope.row.details && scope.row.details[code] ? scope.row.details[code].settlementFee : '-' }}</span>
                  </template>
                </el-table-column>
                <el-table-column :label="'结算均价'" align="center" min-width="100">
                  <template #default="scope">
                    <span>{{ scope.row.details && scope.row.details[code] ? (scope.row.details[code].avgPrice != null ? scope.row.details[code].avgPrice.toFixed(2) : '-') : '-' }}</span>
                  </template>
                </el-table-column>
              </el-table-column>
            </template>
          </el-table>
          </div>
          <!-- 分页区域 -->
          <div style="padding-top: 16px;">
            <el-pagination
              v-model:current-page="pageNum"
              v-model:page-size="pageSize"
              :total="total"
              @current-change="fetchList"
              @size-change="fetchList"
              layout="total, prev, pager, next, sizes"
            />
          </div>
        </div>
        <el-dialog v-model="importDialogVisible" title="批量导入" width="500px" :close-on-click-modal="false">
          <div style="text-align:center; margin-bottom: 16px; color: #888;">批量导入(请把所有文件放入一个压缩包)</div>
          <el-form :model="importForm" label-width="120px" style="margin-bottom: 16px;">
            <el-form-item label="市场主体名称">
              <el-input v-model="importForm.subjectName" placeholder="请慎填，填写后，导入的所有结算单都为该市场主体所有！" clearable />
            </el-form-item>
          </el-form>
          <el-upload
            class="upload-demo"
            drag
            action=""
            :http-request="handleImport"
            accept=".zip"
            :show-file-list="false"
            :on-error="onUploadError"
            style="width: 100%; display: flex; justify-content: center;"
          >
            <i class="el-icon-upload" style="font-size: 48px; color: #409EFF; margin-bottom: 8px;"></i>
            <div class="el-upload__text">将压缩包拖到此处，或 <em>点击上传</em></div>
          </el-upload>
          <div style="text-align:center; margin-top: 24px;">
            <el-button type="primary" @click="confirmImport" :loading="importing">确认导入</el-button>
          </div>
        </el-dialog>
        <el-dialog v-model="importResultDialogVisible" title="导入结果" width="600px" :close-on-click-modal="false">
          <div v-if="importResult">
            <div style="margin-bottom: 12px;">
              <span>总数：</span><b>{{ importResult.totalCount }}</b>
              <span style="margin-left: 24px;">成功：</span><b style="color: #52c41a">{{ importResult.successCount }}</b>
              <span style="margin-left: 24px;">失败：</span><b style="color: #f5222d">{{ importResult.failedCount }}</b>
            </div>
            <el-table :data="importResult.fileResults" border size="small" style="width: 100%">
              <el-table-column prop="fileName" label="文件名" />
              <el-table-column prop="status" label="状态">
                <template #default="scope">
                  <span :style="scope.row.status === 'fail' ? 'color:#f5222d' : 'color:#52c41a'">
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="message" label="备注">
                <template #default="scope">
                  <span :style="scope.row.status === 'fail' ? 'color:#f5222d' : ''">{{ scope.row.message }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <template #footer>
            <el-button type="primary" @click="importResultDialogVisible = false">关闭</el-button>
          </template>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Check, Close, Grid } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
const router = useRouter()

const filters = ref({
  provinceId: null,
  subjectIds: [],
  months: [], // 兼容旧逻辑，实际用 monthRange
  monthRange: [] // 新增：月份范围
})
const provinceList = ref([])
const subjectList = ref([])
const billList = ref([])
const total = ref(0)

// 编辑市场主体名称相关
const editingSubjectId = ref(null)
const editingRowKey = ref(null) // 用于唯一标识正在编辑的行
const editingSubjectName = ref('')
const savingSubjectName = ref(false)
const editInput = ref(null)
const isSaving = ref(false) // 防止blur和save冲突
const pageNum = ref(1)
const pageSize = ref(20)

const importDialogVisible = ref(false)
const importing = ref(false)
let uploadFile = null
const importResult = ref(null)
const importResultDialogVisible = ref(false)

const exporting = ref(false)

const importForm = ref({ subjectName: '' })

const labelList = ref([])
const selectedLabels = ref([])
const tableData = ref([])

const getLabelName = (code) => {
  const found = labelList.value.find(l => l.code === code)
  return found ? found.name : code
}

const getLabelLabel = (code) => {
  const found = labelList.value.find(l => l.code === code)
  return found ? (found.code + ' ' + found.name) : code
}

const fetchLabels = async () => {
  if (!filters.value.provinceId) {
    labelList.value = []
    return
  }
  const { data } = await axios.get('/api/settle/bill/labels', { params: { provinceId: filters.value.provinceId } })
  labelList.value = data
}

// 生成区间内所有月份
function getMonthRangeArr() {
  const [start, end] = filters.value.monthRange || []
  if (!start || !end) return []
  const arr = []
  let cur = dayjs(start)
  const endMonth = dayjs(end)
  while (cur.isBefore(endMonth) || cur.isSame(endMonth, 'month')) {
    arr.push(cur.format('YYYY-MM'))
    cur = cur.add(1, 'month')
  }
  return arr
}

const fetchList = async () => {
  const params = {
    provinceId: filters.value.provinceId,
    subjectIds: filters.value.subjectIds.join(','),
    months: getMonthRangeArr().join(','),
    pageNum: pageNum.value,
    pageSize: pageSize.value
  }
  const { data } = await axios.get('/api/settle/bill/list', { params })
  // 主表数据
  billList.value = data.data
  total.value = data.total
  // 动态明细
  console.log('当前选中的分项标签:', selectedLabels.value);
  if (selectedLabels.value.length > 0) {
    console.log('开始请求分项明细数据...');
    // 批量请求所有主体和月份组合的明细
    const promises = billList.value.map(row =>
      axios.get('/api/settle/bill/detail/summary', {
        params: {
          provinceId: row.provinceId,
          subjectIds: row.subjectId + '', // 保证为字符串
          months: row.month + '',         // 保证为字符串
          itemCodes: selectedLabels.value.join(',')
        }
      })
    )
    const allDetails = await Promise.all(promises)
    console.log('分项明细数据请求完成:', allDetails);
    tableData.value = billList.value.map((row, idx) => ({
      ...row,
      avgPrice: row.onlineElectricity && row.onlineElectricity !== 0 ? row.totalFee / row.onlineElectricity : 0,
      details: allDetails[idx].data[row.subjectId + '-' + row.month] || {}
    }))
  } else {
    console.log('没有选中分项标签，只显示基础数据');
    tableData.value = billList.value.map(row => ({
      ...row,
      avgPrice: row.onlineElectricity && row.onlineElectricity !== 0 ? row.totalFee / row.onlineElectricity : 0
    }))
  }
  console.log('最终表格数据:', tableData.value);
}

watch(selectedLabels, (newLabels, oldLabels) => {
  console.log('分项标签变化:', { newLabels, oldLabels });
  fetchList();
})

const handleImport = async (option) => {
  importing.value = true
  const formData = new FormData()
  formData.append('file', option.file)
  if (importForm.value.subjectName) {
    formData.append('subjectName', importForm.value.subjectName)
  }
  try {
    const { data } = await axios.post('/api/settle/bill/import', formData)
    importResult.value = data
    importDialogVisible.value = false
    importResultDialogVisible.value = true
    fetchList()
  } catch (e) {
    onUploadError()
  } finally {
    importing.value = false
  }
}

const confirmImport = () => {
  document.querySelector('.upload-demo input[type=file]').click()
}

// 已移除goToDetail跳转功能

const handleExport = async () => {
  exporting.value = true
  try {
    const params = {
      provinceId: filters.value.provinceId,
      subjectIds: filters.value.subjectIds.join(','),
      months: getMonthRangeArr().join(','),
      itemCodes: selectedLabels.value.join(',')
    }
    const res = await axios.get('/api/settle/bill/export', {
      params,
      responseType: 'blob'
    })
    const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '结算总览导出.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (e) {
    ElMessage.error('导出失败，请重试！')
  } finally {
    exporting.value = false
  }
}

const onProvinceChange = async (val) => {
  if (val) {
    // 省份变化，请求该省份下市场主体
    const { data } = await axios.get('/api/province/subject/list', { params: { provinceId: val } })
    subjectList.value = data
  } else {
    subjectList.value = []
  }
  filters.value.subjectIds = []
  filters.value.monthRange = []
  // 省份变化时自动刷新分项标签
  fetchLabels()
  selectedLabels.value = []
}
// 结算月份不再根据市场主体动态查询，直接多选即可

// 已移除期间字段合并功能，避免与排序功能冲突

// 删除选中的市场主体及相关数据
const handleDelete = async () => {
  if (!filters.value.subjectIds || filters.value.subjectIds.length === 0) {
    ElMessage.warning('请先选择要删除的市场主体');
    return;
  }

  // 获取选中的市场主体名称用于确认
  const selectedSubjectNames = subjectList.value
    .filter(s => filters.value.subjectIds.includes(s.id))
    .map(s => s.name)
    .join('、');

  try {
    await ElMessageBox.confirm(
      `确定要删除以下市场主体及其所有相关数据吗？\n\n${selectedSubjectNames}\n\n此操作将同时删除：\n• 市场主体信息\n• 结算单总览数据\n• 结算单详情数据\n\n删除后无法恢复！`,
      '危险操作确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    // 执行删除
    const subjectIds = filters.value.subjectIds.join(',');
    const { data } = await axios.post('/api/settle/bill/delete', null, {
      params: { subjectIds }
    });

    if (data.success) {
      ElMessage.success(`删除成功！共删除 ${data.totalDeleted} 条记录`);

      // 清空选择并刷新数据
      filters.value.subjectIds = [];
      await onProvinceChange(filters.value.provinceId);
      await fetchList();
    } else {
      ElMessage.error(data.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败: ' + (error.response?.data || error.message));
    }
  }
};

// 当前排序状态
const currentSort = ref({ prop: null, order: null });

// 排序事件处理
const handleSortChange = (sort) => {
  if (!sort.prop || !sort.order) return;

  // 更新当前排序状态
  currentSort.value = { prop: sort.prop, order: sort.order };

  tableData.value = [...tableData.value].sort((a, b) => {
    // 主排序字段
    let primaryResult = 0;

    if (sort.prop === 'avgPrice') {
      primaryResult = sort.order === 'ascending' ? a.avgPrice - b.avgPrice : b.avgPrice - a.avgPrice;
    } else if (sort.prop === 'month') {
      // 期间字段按时间顺序排序
      const dateA = new Date(a.month + '-01');
      const dateB = new Date(b.month + '-01');
      primaryResult = sort.order === 'ascending' ? dateA - dateB : dateB - dateA;
    } else if (typeof a[sort.prop] === 'number' && typeof b[sort.prop] === 'number') {
      primaryResult = sort.order === 'ascending' ? a[sort.prop] - b[sort.prop] : b[sort.prop] - a[sort.prop];
    } else if (typeof a[sort.prop] === 'string' && typeof b[sort.prop] === 'string') {
      primaryResult = sort.order === 'ascending' ? a[sort.prop].localeCompare(b[sort.prop]) : b[sort.prop].localeCompare(a[sort.prop]);
    }

    // 如果主排序字段相等，则使用期间作为次要排序字段（保持时间顺序）
    if (primaryResult === 0 && sort.prop !== 'month') {
      const dateA = new Date(a.month + '-01');
      const dateB = new Date(b.month + '-01');
      // 次要排序始终按时间升序排列，保持期间的自然顺序
      return dateA - dateB;
    }

    return primaryResult;
  });
};

// 生成行的唯一标识
const getRowKey = (row) => {
  return `${row.subjectId}_${row.month}`;
};

// 开始编辑市场主体名称
const startEdit = (row) => {
  console.log('开始编辑:', row);
  editingSubjectId.value = row.subjectId;
  editingRowKey.value = getRowKey(row);
  editingSubjectName.value = row.subjectName;
  nextTick(() => {
    if (editInput.value) {
      editInput.value.focus();
      editInput.value.select();
    }
  });
};

// 取消编辑
const cancelEdit = () => {
  if (!isSaving.value) {
    editingSubjectId.value = null;
    editingRowKey.value = null;
    editingSubjectName.value = '';
  }
};

// 处理输入框失去焦点
const handleInputBlur = () => {
  // 延迟执行，给保存按钮点击事件时间执行
  setTimeout(() => {
    if (!isSaving.value) {
      cancelEdit();
    }
  }, 200);
};

// 保存市场主体名称
const saveSubjectName = async (row) => {
  console.log('开始保存市场主体名称:', {
    subjectId: row.subjectId,
    oldName: row.subjectName,
    newName: editingSubjectName.value
  });

  if (!editingSubjectName.value || editingSubjectName.value.trim() === '') {
    ElMessage.warning('市场主体名称不能为空');
    return;
  }

  if (editingSubjectName.value.trim() === row.subjectName) {
    // 名称没有变化，直接取消编辑
    console.log('名称没有变化，取消编辑');
    cancelEdit();
    return;
  }

  try {
    isSaving.value = true;
    savingSubjectName.value = true;
    console.log('发送请求到后台...');

    const { data } = await axios.post('/api/settle/bill/updateSubjectName', null, {
      params: {
        subjectId: row.subjectId,
        newName: editingSubjectName.value.trim()
      }
    });

    console.log('后台响应:', data);

    if (data.success) {
      ElMessage.success('市场主体名称修改成功');

      // 更新表格数据中的名称 - 更新所有该市场主体的记录
      tableData.value.forEach(item => {
        if (item.subjectId === row.subjectId) {
          item.subjectName = editingSubjectName.value.trim();
        }
      });

      // 更新市场主体列表中的名称
      const targetSubject = subjectList.value.find(item => item.id === row.subjectId);
      if (targetSubject) {
        targetSubject.name = editingSubjectName.value.trim();
      }

      // 重置编辑状态
      editingSubjectId.value = null;
      editingRowKey.value = null;
      editingSubjectName.value = '';
    } else {
      ElMessage.error(data.message || '修改失败');
    }
  } catch (error) {
    console.error('修改市场主体名称失败:', error);
    ElMessage.error('修改失败: ' + (error.response?.data || error.message));
  } finally {
    isSaving.value = false;
    savingSubjectName.value = false;
  }
};

// 分项标签全选和清空
const selectAllLabels = () => {
  selectedLabels.value = labelList.value.map(item => item.code);
};

const clearAllLabels = () => {
  selectedLabels.value = [];
};

// 计算表格最小宽度
const getTableMinWidth = () => {
  // 基础列宽度：期间(120) + 市场主体名称(320) + 实际上网电量(140) + 结算电费(120) + 结算均价(120) = 820
  const baseWidth = 820;
  // 每个分项标签列宽度：300px (包含3个子列：结算电量100 + 结算电费100 + 结算均价100)
  const labelColumnWidth = 300;
  // 计算总宽度
  const totalWidth = baseWidth + (selectedLabels.value.length * labelColumnWidth);
  // 最小宽度不少于1400px
  return Math.max(totalWidth, 1400);
};

const leftSidebar = ref(null)
const tableArea = ref(null)
const tableAreaHeight = ref(0)

// 同步左侧栏高度为表格区实际高度
const syncSidebarHeight = () => {
  nextTick(() => {
    if (tableArea.value) {
      tableAreaHeight.value = tableArea.value.offsetHeight
    }
  })
}

onMounted(() => {
  axios.get('/api/province/list').then(async res => {
    provinceList.value = res.data
    // 默认选中山西省份（假设名称为“山西”）
    const sx = res.data.find(p => p.name.includes('山西'))
    if (sx) {
      filters.value.provinceId = sx.id
      // 查询市场主体
      const subjectRes = await axios.get('/api/province/subject/list', { params: { provinceId: sx.id } })
      subjectList.value = subjectRes.data
      await fetchLabels()
      await fetchList()
    }
  })
  syncSidebarHeight()
  window.addEventListener('resize', syncSidebarHeight)
})

watch([tableData, importDialogVisible], syncSidebarHeight)
</script>

<style scoped>
/* 分项标签复选框样式优化 */
.label-checkbox {
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  width: 100%;
  box-sizing: border-box;
}

.label-checkbox:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
}

.label-checkbox.is-checked {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
}

/* 确保复选框内容不溢出 */
.label-checkbox :deep(.el-checkbox__label) {
  width: 100%;
  min-width: 0;
  overflow: hidden;
}

/* 复选框本身的样式 */
.label-checkbox :deep(.el-checkbox__input) {
  flex-shrink: 0;
  margin-right: 8px;
}

/* 滚动条样式优化 */
.label-checkbox::-webkit-scrollbar {
  width: 6px;
}

.label-checkbox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.label-checkbox::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.label-checkbox::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 分项标签区域滚动条 */
.label-list-container::-webkit-scrollbar {
  width: 6px;
}

.label-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.label-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.label-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>