<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.settle.dao.TradeTjProvinceInfoMapper">
    <resultMap id="BaseResultMap" type="com.sprixin.settle.entity.TradeTjProvinceInfo">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="nameEn" column="name_en" />
    </resultMap>

    <select id="selectByName" parameterType="string" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_provinceinfo WHERE name = #{name}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_provinceinfo
    </select>
</mapper> 