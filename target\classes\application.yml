server:
  port: 8080

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************
    username: sa
    password: cast1234
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.sprixin.settle.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

logging:
  level:
    com.sprixin.settle: debug
    org.springframework: warn

ai:
  model:
    url: ${AI_MODEL_URL:http://************:8000}
    key: ${AI_MODEL_KEY:sk-1234567890}
    name: ${AI_MODEL_NAME:/data/models/Qwen/Qwen3-30B-A3B-Thinking-2507}
    timeout: ${AI_MODEL_TIMEOUT:30000}

# 内存优化配置
settle:
  upload:
    tmpdir: ${SETTLE_UPLOAD_TMPDIR:./tmp}
  memory:
    # PDF处理时的内存优化
    pdf-processing-batch-size: ${PDF_BATCH_SIZE:10}
    # 强制GC间隔（毫秒）
    gc-interval: ${GC_INTERVAL:60000}