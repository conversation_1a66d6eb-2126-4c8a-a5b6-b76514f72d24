#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=28188, tid=26612
#
# JRE version:  (21.0.8+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-05805b35c368a5bf459667dba4f27621-sock

Host: AMD Ryzen 5 4500U with Radeon Graphics         , 6 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Aug  4 19:40:41 2025 elapsed time: 0.028124 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001498c1fb2f0):  JavaThread "Unknown thread" [_thread_in_vm, id=26612, stack(0x00000048e6f00000,0x00000048e7000000) (1024K)]

Stack: [0x00000048e6f00000,0x00000048e7000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d2449]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001498c2442a0, length=1, elements={
0x000001498c1fb2f0
}

Java Threads: ( => current thread )
=>0x000001498c1fb2f0 JavaThread "Unknown thread"             [_thread_in_vm, id=26612, stack(0x00000048e6f00000,0x00000048e7000000) (1024K)]
Total: 1

Other Threads:
  0x00000149aeb9c8d0 WatcherThread "VM Periodic Task Thread"        [id=22480, stack(0x00000048e7100000,0x00000048e7200000) (1024K)]
  0x000001498c23c5a0 WorkerThread "GC Thread#0"                     [id=17052, stack(0x00000048e7000000,0x00000048e7100000) (1024K)]
Total: 2

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000149af000000-0x00000149afba0000-0x00000149afba0000), size 12189696, SharedBaseAddress: 0x00000149af000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000149b0000000-0x00000149f0000000, reserved size: 1073741824
Narrow klass base: 0x00000149af000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 6 total, 6 available
 Memory: 15591M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 6

Heap:
 PSYoungGen      total 29696K, used 512K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 2% used [0x00000000d5580000,0x00000000d5600020,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 0K, committed 0K, reserved 1048576K
  class space    used 0K, committed 0K, reserved 1048576K

Card table byte_map: [0x00000149aa010000,0x00000149aa420000] _byte_map_base: 0x00000149a9c10000

Marking Bits: (ParMarkBitMap*) 0x00007ffce466a340
 Begin Bits: [0x00000149aa6d0000, 0x00000149ac6d0000)
 End Bits:   [0x00000149ac6d0000, 0x00000149ae6d0000)

Polling page: 0x000001499aaa0000

Metaspace:

Usage:
  Non-class:      0 bytes used.
      Class:      0 bytes used.
       Both:      0 bytes used.

Virtual space:
  Non-class space:        0 bytes reserved,       0 bytes (  ?%) committed,  0 nodes.
      Class space:        1.00 GB reserved,       0 bytes (  0%) committed,  1 nodes.
             Both:        1.00 GB reserved,       0 bytes (  0%) committed. 

Chunk freelists:
   Non-Class:  0 bytes
       Class:  16.00 MB
        Both:  16.00 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 0.
num_arena_deaths: 0.
num_vsnodes_births: 1.
num_vsnodes_deaths: 0.
num_space_committed: 0.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1.
num_chunk_merges: 0.
num_chunk_splits: 1.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x00000149a28f0000, 0x00000149a2b60000, 0x00000149a9e20000]
CodeHeap 'profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x000001499ae20000, 0x000001499b090000, 0x00000149a2350000]
CodeHeap 'non-nmethods': size=5760Kb used=194Kb max_used=342Kb free=5565Kb
 bounds [0x00000149a2350000, 0x00000149a25c0000, 0x00000149a28f0000]
CodeCache: size=245760Kb, used=194Kb, max_used=342Kb, free=245565Kb
 total_blobs=70, nmethods=0, adapters=48, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (1 events):
Event: 0.023 Thread 0x000001498c1fb2f0 Thread added: 0x000001498c1fb2f0


Dynamic libraries:
0x00007ff6b5a00000 - 0x00007ff6b5a0e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffd5a210000 - 0x00007ffd5a408000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd58ff0000 - 0x00007ffd590b2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd57d40000 - 0x00007ffd58036000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd3ce50000 - 0x00007ffd3ce67000 	C:\InetPub\ftproot\Tipray\Ldterm\ghijt64.DLL
0x00007ffd58d90000 - 0x00007ffd58e41000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd582d0000 - 0x00007ffd5836e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd58230000 - 0x00007ffd582cf000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd586e0000 - 0x00007ffd58803000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd57ae0000 - 0x00007ffd57b07000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffd57b10000 - 0x00007ffd57c10000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd43220000 - 0x00007ffd43238000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffd43c40000 - 0x00007ffd43c5e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffd58e50000 - 0x00007ffd58fed000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd58200000 - 0x00007ffd58222000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd5a0f0000 - 0x00007ffd5a11b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd4ebd0000 - 0x00007ffd4ee6a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffd580e0000 - 0x00007ffd581f9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd58040000 - 0x00007ffd580dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd3c3e0000 - 0x00007ffd3c665000 	C:\InetPub\ftproot\Tipray\Ldterm\LdHook64.dll
0x00007ffd59100000 - 0x00007ffd5922b000 	C:\WINDOWS\System32\ole32.dll
0x00007ffd58380000 - 0x00007ffd586d3000 	C:\WINDOWS\System32\combase.dll
0x00007ffd590f0000 - 0x00007ffd590f8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd4eab0000 - 0x00007ffd4eb54000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffd58870000 - 0x00007ffd5891d000 	C:\WINDOWS\System32\shcore.dll
0x00007ffd570f0000 - 0x00007ffd570fc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.DLL
0x00007ffd590c0000 - 0x00007ffd590ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd575b0000 - 0x00007ffd57626000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\Vozokopot.dll
0x00007ffd3c2f0000 - 0x00007ffd3c317000 	C:\Inetpub\ftproot\Tipray\LdTerm\ghhlp64.dll
0x00007ffd3c270000 - 0x00007ffd3c280000 	C:\InetPub\ftproot\Tipray\Ldterm\HookDataInteractionx64.dll
0x00007ffd3c0a0000 - 0x00007ffd3c113000 	C:\InetPub\ftproot\Tipray\Ldterm\LdSmartEnc64.dll
0x00007ffd42370000 - 0x00007ffd4237b000 	C:\WINDOWS\SYSTEM32\FLTLIB.DLL
0x00007ffd3c1b0000 - 0x00007ffd3c1ef000 	C:\Inetpub\ftproot\Tipray\LdTerm\LdUserInjectDll64.dll
0x00007ffd0fde0000 - 0x00007ffd101c9000 	C:\Inetpub\ftproot\Tipray\LdTerm\LdWaterMarkHook64.dll
0x00007ffd40d90000 - 0x00007ffd40f37000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.19041.5915_none_919facb6cc8c4195\gdiplus.dll
0x00007ffd50c10000 - 0x00007ffd50c1c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffd3f7d0000 - 0x00007ffd3f992000 	C:\Inetpub\ftproot\Tipray\LdTerm\LdPrintMonitor64.dll
0x00007ffd59ed0000 - 0x00007ffd59f3b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd2e5e0000 - 0x00007ffd2e66d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffce39b0000 - 0x00007ffce4747000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffd57650000 - 0x00007ffd5769b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd4b910000 - 0x00007ffd4b937000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd4de30000 - 0x00007ffd4de3a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd57630000 - 0x00007ffd57642000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd4f280000 - 0x00007ffd4f646000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathCore.dll
0x00007ffd59d00000 - 0x00007ffd59dda000 	C:\WINDOWS\System32\COMDLG32.dll
0x00007ffd59b20000 - 0x00007ffd59b7b000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffd511e0000 - 0x00007ffd511f4000 	C:\WINDOWS\SYSTEM32\WTSAPI32.dll
0x00007ffd593a0000 - 0x00007ffd59b0e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd4e770000 - 0x00007ffd4eaac000 	C:\WINDOWS\SYSTEM32\msi.dll
0x00007ffd59250000 - 0x00007ffd5931d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd4f0a0000 - 0x00007ffd4f0bd000 	C:\WINDOWS\SYSTEM32\MPR.dll
0x00007ffd56c00000 - 0x00007ffd56cca000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd56bc0000 - 0x00007ffd56bfb000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd59b10000 - 0x00007ffd59b18000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd4e200000 - 0x00007ffd4e209000 	C:\WINDOWS\SYSTEM32\wsock32.dll
0x00007ffd56090000 - 0x00007ffd560a2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd4e1d0000 - 0x00007ffd4e200000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathOM.dll
0x00007ffd4e060000 - 0x00007ffd4e067000 	C:\WINDOWS\SYSTEM32\MSIMG32.dll
0x00007ffd50690000 - 0x00007ffd5069a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffd4daa0000 - 0x00007ffd4dace000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathFw.dll
0x00007ffd553c0000 - 0x00007ffd555c1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd4e030000 - 0x00007ffd4e05d000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathNet.dll
0x00007ffd40d50000 - 0x00007ffd40d84000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd4de80000 - 0x00007ffd4def8000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathFile.dll
0x00007ffd57a50000 - 0x00007ffd57ad2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd4dd00000 - 0x00007ffd4de2e000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathProcess.dll
0x00007ffd4dc00000 - 0x00007ffd4dc58000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathBolo2.dll
0x00007ffd4de50000 - 0x00007ffd4de7b000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathProtect.dll
0x00007ffd50630000 - 0x00007ffd5063f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffd43150000 - 0x00007ffd4316f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffd4de40000 - 0x00007ffd4de47000 	C:\WINDOWS\LVUAAgentInstBaseRoot\system32\MozartBreathManifest.dll

JVMTI agents:
c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:none, loaded, not initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialization error.

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.44.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-05805b35c368a5bf459667dba4f27621-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\8a503447d2c280d9ba6c778ce417e96a\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\jdk1.8.0_151
PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;D:\Program Files\Java\jdk1.8.0_151\bin;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;D:\Program Files\nodejs\;D:\Program Files\MATLAB\R2023b\runtime\win64;D:\Program Files\MATLAB\R2023b\bin;D:\Program Files\Java\jdk1.8.0_151\jre\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;E:\Programs\cursor\resources\app\bin;D:\software\apache-maven-3.9.9-bin\apache-maven-3.9.9\bin;D:\software\Programs\Kiro\bin;D:\Programs\Microsoft VS Code\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 23 days 22:32 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2375, Current Mhz: 2375, Mhz Limit: 2375

Memory: 4k page, system-wide physical 15591M (1587M free)
TotalPageFile size 38375M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 33M, peak: 33M
current process commit charge ("private bytes"): 202M, peak: 203M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
