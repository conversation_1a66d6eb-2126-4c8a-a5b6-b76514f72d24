<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sprixin.settle.dao.TradeTjAllSettleItemLabelMapper">
    <resultMap id="BaseResultMap" type="com.sprixin.settle.entity.TradeTjAllSettleItemLabel">
        <id property="id" column="id" />
        <result property="provinceId" column="province_id" />
        <result property="code" column="code" />
        <result property="name" column="name" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>



    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM trade_tj_all_settle_item_label ORDER BY province_id, code
    </select>



    <insert id="replaceBatch" parameterType="java.util.List">
        REPLACE INTO trade_tj_all_settle_item_label (province_id, code, name, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.provinceId}, #{item.code}, #{item.name}, NOW(), NOW())
        </foreach>
    </insert>
</mapper>